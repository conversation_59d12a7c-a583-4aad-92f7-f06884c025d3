FROM 342743391564.dkr.ecr.us-east-1.amazonaws.com/php-8-1:latest

# Maintainer
LABEL author="<PERSON> <<EMAIL>>"

# Set workdir
WORKDIR /var/www
RUN rm  -rf /var/www/html

# Install SSH client
RUN apk update && \
    apk add openssh && \
    rm -rf /var/cache/apk/*

# Install and enable the PostgreSQL extensions
RUN docker-php-ext-install pdo_pgsql pdo_mysql pgsql

# Generate host keys
RUN ssh-keygen -A

# Copy SSH key into the image
COPY ./.deploy/ssh/id_rsa /root/.ssh/id_rsa
RUN chmod 600 /root/.ssh/id_rsa

# Set up SSH config
RUN echo "Host gitlabcode.uplexis.com.br\n\tStrictHostKeyChecking no\n" >> /root/.ssh/config
RUN chmod 600 /root/.ssh/config

RUN ssh-keyscan gitlabcode.uplexis.com.br >> /root/.ssh/known_hosts
RUN chmod 600 /root/.ssh/known_hosts

#Configura nginx
COPY ./.deploy/000-default-hml.conf /etc/nginx/http.d/default.conf
COPY ./.deploy/ssl /etc/nginx/ssl/

RUN echo "max_input_vars = 5000" >> "$PHP_INI_DIR/php.ini"

# Add files of project to image
COPY . .

RUN composer self-update --snapshot
RUN composer install --optimize-autoloader --no-dev

#RUN npm install

# Deploy project
RUN cp .env.hml .env

RUN chown -R www-data:www-data .
RUN chmod 777 -R ./storage/

RUN php artisan optimize

RUN composer dumpautoload

# Init entrypoint (nginx and php-fpm)
RUN ["chmod", "+x", "./entrypoint.sh"]
RUN ["chmod", "+x", "./installpackages.sh"]

RUN ["./installpackages.sh", "homologation"]

CMD ["./entrypoint.sh"]
