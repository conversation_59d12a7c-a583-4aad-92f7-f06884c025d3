<?php
namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Response;
use Throwable;
use App\Exceptions\CustomException;
use Exception;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Lang;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        ValidationException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function getTypeMap(): array
    {
        return [
            'except' => [
                'class' => [
                    'ValidationException',
                ],
                'statusCode' => [
                    403,
                    422,
                    401,
                ],
            ],
        ];
    }

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @param  Throwable  $e
     * @return void
     */
    public function report(Throwable $e)
    {
        $e = $this->mapException($e);

        if ($this->shouldntReport($e)) {
            return;
        }

        if ($e->getMessage() === Lang::get('authorization.not-logged-user')) {
            return;
        }
        // if ($this->shouldReport($e) && App::environment(['production', 'qa'])) {
            $airbrakeNotifier = App::make('Airbrake\Notifier');
            $airbrakeNotifier->notify($e);
        // }
        parent::report($e);
    }

    public function register()
    {
        $this->renderable(function (Exception $exception) {
            if ($exception instanceof CustomException) {
                return $exception->render($request);
            }

            $map = $this->getTypeMap();
            if (
                method_exists($exception, 'getPrevious')
                && !empty($exception->getPrevious())
            ) {
                $exception = $exception->getPrevious();
            }

            $error =  $exception->getMessage();
            $code = $this->getCodeHttp($exception->getCode());

            if (in_array(env('APP_ENV'),['prod'])) {
                $error =  trans('error.error-generic');
                $code = Response::HTTP_INTERNAL_SERVER_ERROR;
            }

            if (
                in_array(
                    class_basename($exception),
                    $map['except']['class']
                )
            ) {
                $error = $exception->getMessage();
                $code = $this->getCodeHttp($exception->getCode());
            }

            if (
                method_exists($exception, 'getStatusCode')
                && in_array(
                    $exception->getStatusCode(),
                    $map['except']['statusCode']
                )
            ) {
                $error = $exception->getMessage();
                $code = $exception->getStatusCode();
            }

            if ($exception instanceof ValidationException) {
                $error = $exception->validator->errors()->first();
            }

            $response = [
                'message' => $error,
                'status' => 'error'
            ];

            if (env('APP_ENV') != 'prod') {
                $response['file'] = $exception->getFile();
                $response['line'] = $exception->getLine();
            }

            return response()->json($response, $code);
        });

    }

    public function getCodeHttp($code)
    {
         return array_key_exists($code, Response::$statusTexts)
                ? $code : 422;
    }
}
