<?php

namespace App\Domains\Monitoria\Repositories;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Monitoria\Entity\MonitoriaDossie;
use Illuminate\Support\Collection;

class MonitoriaDossieRepository
{
    public function __construct(
        private MonitoriaDossie $monitoriaDossie,
        private PesquisaConsulta $pesquisaConsulta
    ) {}

    public function getById(int $id): ?MonitoriaDossie
    {
        return $this->monitoriaDossie->findOrFail($id);
    }

    public function getByLoteId(int $loteId): ?MonitoriaDossie
    {
        return $this->monitoriaDossie->where('id_lote', $loteId)->first();
    }

    public function getByPerfilId(int $perfilId): ?MonitoriaDossie
    {
        return $this->monitoriaDossie->where('id_perfil', $perfilId)->first();
    }

    public function getBatchsMonitoring(int $perfilId): Collection
    {
        $batches = $this->monitoriaDossie
            ->join('lote_consulta as lc', 'monitoria_dossie.id_lote', '=', 'lc.id')
            ->where('lc.id_consulta', '=', $perfilId)
            ->where('monitoria_dossie.ativo', true)
            ->get();

        return $this->pesquisaConsulta->whereIn('id_lote_consulta', $batches->pluck('id_lote'))
            ->where('id_pesquisa_consulta', null)->get();
    }

    public function create(array $params): MonitoriaDossie
    {
        return $this->monitoriaDossie->create($params);
    }

    public function update(int $id, array $params): bool
    {
        $monitoring = $this->monitoriaDossie->findOrFail($id);

        return $monitoring->update($params);
    }

    public function updateByColumn(string $columnName, string $columnValue, array $data): bool
    {
        $consulta = $this->monitoriaDossie
            ->where($columnName, $columnValue)
            ->firstOrFail();

        return $consulta->update($data);
    }
}
