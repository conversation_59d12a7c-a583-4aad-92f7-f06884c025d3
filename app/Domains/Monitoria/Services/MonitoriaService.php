<?php

namespace App\Domains\Monitoria\Services;

use App\Domains\Dossie\Repositories\LoteConsultaRepository;
use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;
use App\Domains\Dossie\Repositories\ProfileConsultationRepository;
use App\Domains\Monitoria\Repositories\MonitoriaDossieAgendadaRepository;
use App\Domains\Monitoria\Repositories\MonitoriaDossieRepository;
use App\Exceptions\InvalidBatchForMonitoriaException;
use Carbon\Carbon;
use App\Exceptions\CustomException;

class MonitoriaService
{
    public function __construct(
        private MonitoriaDossieRepository $monitoriaDossieRepository,
        private MonitoriaDossieAgendadaRepository $monitoriaDossieAgendadaRepository,
        private LoteConsultaRepository $loteConsultaRepository,
        private ProfileConsultationRepository $profileConsultationRepository,
        private PesquisaConsultaRepository $pesquisaConsultaRepository
    )
    {}

    public function getById(int $monitoringId)
    {
        return $this->monitoriaDossieRepository->getById($monitoringId);
    }

    public function getByLoteId(int $loteId)
    {
        return $this->monitoriaDossieRepository->getByLoteId($loteId);
    }

    public function getByPerfilId(int $perfilId): array
    {
        return $this->monitoriaDossieRepository->getByPerfilId($perfilId)->toArray();
    }

    public function getByIds(array $ids): array
    {
        return $this->monitoriaDossieRepository->getByIds($ids)->toArray();
    }

    public function create(array $params): int
    {
        $monitoringParams = [];
        $monitoringParams['id_lote'] = $params['loteId'];
        $monitoringParams['id_usuario'] = $params['id_usuario'];
        $monitoringParams['nome_monitoria'] = $params['name'];
        $monitoringParams['data_inicial'] = date('Y-m-d');
        $monitoringParams['periodicidade'] = $params['periodicity'];
        $monitoringParams['ativo'] = true;
        $monitoringParams['status'] = 1;
        $monitoringParams['id_grupo'] = $params['id_grupo'];
        $monitoringParams['id_perfil'] = $params['id_perfil'];
        $monitoringParams['perfil'] = $params['perfil'];
        $monitoringParams['nome_usuario'] = $params['nome_usuario'];
        $monitoringParams['created_at'] = date('Y-m-d');

        if (isset($params['end']) && !empty($params['end'])) {
            $monitoringParams['data_final'] = $params['end'];
        }

        $newMonitoring = $this->monitoriaDossieRepository->create($monitoringParams);
        $this->createSchedule($params, $newMonitoring->id);

        return $newMonitoring->id;
    }

    public function createSchedule(array $params, int $monitoringId): void
    {
        $today = date('Y-m-d');

        $scheduleParams = [];
        $scheduleParams['id_monitoria'] = $monitoringId;
        $scheduleParams['client'] = $params['client'];
        $scheduleParams['proxima_execucao'] = $this->getNextExecutionDate($today, $params['periodicity']);
        $scheduleParams['created_at'] = $today;

        $this->monitoriaDossieAgendadaRepository->create($scheduleParams);
    }

    public function createMonitoringFromDossiesTab(int $loteId, array $params): int
    {
        $session = (object) session()->all();
        $batchInfo = $this->loteConsultaRepository->getByID($loteId);

        if ($batchInfo->id_consulta_perfil_default) {
            throw new InvalidBatchForMonitoriaException(
                'Não é possível criar monitoria em lotes com perfil de consulta padrão'
            );
        }

        $params['id_usuario'] = $session->setup['id_usuario'];
        $params['client'] = $session->company;
        $params['id_perfil'] = $batchInfo['id_consulta'];
        $params['perfil'] = $this->profileConsultationRepository
            ->setMode('custom')
            ->getByID($batchInfo->id_consulta)['nome'];
        $params['id_grupo'] = $session->setup['grupo_usuario'];
        $params['nome_usuario'] = $session->setup['nome_usuario'] . " " . $session->setup['sobrenome_usuario'];

        $monitoringBatch = $this->monitoriaDossieRepository?->getByLoteId($loteId)?->id;
        if ($monitoringBatch != null) {
            throw new InvalidBatchForMonitoriaException("Monitoria já existe para o lote $loteId!");
        }

        $monitoringId = $this->create(array_merge(['loteId' => $loteId], $params));

        $this->loteConsultaRepository->update($loteId, [
            'monitoria' => true,
            'monitoria_principal' => true,
            'id_monitoria_dossie' => $monitoringId,
        ]);

        $this->pesquisaConsultaRepository->updateByColumn(
            'id_lote_consulta',
            $loteId,
            [
                'id_monitoria' => $monitoringId,
                'monitoria_diferenca' => false,
                'monitoria_diferenca_verificada' => false,
            ]
        );

        return $monitoringId;
    }

    public function updateMonitoringFromDossiesTab(int $loteId, array $params): bool
    {
        $updateParams = [
            'nome_monitoria' => $params['name'],
            'data_final' => $params['end'],
            'periodicidade' => $params['periodicity'],
        ];

        return $this->monitoriaDossieRepository->updateByColumn('id_lote', $loteId, $updateParams);
    }

    private function getNextExecutionDate(string $startDate, int $periodicity = 7): string
    {
        $startDate = Carbon::parse($startDate);
        $company = session('company');

        switch ($periodicity) {
            case '1' : // diário
                if ($company == 'uplexis') {
                    $executionDate = $startDate->addDay();
                    break;
                }
                //Se por algum motivo caiu aqui, deixar ir para 7 dias.
            case '7'://semanal
                $executionDate = $startDate->addDays(7);
                break;
            case '30'://mensal
                $executionDate = $startDate->addMonth();
                break;
            case '90'://trimestral
                $executionDate = $startDate->addMonths(3);
                break;
            case '180'://semestral
                $executionDate = $startDate->addMonths(6);
                break;
            case '360': //anual
                $executionDate = $startDate->addYear();
                break;
            default:
                throw new CustomException('Periodicidade inválida');
        }

        return $executionDate->format('Y-m-d');
    }

    public function stopMonitoringByLoteId(int $loteId): bool
    {
        $this->monitoriaDossieRepository->updateByColumn('id_lote', $loteId, ['status' => 0]);

        $monitoria = $this->monitoriaDossieRepository->getByLoteId($loteId);

        $session = (object) session()->all();
        $company = $session->company;

        $monitoriaAgendada = $this->monitoriaDossieAgendadaRepository
            ->getByCompanyAndMonitoringId($company, $monitoria->id);

        return $this->monitoriaDossieAgendadaRepository->update($monitoriaAgendada->id, ['monitoria_pausada' => true]);
    }

    public function restartMonitoringByLoteId(int $loteId): bool
    {
        $this->monitoriaDossieRepository->updateByColumn('id_lote', $loteId, ['status' => 1]);

        $monitoria = $this->monitoriaDossieRepository->getByLoteId($loteId);

        $session = (object) session()->all();
        $company = $session->company;

        $monitoriaAgendada = $this->monitoriaDossieAgendadaRepository
            ->getByCompanyAndMonitoringId($company, $monitoria->id);

        return $this->monitoriaDossieAgendadaRepository->update($monitoriaAgendada->id, ['monitoria_pausada' => false]);
    }
}
