<?php

declare(strict_types=1);

namespace App\Domains\Workflow\Entities\Client;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowAnaliseDossie extends Model
{
    protected $table = 'workflow_analise_dossie';

    protected $primaryKey = 'id';

    public $timestamps = false;

    protected $fillable = [
        'id_pesquisa_consulta',
        'status_dossie_workflow',
        'data_analise',
        'aprovado_automatico',
    ];

    public function dossie(): BelongsTo
    {
        return $this->belongsTo(PesquisaConsulta::class, 'id_pesquisa_consulta', 'id');
    }

    /**
     * Checa se o status do workflow está vazio.
     * Recebe uma flag que indica se o workflow está
     * 'em análise' por erro em uma ou mais capturas
     *
     * <AUTHOR> - 17/05/2019 - @since 1.0.0
     *
     * @version 1.0.0
     */
    public function updateEmptyStatus($inAnalysisByError = false): void
    {
        if ($inAnalysisByError && empty($this->status_dossie_workflow)) {
            $this->status_dossie_workflow = Workflow::IN_ANALYSIS;
        }
    }
}
