<?php

declare(strict_types=1);

namespace App\Domains\Workflow\Entities\Client;

use Illuminate\Database\Eloquent\Model;

use App\Domains\User\Entities\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Domains\Workflow\Entities\Client\WorkflowAprovadores;
use App\Domains\Workflow\Entities\Client\WorkflowAprovacaoAutomatica;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Comment\Entities\Client\DossieCommentsOrigem;

class Workflow extends Model
{
    const IN_ANALYSIS = 'em_analise';
    const APPROVED = 'aprovado';
    const REPROVED = 'reprovado';
    const NO_WORKFLOW = 'sem-workflow';

    protected $table = 'workflow';

    public $timestamps = false;

    protected $guarded = [];
    protected $attributes = [
        'nome' => '',
    ];
    public function dossie(): HasMany
    {
        return $this->hasMany(PesquisaConsulta::class, 'id_workflow', 'id');
    }

    public function aprovadores(): HasMany
    {
        return $this->hasMany(WorkflowAprovadores::class, 'id_workflow', 'id');
    }

    public function responsavel(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_usuario_responsavel', 'id');
    }

    public function aprovacaoAutomatica(): HasMany
    {
        return $this->hasMany(WorkflowAprovacaoAutomatica::class, 'id_workflow', 'id');
    }

    public function comments(): HasMany
    {
        return $this->hasMany(DossieCommentsOrigem::class, 'workflow_id', 'id')
        ->whereNull('captura_id')
        ->whereNull('dossie_id');
    }
}
