<?php

declare(strict_types=1);

namespace App\Domains\Workflow\Entities\Client;

use Illuminate\Database\Eloquent\Model;
use App\Domains\User\Entities\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use DateTimeInterface;
use App\Domains\Workflow\Entities\Client\PesquisaConsultaComentarios;
use App\Domains\Workflow\Entities\Client\WorkflowAprovadores;

class WorkflowPesquisaConsulta extends Model
{
    protected $table = 'workflow_pesquisa_consulta';

    protected $fillable = [
        'id_master',
        'id_pesquisa_consulta',
        'status',
        'data_analise',
        'id_workflow_aprovadores',
    ];
    
    protected $casts = [
        'data_analise' => 'date',
    ];

    public $timestamps = false;
    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('d/m/Y');
    }
    public function aprovadorMaster(): BelongsTo
    {
        return $this->belongsTo(User::class, 'id_master', 'id')->select('id', 'nome', 'sobrenome', 'imagem_perfil', 'email');
    }

    public function reason()
    {
        return $this->hasOne(PesquisaConsultaComentarios::class, 'id_workflow_pesquisa_consulta', 'id');
    }

    public function aprovador(): BelongsTo
    {
        return $this->belongsTo(WorkflowAprovadores::class, 'id_workflow_aprovadores', 'id')->select('id', 'id_usuario');
    }
}
