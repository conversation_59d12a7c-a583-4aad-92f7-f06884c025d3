<?php

namespace App\Domains\Workflow\Services;

use App\Actions\Clients\Gupy\GupyIntegration;
use App\Domains\AuroraLog\Services\AuroraLogService;
use App\Domains\Dossie\Entities\Seguranca\Captura;
use App\Domains\Dossie\Repositories\GrupoRepository;
use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;
use App\Domains\Dossie\Repositories\ProfileConsultationRepository;
use App\Domains\User\Repositories\UserRepository;
use App\Domains\Workflow\Actions\ProcessNewAprovacaoAutomaticaWorkflowAction;
use App\Domains\Workflow\Actions\ProcessNewAprovadoresWorkflowAction;
use App\Domains\Workflow\Entities\Client\WorkflowAprovadores;
use App\Domains\Workflow\Helpers\CardDeatailsWorkflowHelper;
use App\Domains\Workflow\Helpers\FinalAnalysisWorkflowHelper;
use App\Domains\Workflow\Helpers\UserWithNivelCanMakeApproveHelper;
use App\Domains\Workflow\Helpers\WorkflowApproveHelper;
use App\Domains\Workflow\Repositories\WorkflowAnaliseDossieRepository;
use App\Domains\Workflow\Repositories\WorkflowAprovacaoAutomaticaRepository;
use App\Domains\Workflow\Repositories\WorkflowAprovadoresRepository;
use App\Domains\Workflow\Repositories\WorkflowPesquisaConsultaRepository;
use App\Domains\Workflow\Repositories\WorkflowRepository;
use App\Domains\Workflow\Transforms\TransformAnalysisWorkflow;
use App\Domains\Workflow\Transforms\TransformAnalysisWorkflowAuto;
use App\Domains\Workflow\Transforms\TransformCardSimpleWorkflow;
use App\Exceptions\CustomException;
use App\Exceptions\ThereIsAnAnalysisForThisDossierException;
use DB;
use Exception;

class WorkflowService
{
    private PesquisaConsultaRepository $pesquisaConsultaRepository;
    private WorkflowPesquisaConsultaRepository $workflowPesquisaConsultaRepository;
    private TransformAnalysisWorkflow $transformAnalysis;
    private TransformAnalysisWorkflowAuto $transformAnalysisAuto;
    private WorkflowAnaliseDossieRepository $workflowAnaliseDossieRepository;
    private WorkflowRepository $workflowRepository;
    private TransformCardSimpleWorkflow $transformCardSimpleWorkflow;
    private CardDeatailsWorkflowHelper $cardDeatailsWorkflowHelper;
    private WorkflowApproveHelper $workflowApproveHelper;
    private UserWithNivelCanMakeApproveHelper $userWithNivelCanMakeApproveHelper;
    private FinalAnalysisWorkflowHelper $finalAnalysisWorkflowHelper;
    private GupyIntegration $gupyIntegration;
    private ProfileConsultationRepository $profileConsultationRepository;
    private WorkflowAprovadoresRepository $workflowAprovadoresRepository;
    private WorkflowAprovacaoAutomaticaRepository $workflowAprovacaoAutomaticaRepository;
    private UserRepository $userRepository;
    private AuroraLogService $auroraLogService;

    public function __construct(
        PesquisaConsultaRepository $pesquisaConsultaRepository,
        WorkflowPesquisaConsultaRepository $workflowPesquisaConsultaRepository,
        TransformAnalysisWorkflowAuto $transformAnalysisAuto,
        TransformAnalysisWorkflow $transformAnalysis,
        WorkflowAnaliseDossieRepository $workflowAnaliseDossieRepository,
        WorkflowRepository $workflowRepository,
        TransformCardSimpleWorkflow $transformCardSimpleWorkflow,
        CardDeatailsWorkflowHelper $cardDeatailsWorkflowHelper,
        WorkflowApproveHelper $workflowApproveHelper,
        UserWithNivelCanMakeApproveHelper $userWithNivelCanMakeApproveHelper,
        FinalAnalysisWorkflowHelper $finalAnalysisWorkflowHelper,
        GupyIntegration $gupyIntegration,
        ProfileConsultationRepository $profileConsultationRepository,
        WorkflowAprovadoresRepository $workflowAprovadoresRepository,
        WorkflowAprovacaoAutomaticaRepository $workflowAprovacaoAutomaticaRepository,
        GrupoRepository $grupoRepository,
        UserRepository $userRepository,
        AuroraLogService $auroraLogService
    ) {
        $this->pesquisaConsultaRepository = $pesquisaConsultaRepository;
        $this->workflowPesquisaConsultaRepository = $workflowPesquisaConsultaRepository;
        $this->workflowAnaliseDossieRepository = $workflowAnaliseDossieRepository;
        $this->transformAnalysisAuto = $transformAnalysisAuto;
        $this->transformAnalysis = $transformAnalysis;
        $this->workflowRepository = $workflowRepository;
        $this->transformCardSimpleWorkflow = $transformCardSimpleWorkflow;
        $this->cardDeatailsWorkflowHelper = $cardDeatailsWorkflowHelper;
        $this->workflowApproveHelper = $workflowApproveHelper;
        $this->userWithNivelCanMakeApproveHelper = $userWithNivelCanMakeApproveHelper;
        $this->finalAnalysisWorkflowHelper = $finalAnalysisWorkflowHelper;
        $this->gupyIntegration = $gupyIntegration;
        $this->profileConsultationRepository = $profileConsultationRepository;
        $this->workflowAprovadoresRepository = $workflowAprovadoresRepository;
        $this->workflowAprovacaoAutomaticaRepository = $workflowAprovacaoAutomaticaRepository;
        $this->grupoRepository = $grupoRepository;
        $this->userRepository = $userRepository;
        $this->auroraLogService = $auroraLogService;
    }

    public function getWorkflowByPerfilId(int $perfilId, array $params): array
    {
        $workflow = $this->profileConsultationRepository->getWorkflowByPerfilId($perfilId, $params);

        if (!$workflow) {
            throw new CustomException(trans('workflow.not-found'));
        }
        unset($workflow->id);
        $aprovadores = $this->workflowAprovadoresRepository->getWithUsuarioAprovadorById($workflow->id_workflow, $workflow->id_versao_workflow);
        $aprovacaoAutomatica = $this->workflowAprovacaoAutomaticaRepository->getByWorkflowVersaoId($workflow->id_workflow, $workflow->id_versao_workflow);
        $sourcesProfile = $workflow->getConsulta->pluck('saida');
        foreach ($aprovacaoAutomatica as $key => $value) {
            if ($sourcesProfile->doesntContain($value->id_captura)) {
                unset($aprovacaoAutomatica[$key]);
                continue;
            }
            $aprovacaoAutomatica[$key]->palavras = empty(json_decode($value->palavras)) ? '[]' : $value->palavras;
        }

        $response = [
            'levels' => [],
            'automaticApprove' => $aprovacaoAutomatica->values(),
            'id_workflow' => $workflow->id_workflow,
            'id_versao_workflow' => $workflow->id_versao_workflow,
            'created_at' => $workflow->data_cadastro_workflow,
        ];

        foreach ($aprovadores as $aprovador) {
            if (!array_key_exists($aprovador->nivel, $response['levels'])) {
                $response['levels'][$aprovador->nivel] = [];
            }

            $response['levels'][$aprovador->nivel][] = [
                'id' => $aprovador->aprovador->id,
                'name' => $aprovador->aprovador->nome,
                'surname' => $aprovador->aprovador->sobrenome,
                'fullname' => trim($aprovador->aprovador->nome
                    . " " . $aprovador->aprovador->sobrenome),
                'email' => $aprovador->aprovador->email
            ];
        }

        return $response;
    }

    public function getCardDetailsWorkflow(int $id): array
    {
        $dossie = $this->pesquisaConsultaRepository->getByID($id)->toArray();
        if (empty($dossie['id_workflow'])) {
            return $this->mountVoid();
        }

        $workflow = $this->workflowRepository->getWorkflowDetails(
            $id,
            $dossie['id_versao_workflow'],
            $dossie['id_workflow']
        );

        $workflow['analiseDossie'] = $this->workflowAnaliseDossieRepository->getByID($id);

        $workflow['aprovadoAutomatico'] = $this->pesquisaConsultaRepository->hasAppoveAutomatic($id);
        $workflow['workflowPesquisaDossie'] = $this->workflowPesquisaConsultaRepository->analiseManual($id);

        return $this->cardDeatailsWorkflowHelper->mountCardDeatailsWorkflow(
            $workflow,
            session('setup.id_usuario'),
            session('master')
        );
    }

    public function getCardSimpleWorkflow(int $id): array
    {
        $dossie = $this->pesquisaConsultaRepository->getByID($id)->toArray();
        if (empty($dossie['id_workflow'])) {
            return $this->mountVoid();
        }

        $workflow = $this->workflowRepository->getWorkflow(
            $dossie['id_versao_workflow'],
            $dossie['id_workflow']
        );

        $workflow['analiseDossie'] = $this->workflowAnaliseDossieRepository->getByID($id);

        $workflow['aprovadoAutomatico'] = $this->pesquisaConsultaRepository->hasAppoveAutomatic($id);
        $workflow['workflowPesquisaDossie'] = $this->workflowPesquisaConsultaRepository->analiseManual($id);

        return $this->transformCardSimpleWorkflow->handle($workflow);
    }

    public function addApprove(int $id, $params):bool|array|Exception
    {
        $dossie = $this->pesquisaConsultaRepository->getByID($id)->toArray();
        if (empty($dossie['id_workflow'])) {
            return false;
        }

        if (!empty($dossie['workflow_analise']['status_dossie_workflow'])
            && $dossie['workflow_analise']['status_dossie_workflow'] != 'em_analise'
        ) {
            throw new ThereIsAnAnalysisForThisDossierException(trans('error.error-existe-analise-workflow'), 1);
        }

        // se não existe analise para esse nivel retorna um aprovadorId se não lança exception
        $approveID = $this->userWithNivelCanMakeApproveHelper->existsApprove(
            $params['nivel'],
            session('setup.id_usuario'),
            $dossie
        );
        try {
            DB::beginTransaction();

            // Send Gupy
            $this->gupyIntegration->sendWorkflow(
                $id,
                $params['status'],
                session('setup.id_master'),
                $params['motivo'] ?? ''
            );

            $masterID = $this->workflowApproveHelper->getMaster(
                session('setup.id_usuario'),
                session('master'),
                $dossie['id_workflow'],
                $dossie['id_versao_workflow'],
                $approveID,
                $id
            );

            // //Adiciona nova análise e comentario
            $workflowPesquisaID = $this->workflowPesquisaConsultaRepository->insert(
                $id,
                $masterID,
                $approveID,
                $params['status']
            );
            if (!empty($params['motivo'])) {
                $this->workflowRepository->setAnalysisManual(
                    [
                        'id_pesquisa_consulta' => $id,
                        'id_usuario' => session('setup.id_usuario'),
                        'id_workflow_pesquisa_consulta' => $workflowPesquisaID,
                    ],
                    [
                        'comentario' => $params['motivo'],
                        'data_inclusao' => date('Y-m-d H:i:s'),
                        'nome_usuario' => session('setup.nome_usuario').' '.session('setup.sobrenome_usuario'),
                    ]
                );
             }

            $this->finalAnalysisWorkflowHelper->setAnalise(
                $this->workflowAnaliseDossieRepository->getByID($id),
                $dossie,
                $params,
            );
            DB::commit();
            return $this->getAnalysis($id);
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    private function mountVoid():array
    {
        return [
            'status' => 'sem-workflow',
            'visible' => false,
        ];
    }

    public function getAnalysis(int $dossieId): array
    {
       $analysisAuto = $this->transformAnalysisAuto->handle(
            $this->workflowRepository->getAnalysisAuto($dossieId)->unique('comentario')
        );
        $analysis =  $this->transformAnalysis->handle(
            $this->workflowPesquisaConsultaRepository->getAnalysisManual($dossieId),
            $analysisAuto
        );

        return $analysis->sortByDesc('data')->values()->toArray();
    }

    public function create(int $perfilId, array $params): array
    {
        $session = (object)session()->all();
        $cliente = $session->company;
        $idUsuarioLogado = $session->setup['id_usuario'];
        $filtros = [];
        $capturas = [];
        $status = [];

        if (!empty($params['parametros_aprovacao'])) {
            foreach ($params['parametros_aprovacao'] as $index => $value) {
                if (!empty($value['palavras'])) {
                    $palavras = [];
                    foreach ($value['palavras'] as $palavra) {
                        $palavras[] = str_replace(['"', "'"], '', $palavra);
                    }
                    $params['parametros_aprovacao'][$index]['palavras'] = $palavras;
                }
            }
        }

        $consulta = $this->profileConsultationRepository->setMode('custom')->getByID($perfilId);

        if (!$consulta) {
            throw new CustomException(trans('profile.not-found', ['id' => $perfilId]), 1);
        }

        $niveisWorkflow = ($this->checkWorkflowHasLevelZero($params['niveis_workflow'])) ?
            array_slice($params['niveis_workflow'], 1) : $params['niveis_workflow'];

        $countNiveis = count($niveisWorkflow);
        $qntNiveisWorkflow = max([(int)$params['qt_niveis'], $countNiveis]);
        $idGrupoConsulta = $params['id_grupo_consulta'];

        $aprovacaoAutomatica = isset($params['aprovacao_automatica']) ? $params['aprovacao_automatica'] : false;
        $parametrosAprovacao = isset($params['parametros_aprovacao']) ? $params['parametros_aprovacao'] : false;

        $workflowAtual = $this->grupoRepository->getGrupoConsultaById($idGrupoConsulta, $perfilId)->toArray();

        $ultimoIdWorkflow = $workflowAtual['id_workflow'];

        if (empty($ultimoIdWorkflow)) {
            $ultimoWorkflow = $this->workflowRepository->getLastId()->toArray();

            $ultimoIdWorkflow = 1;
            if (!empty($ultimoWorkflow['ultimo_id'])) {
                $ultimoIdWorkflow = $ultimoWorkflow['ultimo_id'] + 1;
            }

            $ultimaVersaoWorkflow = 1;

        } else {
            $ultimaVersaoWorkflow = $this->workflowRepository->getLastVersion($ultimoIdWorkflow);
            $ultimaVersaoWorkflow = $ultimaVersaoWorkflow['ultima_versao'] + 1;
        }

        $workflowParams = [
            'id' => $ultimoIdWorkflow,
            'id_versao' => $ultimaVersaoWorkflow,
            'id_usuario_responsavel' => $idUsuarioLogado,
            'qt_niveis' => $qntNiveisWorkflow
        ];

        $workflow = $this->workflowRepository->create($workflowParams);

        //DIFF CASO SEJA UPDATE
        if ($workflow->id_versao != 1) {
            $diffFields = ['diff' => [], 'add' => [], 'del' => []];

            $capturas = Captura::select('id', 'nome')->get()->keyBy('id')->toArray();
            $filtros = [
                '' => 'Sem Filtro',
                'palavra' => 'Palavras',
                'sem-palavra' => 'Sem Palavras',
                'consta' => 'Possui resultado',
                'nao-consta' => 'Não possui resultado',
            ];

            $status = [
                '' => 'Sem Status',
                'reprovar' => 'Reprovar',
                'em_analise' => 'Em análise',
            ];

            $workflowAtual = $this->workflowRepository->getWorkflow(
                $workflowAtual['id_versao_workflow'],
                $workflowAtual['id_workflow']
            );

            //APROVAÇÕES ANTIGAS
            $oldAuto = [];
            $oAuto = $this->workflowAprovacaoAutomaticaRepository->getWorkflowAprovacaoAutomatica(
                $workflowAtual['id'],
                $workflowAtual['id_versao']
            );

            foreach ($oAuto as $v) {
                $oldAuto["{$v->id_captura}_{$v->tipo}_{$v->status}"] = $v;
            }
        }

        $newAuto = [];
        if ($aprovacaoAutomatica) {
            $logAdd = app(ProcessNewAprovacaoAutomaticaWorkflowAction::class)->execute(
                $workflow,
                $parametrosAprovacao,
                $ultimoIdWorkflow,
                $ultimaVersaoWorkflow,
                $capturas,
                $filtros,
                $status
            );

            if (!empty($logAdd['add'])) {
                $diffFields = $this->updateLog($diffFields, $logAdd, 'diff');
            }
        }

        //DIFF CASO SEJA UPDATE
        if ($workflow->id_versao != 1) {
            //VERIFICA APROVAÇÃO REMOVIDAS
            foreach ($oldAuto as $key => $value) {
                if (!isset($newAuto[$key])) {
                    $diffFields = $this->updateLog(
                        $diffFields,
                        [
                            'label' => 'Aprovação Automática',
                            'value' => $capturas[$value->id_captura]['nome'] ?? $value->id_captura
                                . "{$filtros[$value->tipo]} - "
                                . "{$status[$value->status]}",
                            'relation' => 'workflow_aprovacao_automatica',
                            'data' => $value
                        ],
                        'del'
                    );
                }
            }

            //APROVADORES ANTIGOS
            $oldApprovers = [];
            $oAuto = WorkflowAprovadores::select()->where([
                ['id_workflow', '=', $workflowAtual['id']],
                ['id_versao_workflow', '=', $workflowAtual['id_versao']],
            ])->get();

            foreach ($oAuto as $v) {
                $oldApprovers["{$v->id_usuario}_{$v->nivel}"] = $v;
                $user = $this->userRepository->getUserById($v['id_usuario'])->toArray();
                $oldApprovers["{$v->id_usuario}_{$v->nivel}"]->name = "{$user['nome']} {$user['sobrenome']}";
            }
        }

        //Adiciona aprovadores do workflow
        [$id_aprovadores, $logAdd] = app(ProcessNewAprovadoresWorkflowAction::class)->execute(
            $workflow,
            $niveisWorkflow,
            $cliente,
            $ultimoIdWorkflow,
            $ultimaVersaoWorkflow
        );

        if (!empty($logAdd['add'])) {
            $diffFields = $this->updateLog($diffFields, $logAdd, 'add');
        }

        //Se não foi adicionado nenhum aprovador, então deleta o workflow
        if (count($id_aprovadores) === 0) {
            $this->workflowRepository->delete($ultimoIdWorkflow);

            throw new CustomException(trans('workflow.minimal-approver'), 1);
        }

        //VERIFICA APROVADORES REMOVIDOS
        if ($workflow->id_versao != 1) {
            foreach ($oldApprovers as $key => $value) {
                if (!isset($newApprovers[$key])) {
                    $diffFields = $this->updateLog(
                        $diffFields,
                        [
                            'label' => 'Aprovador',
                            'value' => "{$value->name} Nível {$value->nivel}",
                            'relation' => 'workflow_aprovadores',
                            'data' => $value,
                        ],
                        'del'
                    );
                }
            }
        }

        // Salva id do workflow no grupo consulta
        $this->grupoRepository->updateGrupoConsulta($idGrupoConsulta, $perfilId, [
            'id_workflow' => $ultimoIdWorkflow,
            'id_versao_workflow' => $ultimaVersaoWorkflow
        ]);

        //LOG
        $details = [
            'id' => $workflow->id,
            'id_versao' => $workflow->id_versao,
            'request' => $params,
        ];

        $vars = ['nome' => "{$workflow->id} v{$workflow->id_versao}"];
        if ($workflow->id_versao != 1) {
            $vars = [
                'nome' => "{$workflowAtual['nome']} v{$workflowAtual['id_versao']}",
                'nome_novo' => "{$workflow->nome} v{$workflow->id_versao}"
            ];

            $details['changes'] = $diffFields;
        }

        $this->auroraLogService->saveLog(
            1,
            5,
            $workflow->id_versao == 1 ? 1 : 2,
            "{$workflow->id} v{$workflow->id_versao}",
            [
                'path' => $workflow->id_versao == 1 ? 'log.workflow-criar' : 'log.workflow-editar',
                'var' => $vars,
            ],
            $details
        );

        return [
            'id' => $workflow->id,
            'version' => $workflow->id_versao
        ];
    }

    private function checkWorkflowHasLevelZero(array $workflowLevels): bool
    {
        foreach ($workflowLevels as $level) {
            if ($level['level'] != 0) {
                continue;
            }
            return true;
        }
        return false;
    }

    private function updateLog(array $logs, array $newLog, string $key): array
    {
        if (!empty($newLog[$key])) {
            foreach($newLog[$key] as &$log) {
                $logs[$key][] = $log;
            }
        }

        return $logs;
    }

    public function editReasonAnalysis(array $params)
    {
        return $this->workflowRepository->updateReasonAnalysisManual($params);
    }
}
