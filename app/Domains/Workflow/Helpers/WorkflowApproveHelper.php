<?php

namespace App\Domains\Workflow\Helpers;

use App\Domains\Workflow\Repositories\WorkflowRepository;
use Exception;
use App\Exceptions\CustomException;

class WorkflowApproveHelper
{
    private WorkflowRepository $workflowRepository;
    public function __construct(
        WorkflowRepository $workflowRepository
    ) {
        $this->workflowRepository = $workflowRepository;
    }
    public function getMaster(int $userID, bool $isMaster, int $workflowID, int $versao, int &$id, int $pesquisaConsultaID): int|Exception|null
    {
        $has = $this->workflowRepository->existsApprover(
            $workflowID,
            $versao,
            $userID,
            $pesquisaConsultaID
        );

        //SE NÃO ENCONTROU USÚÁRIO APROVADOR, ENTÃO, VERIFICA SE É MASTER
        if ($has) {
            $id = $has->aprovadores?->first()?->id;
            return null;
        }

        if ($isMaster) {
            return $userID;
        }

        return throw new CustomException(trans('sem-permissao aprovador-ou-master'), 1);
    }
}
