<?php

namespace App\Domains\Workflow\Helpers;

use App\Domains\Workflow\Repositories\WorkflowAprovadoresRepository;
use Exception;
use App\Exceptions\CustomException;

class UserWithNivelCanMakeApproveHelper
{
    private WorkflowAprovadoresRepository $workflowAprovadoresRepository;
    public function __construct(
        WorkflowAprovadoresRepository $workflowAprovadoresRepository
    ) {
        $this->workflowAprovadoresRepository = $workflowAprovadoresRepository;
    }

    public function existsApprove(int $nivel, int $userID, array $dossie): int|Exception
    {
        $aprovadores = $this->workflowAprovadoresRepository->getByID(
            $dossie['id_workflow'],
            $dossie['id_versao_workflow'],
            $nivel,
            $dossie['id']
        )->groupBy(function ($item) {
            return $item->workflowPesquisaConsulta->isNotEmpty() ? '1' : '0';
        });

        //ENCONTROU USÚÁRIO APROVADOR
        if (empty($aprovadores->get('1', []))) {
            return $aprovadores->get('0')->first()->id;
        }
        if (!empty($aprovadores->get('0', []))) {
            return $aprovadores->get('0')->first()->id;
        }

        return throw new CustomException(trans('workflow.existe-aprovacao'), 1);
    }
}
