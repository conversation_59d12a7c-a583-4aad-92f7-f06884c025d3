<?php

namespace App\Domains\Workflow\Repositories;

use App\Domains\Workflow\Entities\Client\PesquisaConsultaComentarios;
use App\Domains\Workflow\Entities\Client\Workflow;
use Illuminate\Support\Facades\DB;

class WorkflowRepository
{
    private Workflow $workflow;
    private PesquisaConsultaComentarios $analysis;
    public function __construct(
        Workflow $workflow,
        PesquisaConsultaComentarios $analysis
    )
    {
        $this->workflow = $workflow;
        $this->analysis = $analysis;
    }

    public function getWorkflow(int $versao, int $workflowID): array
    {
        return $this->workflow->where('id', $workflowID)
        ->where('id_versao', $versao)
        ->with([
            'aprovacaoAutomatica' => function ($q) use ($versao) {
                $q->where('id_versao_workflow', $versao);
            }
        ])
        ->first()
        ->toArray();
    }

    public function getWorkflowDetails(int $dossieID, int $versao, int $workflowID):array
    {
        return $this->workflow->where('id', $workflowID)
        ->where('id_versao', $versao)
        ->with([
            'aprovadores' => function ($q) use ($versao, $dossieID) {
                $q->where('id_versao_workflow', $versao)
                ->with([
                    'aprovador' => function ($q) {
                        $q->select('id', 'nome', 'sobrenome');
                    },
                    'workflowPesquisaConsulta' => function ($q) use ($dossieID) {
                        $q->where('id_pesquisa_consulta', $dossieID)
                        ->with(['aprovadorMaster' => function ($q) {
                            $q->select('id', 'nome', 'sobrenome');
                        }]);
                    }
                ])
                ->orderBy('nivel');
            },
            'aprovacaoAutomatica' => function ($q) use ($versao, $dossieID) {
                $q->where('id_versao_workflow', $versao)
                ->with([
                    'fonte' => function ($q) {
                        $q->select('id', 'nome');
                    },
                    'workflowFonte' => function ($q) use ($dossieID) {
                        $q->where('id_pesquisa_consulta', $dossieID);
                    }
                ]);
            },
        ])
        ->withCount(['comments' => function ($q) use ($versao) {
            $q->where('version_workflow_id', $versao);
        }])
        ->first()->toArray();
    }

    public function getQtNiveis(int $id, int $versao): int
    {
        return $this->workflow->where('id_versao', $versao)->where('id', $id)->first()->qt_niveis;
    }

    public function existsApprover(int $workflowID, int $versao, int $userID, int $pesquisaConsultaID):Workflow|null
    {
        return $this->workflow->where('id', $workflowID)
        ->where('id_versao', $versao)
        ->whereHas('aprovadores', function ($q) use ($versao, $userID, $pesquisaConsultaID) {
            $q->where('id_versao_workflow', $versao)
            ->where('id_usuario', $userID)
            ->whereDoesntHave('workflowPesquisaConsulta', function ($q2) use ($pesquisaConsultaID) {
                $q2->where('id_pesquisa_consulta', $pesquisaConsultaID);
            });
        })
        ->with(['aprovadores' => function ($q) use ($versao, $userID) {
            $q->where('id_versao_workflow', $versao)
            ->where('id_usuario', $userID)->select('id', 'id_workflow');
        }])
        ->first();
    }

    public function getAnalysisAuto(int $dossieId)
    {
        return $this->analysis->where('id_pesquisa_consulta', $dossieId)
        ->with(['user'])
        ->where(function ($q) {
            $q->where('aprovado_automatico', true);
        })
        ->orderBy('id', 'desc')
        ->get();
    }

    public function setAnalysisManual(array $newData, array $data)
    {
        $this->analysis->firstOrCreate(
            $newData,
            $data
        );
    }
    public function updateReasonAnalysisManual(array $data)
    {
        $analysis = $this->analysis->findOrFail(
            $data['analise_id']
        );
        $analysis->comentario = $data['motivo'];
        $analysis->data_inclusao = date('Y-m-d H:i:s');
        return $analysis->save();
    }

    public function getLastId(): Workflow
    {
        return $this->workflow->select(DB::raw('MAX(id) as ultimo_id'))->first();
    }

    public function getLastVersion(int $ultimoIdWorkflow): Workflow
    {
        return $this->workflow->select(DB::raw('MAX(id_versao) as ultima_versao'))
            ->where('id', $ultimoIdWorkflow)->first();
    }

    public function create(array $params): Workflow
    {
        return $this->workflow->create($params);
    }

    public function delete(int $id): ?bool
    {
        return $this->workflow->delete($id);
    }
}
