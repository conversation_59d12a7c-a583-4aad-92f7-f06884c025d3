<?php

namespace App\Domains\Workflow\Repositories;

use App\Domains\Workflow\Entities\Client\WorkflowAprovadores;
use Illuminate\Support\Collection;

class WorkflowAprovadoresRepository
{
    private WorkflowAprovadores $workflowAprovadores;

    public function __construct(WorkflowAprovadores $workflowAprovadores)
    {
        $this->workflowAprovadores = $workflowAprovadores;
    }

    public function getByID(int $workflowID, int $versao, int $nivel, int $dossieId)
    {
        $aprovadores = $this->workflowAprovadores
        ->where(function ($q) use ($versao, $nivel, $workflowID) {
            $q->where('id_versao_workflow', $versao)
            ->where('id_workflow', $workflowID);
        })
        ->with('workflowPesquisaConsulta', function ($q) use ($dossieId) {
            $q->where('id_pesquisa_consulta', $dossieId)->select('id_workflow_aprovadores');
        })
        ->get();
        return $aprovadores;
    }

    public function getWithUsuarioAprovadorById(int $workflowID, int $versao)
    {
        return $this->workflowAprovadores->where('id_workflow', $workflowID)
            ->with(['aprovador' => function ($q) {
                $q->select('id', 'nome', 'sobrenome', 'email');
            }])
            ->where('id_versao_workflow', $versao)
            ->orderBy('nivel', 'asc')
            ->get();
    }

    public function getByWorkflowVersaoNivelId(int $workFlowId, int $versaoWorkFlowId, int $nivel): Collection
    {
        return $this->workflowAprovadores->where('id_workflow', $workFlowId)
            ->whereHas(
                'aprovador',
                fn($q) => $q->where('ativo', true)->where('bloqueio', false)
            )
            ->with(['aprovador' => function ($q) {
                $q->select('id', 'nome', 'sobrenome', 'email');
            }])
            ->where('id_versao_workflow', $versaoWorkFlowId)
            ->where('nivel', $nivel)
            ->orderBy('nivel', 'asc')
            ->get();
    }

    public function updateOrCreate(
        int $id,
        array $params
    ): bool {
        $data = $this->workflowAprovadores->updateOrCreate(
            [
                'id_pesquisa_consulta' => $id,
            ],
            [
                'status_dossie_workflow' => $params['status'],
                'data_analise' => date('Y-m-d'),
                'aprovado_automatico' => false,
            ]
        );

        if (empty($data)) {
            return false;
        }

        return true;
    }

    public function create(array $params): WorkflowAprovadores
    {
        return $this->workflowAprovadores->create($params);
    }
}
