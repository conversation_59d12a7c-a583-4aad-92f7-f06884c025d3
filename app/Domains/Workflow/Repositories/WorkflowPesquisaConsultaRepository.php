<?php

namespace App\Domains\Workflow\Repositories;

use App\Domains\Workflow\Entities\Client\WorkflowPesquisaConsulta;
use App\Exceptions\CustomException;
use Illuminate\Database\Eloquent\Collection;

class WorkflowPesquisaConsultaRepository
{
    const STATUS_DISAPPROVE = 'reprovado';
    private WorkflowPesquisaConsulta $workflowPesquisaConsulta;

    public function __construct(WorkflowPesquisaConsulta $workflowPesquisaConsulta)
    {
        $this->workflowPesquisaConsulta = $workflowPesquisaConsulta;
    }

    public function all(int $id): Collection
    {
        return $this->workflowPesquisaConsulta->select('status')->where('id_pesquisa_consulta', $id)->get();
    }
    
    public function analiseManual(int $id): array
    {
        $workflowPesquisa = $this->workflowPesquisaConsulta->where('id_pesquisa_consulta', $id)
        ->orderBy('status', 'desc')
        ->get()
        ->toArray();
        return $workflowPesquisa[0] ?? $workflowPesquisa;
    }

    public function getAnalysisManual(int $id): Collection
    {
        $workflowPesquisa = $this->workflowPesquisaConsulta->where('id_pesquisa_consulta', $id)
        ->with([
            'reason',
            'aprovadorMaster',
            'aprovador.aprovador' => function ($q) {
                $q->select('id', 'nome', 'sobrenome', 'imagem_perfil', 'email');
            },
        ])
        ->get();
        return $workflowPesquisa;
    }
    public function insert(
        int $id,
        ?int $master,
        int $approveID,
        string $status
    ): ?int {
        $id = $this->workflowPesquisaConsulta->insertGetId(
            [
                'id_pesquisa_consulta' => $id,
                'id_master' => $master,
                'status' => $status,
                'id_workflow_aprovadores' => $approveID
            ]
        );
        
        if (empty($id)) {
            return throw new CustomException("Error ao executar essa ação", 1);
        }

        return $id;
    }
}
