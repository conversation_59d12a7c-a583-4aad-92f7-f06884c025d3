<?php

namespace App\Domains\Workflow\Repositories;

use App\Domains\Workflow\Entities\Gupy\DossieGupy;

class DossieGupyRepository
{
    private DossieGupy $dossieGupy;

    public function __construct(DossieGupy $dossieGupy)
    {
        $this->dossieGupy = $dossieGupy;
    }

    public function getByClientIDLoteID(int $loteID): ?DossieGupy
    {
        return $this->dossieGupy->where('lote_id', $loteID)
        ->first();
    }
}
