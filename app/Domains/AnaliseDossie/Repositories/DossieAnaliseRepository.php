<?php

namespace App\Domains\AnaliseDossie\Repositories;

use App\Domains\AnaliseDossie\Entities\Client\DossierAnalyze;
use Illuminate\Database\Eloquent\Collection;

class DossieAnaliseRepository
{
    public function __construct(
        private readonly DossierAnalyze $dossierAnalyze
    )
    {
    }

    public function storeDossierAnalyze(array $dossieAnalysis)
    {
        $this->dossierAnalyze->create($dossieAnalysis);
    }

    public function getAnalyseDossieId($dossierId): mixed
    {
        return $this->dossierAnalyze->where('dossie_id', $dossierId)->get();
    }

    private function baseDossierAnalise($dossierId)
    {
        return $this->dossierAnalyze
            ->with(['user' => fn($query) => $query->select('id', 'nome', 'sobrenome')])
            ->where('dossie_id', $dossierId);
    }

    public function getHistoricoAnaliseDossie($dossierId): Collection|array
    {
        return $this->baseDossierAnalise($dossierId)
            ->get();
    }

    public function getAnaliseDossie($dossierId, bool $relevant = false): object|null
    {
        return $this->baseDossierAnalise($dossierId)
            ->when($relevant, fn($query) => $query->where('is_relevante', $relevant))
            ->orderByDesc('created_at')
            ->first();
    }

    public function updateRelevance(int $dossieId, bool $isRelevance): bool
    {
        $lastVersion = $this->dossierAnalyze->where('dossie_id', $dossieId)->orderByDesc('created_at')
            ->first();
        $lastVersion->is_relevante = $isRelevance;
        return $lastVersion->save();
    }
}
