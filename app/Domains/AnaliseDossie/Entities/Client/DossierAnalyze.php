<?php

namespace App\Domains\AnaliseDossie\Entities\Client;

use App\Domains\User\Entities\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class DossierAnalyze extends Model
{
    use SoftDeletes;

    protected $table = 'dossier_analyze';

    protected $fillable = [
        "motivo",
        "visible",
        "version",
        "status",
        "user_id",
        "dossie_id",
        "is_relevante",
    ];
    protected $casts = [
        'created_at' => 'datetime'
    ];
    public function dossie(): BelongsTo
    {
        return $this->belongsTo(PesquisaConsulta::class, 'dossie_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

}
