<?php

declare (strict_types=1);

namespace App\Domains\User\Services;

use App\Domains\User\Repositories\UserRepository;
use App\Domains\User\Transform\TransformUserAndOthersUsersGroup as TransformUserAndYourGroup;
use Illuminate\Support\Arr;

class UserService
{
    private UserRepository $userRepository;
    private TransformUserAndYourGroup $transformUserAndYourGroup;

    public function __construct(
        TransformUserAndYourGroup $transformUserAndYourGroup,
        UserRepository            $userRepository
    )
    {
        $this->userRepository = $userRepository;
        $this->transformUserAndYourGroup = $transformUserAndYourGroup;
    }

    public function getLogoConta(): array
    {
        $masterId = session('setup.id_master');
        return $this->userRepository->getLogoConta($masterId);
    }

    public function getUserSession(): array
    {
        $userId = session('setup.id_usuario');
        $hub = session('setup.hubspot_visitor_key', '');
        $master = session('master');
        $admin = session('admin');
        $masterPermissions = session('contents.master_permissions');
        $simulation = session('env') == 'simulate';
        $groupId = session('setup.grupo_usuario');
        $planId = session('id_plano');
        $user = $this->userRepository->getUserById($userId)?->toArray();
        $email = $user['email'];
        $hasAccessToApp = in_array(100, array_keys(session('contents.aplicativos.utilitarios')));
        if ($user) {
            $user = $this->transformUserAndYourGroup->mountUser($user);
            $user['id'] = $userId;
            $user['email'] = $email;
            $user['id_plano'] = $planId;
            $user['simulate'] = $simulation;
            $user['master'] = $master;
            $user['admin'] = $admin;
            $user['hubspot_visitor_key'] = $hub;
            $user['group_id'] = $groupId;
            $user['hasAccessToApp'] = $hasAccessToApp;
            $user['masterPermissions'] = $masterPermissions;

            return $user;
        }
        return [];
    }
}
