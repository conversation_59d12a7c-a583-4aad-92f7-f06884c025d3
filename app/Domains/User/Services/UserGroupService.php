<?php

namespace App\Domains\User\Services;

use App\Domains\ConvertOldDossie\Utils\Funcoes;
use App\Domains\User\Repositories\UserGroupRepository;
use App\Domains\User\Transform\TransformUserByGroup;
use App\Domains\User\Transform\TransformUserAndOthersUsersGroup as TransformUserAndYourGroup;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class UserGroupService
{
    private UserGroupRepository  $userGroupRepository;
    private TransformUserByGroup $transformUserByGroup;
    private TransformUserAndYourGroup $transformUserAndYourGroup;

    public function __construct(
        UserGroupRepository  $userGroupRepository,
        TransformUserAndYourGroup  $transformUserAndYourGroup,
        TransformUserByGroup $transformUserByGroup
    ) {
        $this->userGroupRepository = $userGroupRepository;
        $this->transformUserByGroup = $transformUserByGroup;
        $this->transformUserAndYourGroup = $transformUserAndYourGroup;
    }

    public function getUsuarioByGrupo(?array $params): Collection|array
    {

        $nameOrEmail = Arr::get($params, 'name_or_email');
        $groupId = session('setup.grupo_usuario');

        $data = $this->userGroupRepository->getUsuarioByGrupo($nameOrEmail, $groupId);
        return $this->transformUserByGroup->handle($data);
    }

    public function getUserAndOthersUsersGroup(): array
    {
        $groupId = session('setup.grupo_usuario');
        $data = $this->userGroupRepository->getUsersByGroup($groupId)->toArray();
        return $this->transformUserAndYourGroup->handle(
            $data,
            session('setup.id_usuario')
        );
    }

    public function getAll(): array
    {
        $groups = $this->userGroupRepository->getAll();
        $groups = $groups->toArray();

        if (!empty($groups)) {
            foreach ($groups as &$group) {
                $group['alias'] = Funcoes::getAlias($group['nome']);
            }
        }

        return $groups;
    }

    public function getAllGroupsWithoutAlias(): array
    {
        $groups = $this->userGroupRepository->getAll();
        $groups = $groups->toArray();
        $final = [];

        if (!empty($groups)) {
            foreach ($groups as &$group) {
                $final[] = [
                    'id' => $group['id'],
                    'name' => $group['nome']
                ];
            }
        }

        return $final;
    }
}
