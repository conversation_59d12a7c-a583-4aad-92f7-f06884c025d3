<?php

namespace App\Domains\Tag\Repositories;

use DossieSourcesModule\Sources\Entities\Cliente\DossieTagsSource;

class TagSourceRepository
{
    private DossieTagsSource $dossieTagSource;
    public function __construct(DossieTagsSource $dossieTagSource)
    {
        $this->dossieTagSource = $dossieTagSource;
    }

    public function add(int $dossieID, int $userID, int $tagID, array $params): bool
    {
        $data = $this->dossieTagSource->updateOrCreate(
            ['dossie_id' => $dossieID, 'source_id' => $params['source']],
            ['tag_id' => $tagID, 'user_id' => $userID]
        );

        if (!empty($data)) {
            return true;
        }

        return false;
    }

    public function delete(int $dossieID, int $sourceID, int $userID): bool
    {
        $data = $this->dossieTagSource->where(
            [
                'dossie_id' => $dossieID,
                'source_id' =>$sourceID
            ]
        )
        ->delete();
        if (!empty($data)) {
            return true;
        }

        return false;
    }
}
