<?php

namespace App\Domains\Tag\Repositories;

use DossieSourcesModule\Sources\Entities\Cliente\DossieTags;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class TagDossieRepository
{
    private DossieTags $dossieTags;

    public function __construct(DossieTags $dossieTags)
    {
        $this->dossieTags = $dossieTags;
    }

    public function getByDossieID(int $dossieID): Collection
    {
        return $this->dossieTags->select('id', 'tag', 'cor')
            ->where('dossie_id', $dossieID)
            ->get();
    }

    public function update(int $dossieID, int $tag, array $params): bool
    {
        $data = $this->dossieTags->where('dossie_id', $dossieID)
            ->where('id', $tag)
            ->update($params);
        if (!empty($data)) {
            return true;
        }

        return false;
    }

    public function insert(int $dossieID, int $userID, array $params): DossieTags
    {
        return $this->dossieTags->updateOrCreate(
            [
                'dossie_id' => $dossieID,
                'tag' => $params['tag'],
            ],
            [
                'cor' => $params['cor'],
                'user_id' => $userID
            ]
        );
    }

    public function delete(int $dossieID, int $tagID): bool
    {
        $tag = $this->dossieTags->where(
            ['dossie_id' => $dossieID, 'id' => $tagID]
        )->first();

        if ($tag) {
            $data = $tag->delete();
            $tag->tagsSource()->delete();
            $tag->tagsCategory()->delete();
            return $data > 0;
        }

        return false;
    }

    public function getAmountByDossieID(int $dossieId): array
    {
        return $this->dossieTags->select('id', 'tag', 'cor')
            ->withCount([
                'tagsCategory' => function (Builder $builder) use ($dossieId) {
                    $builder->where('dossie_id', $dossieId);
                },
                'tagsSource' => function (Builder $builder) use ($dossieId) {
                    $builder->where('dossie_id', $dossieId);
                }
            ])
            ->where('dossie_id', $dossieId)
            ->get()
            ->toArray();
    }
}
