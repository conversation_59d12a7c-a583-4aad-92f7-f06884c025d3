<?php

namespace App\Domains\Tag\Repositories;

use DossieSourcesModule\Sources\Entities\Cliente\DossieTagsCategory;

class TagCategoryRepository
{
    private DossieTagsCategory $dossieTagCategory;
    public function __construct(DossieTagsCategory $dossieTagCategory)
    {
        $this->dossieTagCategory = $dossieTagCategory;
    }

    public function add(int $dossieID, int $userID, int $tagID, array $params): bool
    {
        $data = $this->dossieTagCategory->updateOrCreate(
            ['dossie_id' => $dossieID, 'source_group_alias' => $params['category']],
            ['tag_id' => $tagID, 'user_id' => $userID]
        );

        if (!empty($data)) {
            return true;
        }

        return false;
    }

    public function delete(int $dossieID, string $categoryAlias): bool
    {
        $data = $this->dossieTagCategory->where(
            [
                'dossie_id' => $dossieID,
                'source_group_alias' =>$categoryAlias
            ]
        )
        ->delete();
        if (!empty($data)) {
            return true;
        }

        return false;
    }
}
