<?php

namespace App\Domains\ConvertOldDossie;

use DossieSourcesModule\Sources\Entities\Seguranca\Capture as CapturaModel;
use App\Domains\ConvertOldDossie\Utils\Funcoes;
use App\Domains\ConvertOldDossie\Utils\Functions;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Exceptions\CustomException;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetSourceResultsOldDossieBySource
{
    private $mapTypeRelevance = ['R' => true, 'I' => false];
    const DEFAULT_VALUES_RELEVANCE = null;

    public function __call($name, $params)
    {
        // Verifica se existe tratamento diferenciado
        if (method_exists($this, $name)) {
            return call_user_func_array([$this, $name], $params);
        } else {
            // Executa o tratamento padrão
            return call_user_func_array([$this, '__default'], $params);
        }
    }

    private function getRelevance(?string $estado): bool|null
    {
        return $this->mapTypeRelevance[$estado] ?? self::DEFAULT_VALUES_RELEVANCE;
    }

    private function __default(CapturaModel $captura, PesquisaConsulta $consulta, $resumo = false)
    {
        $this->setTable($captura, $consulta);
        if (is_array($consulta)) {
            $consulta = (object)$consulta;
        }
        if (is_array($captura)) {
            $captura = (object)$captura;
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        // Resultado de capturas com referência
        if (!$captura->captura_sem_referencia) {
            // Banco de captura
            $db = \DB::connection('pgsql_captura3');
            $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
            $dossies = $this->getConsultaIds($dossierID, $captura->id);

            $dossies = collect($dossies)->filter(function ($dossie) {
                return in_array($dossie->estado, [5, 4]);
            });

            if ($dossies->isEmpty()) {
                return '';
            }

            $referencias = $dossies->pluck('referencia')->unique()->toArray();

            if (empty($referencias)) {
                return null;
            }
            try {
                $dado = $db->table($captura->tabela . '_consulta')
                    ->select('dado', 'data_hora')
                    ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
                    ->whereIn($captura->tabela . '_consulta.id', $referencias)
                    ->first();
            } catch (Exception $e) {
                if (preg_match('/Undefined column/', $e->getMessage())) {
                    return '';
                }
                throw new CustomException('Erro Consulta:' . $e->getMessage() . 'LINE:' . $e->getLine());
            }

            // Verifica se tem resposta
            if ($dado == null) {
                return '';
            }

            // Converter dados
            $arquivo = Funcoes::RdsCaptureData($dado->dado);

            if (empty($arquivo)) {
                return $arquivo;
            }

            if (is_string($arquivo) && strpos($arquivo, "a:") === 0) {
                return Funcoes::voToObject($arquivo);
            }

            if ($arquivo instanceof Collection) {
                $arquivo = $arquivo->toArray();
            }

            if (!is_array($arquivo)) {
                $arquivo = json_decode($arquivo, true);
            }

            if (isset($arquivo['data']) && !is_string($arquivo['data'])) {
                return [$arquivo['data']];
            }

            return $arquivo;
        } else {
            //recupera os dados da fonte
            $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
            $dados = $this->getDadosSemRef($captura->id, $captura->tabela, $dossierID);
            // Verifica se tem resposta
            if ($dados == null) {
                return [];
            }

            // Verifica se os dados retornados contém mais informações além dos dados e se essas informações estão em dados binários
            // Se sim, então esses dados são convertidos
            if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado) && is_resource($dados[0]->dado)) {
                foreach ($dados as $i_dado => $dado) {
                    $arquivo = Funcoes::RdsCaptureData($dados[0]->dado);
                    if (is_string($arquivo) && strpos($arquivo, "a:") === 0) {
                        return Funcoes::voToObject($arquivo);
                    }
                    if (!$arquivo) {
                        $arquivo = [];
                    }

                    $dados[$i_dado]->dado = $arquivo;
                }
            }

            if (is_string($dados) && strpos($dados, "a:") === 0) {
                return Funcoes::voToObject($dados);
            }

            if ($dados instanceof Collection) {
                $dados = $dados->toArray();
            }

            return $dados;
        }
    }

    private function TransparenciaBrasilCeis($captura, $consulta, $resumo = false)
    {
        return [$this->__default($captura, $consulta, $resumo)];
    }

    private function Judit($captura, $consulta, $resumo = false)
    {
        $dado = $this->__default($captura, $consulta, $resumo);

        if (is_string($dado)) {
            $dado = json_decode($dado, true);
        }

        if (empty($dado)) {
            return [];
        }

        foreach ($dado as &$value) {
            $value['conteudo_relevante'] = self::DEFAULT_VALUES_RELEVANCE;
        }

        return $dado;
    }


    private function FontesJuridicas($captura, $consulta, $resumo = false)
    {
        $dado = $this->__default($captura, $consulta, $resumo);

        if (is_string($dado)) {
            $dado = json_decode($dado, true);
        }

        if (empty($dado)) {
            return [];
        }

        foreach ($dado as &$value) {
            $value['conteudo_relevante'] = self::DEFAULT_VALUES_RELEVANCE;
        }

        return $dado;
    }

    private function ProcessosJuridicos($captura, $consulta, $resumo = false)
    {
        $dado = $this->__default($captura, $consulta, $resumo);

        if (is_string($dado)) {
            $dado = json_decode($dado, true);
        }

        if (empty($dado)) {
            return [];
        }

        foreach ($dado as &$value) {
            $value['conteudo_relevante'] = self::DEFAULT_VALUES_RELEVANCE;
        }

        return $dado;
    }

    private function FepamLicenciamentoAmbientalRS($captura, $consulta, $resumo = false)
    {
        $dado = $this->__default($captura, $consulta, $resumo);

        if (is_string($dado)) {
            $dado = json_decode($dado, true);
        }
        if (empty($dado[0]->cpfCnpj) && empty($dado[0]->codEmpreendedor)) {
            return '';
        }

        return $dado;
    }

    /**
     *  Metodo adicinado para unserializar os dados e transformar em array,
     *  Mas isso só deve ser feito depois da data de troca de fonte
     */
    private function SiorgCargoDocumento($captura, $consulta, $resumo = false)
    {
        $dados = $this->__default($captura, $consulta, $resumo);

        //Retorna sem passar pela transformação se for lambda
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            return $dados;
        }

        // data da mudança da url fonte
        $trocaDeFonte = \Carbon\Carbon::parse('2019-07-15 00:00:00');

        $dataConsulta = \Carbon\Carbon::parse($consulta->data_criacao);

        if ($dataConsulta->greaterThan($trocaDeFonte)) {
            return is_array($dados) ? $dados : json_decode(unserialize($dados), true);
        }

        return $dados;
    }

    private function isNewLambdaCaptura($captura, $consulta)
    {
        if (!empty($consulta->data_estado)) {
            $dataCriacao = explode(" ", $consulta->data_estado);
        } else {
            if (!empty($consulta->data_faturamento)) {
                $dataCriacao = explode(" ", $consulta->data_faturamento);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                } else {
                    return false;
                }
            }
        }

        if (!empty($captura->data_migracao_lambda)
            && isset($dataCriacao[0])
            && !empty($dataCriacao[0])
            && strtotime($captura->data_migracao_lambda) <= strtotime($dataCriacao[0])
        ) {
            return true;
        } else {
            return false;
        }
    }

    private function dowjonesApiWatchlist(CapturaModel $captura, $consulta, $resumo = false)
    {
        $result = $this->__default($captura, $consulta, $resumo);
        if (is_string($result)) {
            $result = json_decode($result, true);
        }

        if (is_array($result) && count($result)) {
            return $result[0];
        }

        return $result;
    }

    private function setTable(&$captura, $consulta)
    {
        //caso a data da migração para o lambda seja menor que a criação do dossie a tabela sempre vai ser captura.captura
        if (!empty($consulta->data_estado)) {
            $dataCriacao = explode(" ", $consulta->data_estado);
        } else {
            if (!empty($consulta->data_faturamento)) {
                $dataCriacao = explode(" ", $consulta->data_faturamento);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                } else {
                    return;
                }
            }
        }

        if (!empty($captura->data_migracao_lambda)
            && isset($dataCriacao[0])
            && !empty($dataCriacao[0])
            && strtotime($captura->data_migracao_lambda) <= strtotime($dataCriacao[0])
        ) {
            $captura->tabela = 'captura.captura';
        }
    }


    private function getDadosSemRef($id_captura, $tabela, $id_consulta)
    {
        // Banco do upMiner
        $db = DB::connection('upminer');
        $IdsConsulta = $this->getConsultaIds($id_consulta, $id_captura);

        $ids = collect($IdsConsulta)
            ->pluck('id')
            ->unique()
            ->toArray();

        $dados = $db->table($tabela)
            ->select()
            ->whereIn('id_pesquisa_consulta', $ids)
            ->orderBy('id', 'ASC')
            ->get();

        if (is_array($dados)) {
            usort($dados, [$this, 'sortByRelevancia']);
        }

        return $dados;
    }

    private function eleicoesBase(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela)
            ->where($captura->tabela . '.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        return $dado;
    }

    private function ofacInstant(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta, $resumo);

        if (empty($arquivo)) {
            return array();
        }

        if (!empty($consulta->data_estado)) {
            $dataCriacao = explode(" ", $consulta->data_estado);
        } else {
            if (!empty($consulta->data_faturamento)) {
                $dataCriacao = explode(" ", $consulta->data_faturamento);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                }
            }
        }

        if (!empty($captura->data_migracao_lambda)
            && isset($dataCriacao[0])
            && !empty($dataCriacao[0])
            && strtotime($captura->data_migracao_lambda) <= strtotime($dataCriacao[0])
        ) {
            $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);
            return $arquivo['data'] ?? $arquivo;
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();


        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = str_replace("''", "'", (pg_unescape_bytea(stream_get_contents($dado->dado))));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);

        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function MteCertidaoDebito(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($consulta->referencia) {
            $arquivo = $this->__default($captura, $consulta, $resumo);
            if (is_string($arquivo)) {
                $arquivo = json_decode($arquivo, true);
            }
            $arquivo['conteudo'] = iconv('ISO-8859-1', 'UTF-8', utf8_decode($arquivo['conteudo']));
            return [$arquivo];
        }

        return null;
    }

    private function transparenciaBrasilExcelencia(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        $arquivo = $dado->dado;

        return $arquivo;
    }

    /**
     * Método para parseamento dos dados da fonte Certidão Negativa de Débitos (IBAMA)
     *
     * @param CapturaModel $captura
     * @param array $consulta
     * @param boolean $resumo
     *
     * @return array
     * <AUTHOR> <??>
     * @version 1.0.0
     *          1.1.0 - Vitor Hugo R. - Mudança para o Lambda da captura e ajustes do PSR7
     *          1.2.0 - Vitor Hugo R. - Mudança para o metódo __default
     *          1.3.0 - Jefferson Mesquita - 23/05/2019 - Mudança para verificar se for lambda buscar em captura.captura
     *
     */
    private function IbamaCertidaoNegativa(CapturaModel $captura, $consulta, $resumo = false)
    {
        $this->setTable($captura, $consulta);
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dossieID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $referencias = $consulta->getReferenciasDaCaptura($captura->id, $dossieID);

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('data_hora', 'dado')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->whereIn($captura->tabela . '_consulta.id', $referencias)
            ->first();


        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        $dado->dado = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($dado->dado, "a:") === 0) {
            return Funcoes::voToObject($dado->dado);
        }
        return $dado->dado;
    }

    private function DataprevCnd(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta, $resumo);

        if (!$this->isNewLambdaCaptura($captura, $consulta)) {
            $arquivo = Funcoes::voToObject($arquivo);

            $arquivo = [
                'certidao' => $arquivo['certidao'],
                'dataEmissao' => $arquivo['data_emissao'],
                'fin' => $arquivo['fin'],
                'dataValidade' => $arquivo['data_validade'],
                'dataCancelamento' => $arquivo['data_cancelamento'],
                'horaBrasilia' => $arquivo['hora_brasilia'],
                'pdf' => null,
            ];
        }

        return $arquivo;
    }

    /**
     * Método para parseamento dos dados da fonte Certidão Negativa de Débitos Trabalhistas (TST)
     *
     * @param CapturaModel $captura
     * @param array $consulta
     * @param boolean $resumo
     *
     * @return array
     * <AUTHOR> <??>
     * @version 1.0.0
     *          1.1.0 - Jefferson Mesquita - 23/05/2019 - Mudança para verificar se for lambda buscar em captura.captura
     *
     */
    private function Tst(CapturaModel $captura, $consulta, $resumo = false)
    {
        $this->setTable($captura, $consulta);
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dossieID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $referencias = $consulta->getReferenciasDaCaptura($captura->id, $dossieID);

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->whereIn($captura->tabela . '_consulta.id', $referencias)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        $dado->dado = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($dado->dado, "a:") === 0) {
            return Funcoes::voToObject($dado->dado);
        }
        return $dado->dado;
    }

    private function susepNomeLambda(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        $r = [];

        foreach ($results as $result) {
            try {
                $r[] = json_decode($result->dado);
            } catch (Exception $e) {
            }
        }

        return $r;
    }

    private function susepEmpresaNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // mudança de fonte
        return $this->susepNomeLambda($captura, $consulta, $resumo = false);

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join(
                $captura->tabela . '_referencia',
                $captura->tabela . '_referencia.id',
                '=',
                $captura->tabela . '_consulta.id_referencia'
            )
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }

        return $arquivo;
    }

    /**
     * Método para parseamento dos dados da fonte World Compliance
     *
     * @param CapturaModel $captura
     * @param array $consulta
     * @param boolean $resumo
     * @return array
     * @version 1.0.0
     * <AUTHOR> Favoreto Makhoul - 29/05/2018
     *
     */
    private function wordCompliance(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);
        $processosClassificacao = [];
        foreach ($IdsConsulta as $ids) {
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->orderBy('id')->get();
            }
            $processosClassificacao = array_merge($processosClassificacao, $dados);
        }
        return $processosClassificacao;
    }

    private function anvisa(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_referencia')
            ->select('dado')
            ->where($captura->tabela . '_referencia.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function antt(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $this->setTable($captura, $consulta);
            return $this->__default($captura, $consulta);
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela)
            ->select()
            ->where($captura->tabela . '.id_referencia', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        return $dado;
    }

    private function suframa(CapturaModel $captura, $consulta, $resumo = false)
    {
        $this->setTable($captura, $consulta);

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            return $this->__default($captura, $consulta);
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = str_replace("''", "'", (pg_unescape_bytea(stream_get_contents($dado->dado))));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function acspPjAnalitico(CapturaModel $captura, $consulta, $resumo = false)
    {
        $this->setTable($captura, $consulta);

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = str_replace("''", "'", (pg_unescape_bytea(stream_get_contents($dado->dado))));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function coaf(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }
        // Verica se retorno tem estrutura das fontes lambdas
        if (isset($results['data']) && !empty($results['data'])) {
            return $results['data'];
        }

        return $results;
    }


    private function spcPefin(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }

        if (
            isset($results['data']['alerta-documento']['xxxxx'])
            && !empty($results['data']['alerta-documento']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['alerta-documento']['xxxxx'])
        ) {
            $results['data']['alerta-documento']['xxxxxxx'] = array($results['data']['alerta-documento']['xxxxx']);
        }

        if (
            isset($results['data']['ccf']['detalhe-ccf'])
            && !empty($results['data']['ccf']['detalhe-ccf'])
            && Funcoes::isAssociativeArray($results['data']['ccf']['detalhe-ccf'])
        ) {
            $results['data']['ccf']['detalhe-ccf'] = array($results['data']['ccf']['detalhe-ccf']);
        }

        if (
            isset($results['data']['pendencia-financeira']['detalhe-pendencia-financeira'])
            && !empty($results['data']['pendencia-financeira']['detalhe-pendencia-financeira'])
            && Funcoes::isAssociativeArray($results['data']['pendencia-financeira']['detalhe-pendencia-financeira'])
        ) {
            $results['data']['pendencia-financeira']['detalhe-pendencia-financeira'] = array($results['data']['pendencia-financeira']['detalhe-pendencia-financeira']);
        }

        if (
            isset($results['data']['cheque-sem-fundo-varejo']['xxxxx'])
            && !empty($results['data']['cheque-sem-fundo-varejo']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['cheque-sem-fundo-varejo']['xxxxx'])
        ) {
            $results['data']['cheque-sem-fundo-varejo']['xxxxxxx'] = array($results['data']['cheque-sem-fundo-varejo']['xxxxx']);
        }
        $results = json_encode($results);

        return $results;
    }

    private function spcProtestos(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }

        if (
            isset($results['data']['protesto']['detalhe-protesto'])
            && !empty($results['data']['protesto']['detalhe-protesto'])
            && Funcoes::isAssociativeArray($results['data']['protesto']['detalhe-protesto'])
        ) {
            $results['data']['protesto']['detalhe-protesto'] = array($results['data']['protesto']['detalhe-protesto']);
        }

   if (isset($arquivo['data']) && !is_string($arquivo['data'])) {
            return [$arquivo['data']];
        }
        return [$results];    }

    /**
     * Seta a tabela e pega o resultado da fonte
     *
     * @param CapturaModel $captura
     * @param array $consulta
     * @param boolean $resumo
     *
     * @return array()
     *
     * @version 1.0.0
     * <AUTHOR> Prates - 30/05/2018 - Este método seta a tabela do banco de dados e pega o resultado da fonte
     *
     */
    private function protestoPesquisa(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        //caso seja serializado é pq está no formato antigo antes de passar para o lambda
        if (Funcoes::isSerialize($results)) {
            $results = Funcoes::voToObject($results);
        } else {
            $results = is_array($results) ? $results : json_decode($results, true);
        }

        $results = isset($results['data']) ? $results['data'] : $results;

        return [$results];
    }

    private function receitaFederalPf(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        //caso seja serializado é pq está no formato antigo antes de passar para o lambda
        if (Funcoes::isSerialize($results)) {
            $results = Funcoes::voToObject($results);
        } else {
            $results = is_array($results) ? $results : json_decode($results, true);
        }

        return $results;
    }

    /**
     * Função de busca e organização dos dados da fonte SPC Localiza Mix
     *
     * @param CapturaModel $captura
     * @param $consulta
     * @param bool $resumo
     * @return array
     * @throws Exception
     * @version 1.1
     *
     * <AUTHOR> Favoreto Makhoul - 17/04/2018
     * Revisao Sami Favoreto Makhoul - 17/04/2018: Correcao para quando o criterio não era valido
     */
    private function spcLocalizaMix(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        return $results;
    }

    private function MunicipiosFronteiricos(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }
        $results = json_decode($results, true);
        if (!empty($results) && !isset($results[0])) {
            $results = [$results];
        }
        return $results;
    }

    private function spcCheques(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }

        if (
            isset($results['data']['alerta-documento']['xxxxx'])
            && !empty($results['data']['alerta-documento']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['alerta-documento']['xxxxx'])
        ) {
            $results['data']['alerta-documento']['xxxxxxx'] = array($results['data']['alerta-documento']['xxxxx']);
        }

        if (
            isset($results['data']['ccf']['detalhe-ccf'])
            && !empty($results['data']['ccf']['detalhe-ccf'])
            && Funcoes::isAssociativeArray($results['data']['ccf']['detalhe-ccf'])
        ) {
            $results['data']['ccf']['detalhe-ccf'] = array($results['data']['ccf']['detalhe-ccf']);
        }

        $results = json_encode($results);

        return $results;
    }

    private function spcChequeAnalitica(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }

        if (
            isset($results['data']['alerta-documento']['xxxxx'])
            && !empty($results['data']['alerta-documento']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['alerta-documento']['xxxxx'])
        ) {
            $results['data']['alerta-documento']['xxxxxxx'] = array($results['data']['alerta-documento']['xxxxx']);
        }

        if (
            isset($results['data']['cheque-lojista']['xxxxx'])
            && !empty($results['data']['cheque-lojista']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['cheque-lojista']['xxxxx'])
        ) {
            $results['data']['cheque-lojista']['xxxxxxx'] = array($results['data']['cheque-lojista']['xxxxx']);
        }

        if (
            isset($results['data']['ccf']['detalhe-ccf'])
            && !empty($results['data']['ccf']['detalhe-ccf'])
            && Funcoes::isAssociativeArray($results['data']['ccf']['detalhe-ccf'])
        ) {
            $results['data']['ccf']['detalhe-ccf'] = array($results['data']['ccf']['detalhe-ccf']);
        }

        if (
            isset($results['data']['contra-ordem-documento-diferente']['xxxxx'])
            && !empty($results['data']['contra-ordem-documento-diferente']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['contra-ordem-documento-diferente']['xxxxx'])
        ) {
            $results['data']['contra-ordem-documento-diferente']['xxxxxxx'] = array($results['data']['contra-ordem-documento-diferente']['xxxxx']);
        }

        if (
            isset($results['data']['contra-ordem']['xxxxx'])
            && !empty($results['data']['contra-ordem']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['contra-ordem']['xxxxx'])
        ) {
            $results['data']['contra-ordem']['xxxxxxx'] = array($results['data']['contra-ordem']['xxxxx']);
        }

        if (
            isset($results['data']['contumacia']['xxxxx'])
            && !empty($results['data']['contumacia']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['contumacia']['xxxxx'])
        ) {
            $results['data']['contumacia']['xxxxxxx'] = array($results['data']['contumacia']['xxxxx']);
        }

        if (
            isset($results['data']['credito-concedido']['xxxxx'])
            && !empty($results['data']['credito-concedido']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['credito-concedido']['xxxxx'])
        ) {
            $results['data']['credito-concedido']['xxxxxxx'] = array($results['data']['credito-concedido']['xxxxx']);
        }

        if (
            isset($results['data']['consulta-realizada']['xxxxx'])
            && !empty($results['data']['consulta-realizada']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['consulta-realizada']['xxxxx'])
        ) {
            $results['data']['consulta-realizada']['xxxxxxx'] = array($results['data']['consulta-realizada']['xxxxx']);
        }

        if (
            isset($results['data']['cheque-consulta-online-srs']['xxxxx'])
            && !empty($results['data']['cheque-consulta-online-srs']['xxxxx'])
            && Funcoes::isAssociativeArray($results['data']['cheque-consulta-online-srs']['xxxxx'])
        ) {
            $results['data']['cheque-consulta-online-srs']['xxxxxxx'] = array($results['data']['cheque-consulta-online-srs']['xxxxx']);
        }

        $results = json_encode($results);

        return $results;
    }

    private function ibamaAreaEmbargada(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $this->setTable($captura, $consulta);
            $retorno = $this->__default($captura, $consulta);
            if (is_string($retorno)) {
                $retorno = json_decode($retorno, true);
            }
            if (!empty($retorno[0]['dados'])) {
                return [$retorno[0]['dados']];
            }else{
                $result = [];
                if (empty($retorno)) {
                    return $result;
                }
                foreach ($retorno as $dado) {
                    if (isset($dado['dados'])) {
                            $result[]=  [ $dado['dados']];
                        }
                    }
                return [$result];
            }
        }

        $results = $this->__default($captura, $consulta, $resumo);
        $results = Funcoes::voToObject($results);
        $dados['dados'] = $results;

        return [[$dados]];
    }

    private function ibamaAreaEmbargadaOffline(CapturaModel $captura, $consulta, $resumo = false)
    {
        $dados = $this->__default($captura, $consulta, $resumo);
        if (is_string($dados)) {
            $dados = json_decode($dados, true);
        }
        return json_decode($dados, true);
    }

    private function cadastur(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }
        $dados['dados'] = $results;
        $dados['pdf'] = $results[0]['url'];

        return $dados;
    }

    private function serasaPep(CapturaModel $captura, $consulta, $resumo = false)
    {
        $retorno = $this->__default($captura, $consulta);

        if (!empty($consulta->data_faturamento)) {
            $dataCriacao = explode(" ", $consulta->data_faturamento);
        } else {
            if (!empty($consulta->data_estado)) {
                $dataCriacao = explode(" ", $consulta->data_estado);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                } else {
                    $dataCriacao = array();
                }
            }
        }

        if ($captura->data_migracao_lambda != '' && strtotime($captura->data_migracao_lambda) <= strtotime(
                @$dataCriacao[0]
            )) {
            //Lote Novo
            return $retorno;
        } else {
            //Lote Antigo
            if (empty($retorno)) {
                return array();
            }

            $retorno = Funcoes::voToObject($retorno);
            $newRetorno = array(
                'cpf' => (isset($retorno['cpf']) && !empty($retorno['cpf'])) ? $retorno['cpf'] : '',
                'tipo' => (isset($retorno['tipo']) && !empty($retorno['tipo'])) ? $retorno['tipo'] : '',
                'nome' => (isset($retorno['nome']) && !empty($retorno['nome'])) ? $retorno['nome'] : '',
                'nascimento' => (isset($retorno['data_nascimento']) && !empty($retorno['data_nascimento'])) ? $retorno['data_nascimento'] : '',
                'cargos' => array(),
                'relacionados' => array(),
            );
            if (isset($retorno['aCargo'])) {
                foreach ($retorno['aCargo'] as $cargo) {
                    if (
                        (isset($cargo['fim']) && !empty($cargo['fim']))
                        && (!isset($cargo['motivo']) || empty($cargo['motivo']))
                    ) {
                        $fimMotivo = explode(
                            '</td><td height="15" width="5"></td><td class="label1c" height="15" width="240">',
                            $cargo['fim']
                        );
                        $fim = $fimMotivo[0];
                        $motivo = (isset($fimMotivo[1]) && !empty($fimMotivo[1])) ? $fimMotivo[1] : '';
                    } else {
                        $fim = (isset($cargo['fim']) && !empty($cargo['fim'])) ? $cargo['fim'] : '';
                        $motivo = (isset($cargo['motivo']) && !empty($cargo['motivo'])) ? $cargo['motivo'] : '';
                    }
                    $newCargo = array(
                        'descricao' => (isset($cargo['descricao']) && !empty($cargo['descricao'])) ? $cargo['descricao'] : '',
                        'inicio' => (isset($cargo['inicio']) && !empty($cargo['inicio'])) ? $cargo['inicio'] : '',
                        'fim' => $fim,
                        'motivo' => $motivo,
                        'orgaoEmpresa' => (isset($cargo['orgao_empresa']) && !empty($cargo['orgao_empresa'])) ? $cargo['orgao_empresa'] : '',
                        'endereco' => (isset($cargo['endereco']) && !empty($cargo['endereco'])) ? $cargo['endereco'] : '',
                    );
                    $newRetorno['cargos'][] = $newCargo;
                }
            }
            if (isset($retorno['aRelacao'])) {
                foreach ($retorno['aRelacao'] as $relacao) {
                    /**
                     * Ta Nome e Relacionamento invertido mesmo por culpa da fonte antiga
                     */
                    $newRelacao = array(
                        'relacionamento' => (isset($relacao['nome']) && !empty($relacao['nome'])) ? $relacao['nome'] : '',
                        'documento' => (isset($relacao['cpf']) && !empty($relacao['cpf'])) ? $relacao['cpf'] : '',
                        'nome' => (isset($relacao['descricao']) && !empty($relacao['descricao'])) ? $relacao['descricao'] : '',
                    );
                    $newRetorno['relacionados'][] = $newRelacao;
                }
            }
            return $newRetorno;
        }
    }

    private function basePepSerasa(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $return = $this->__default($captura, $consulta);
            return $return;
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela)
            ->where($captura->tabela . '.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        return $dado;
    }

    private function receitaFederalSimplesNacionalInat(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join(
                $captura->tabela . '_referencia',
                $captura->tabela . '_referencia.id',
                '=',
                $captura->tabela . '_consulta.id_referencia'
            )
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function susepEmpresaPf(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->susepNomeLambda($captura, $consulta, $resumo = false);

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join(
                $captura->tabela . '_referencia',
                $captura->tabela . '_referencia.id',
                '=',
                $captura->tabela . '_consulta.id_referencia'
            )
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function susepEmpresaPj(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->susepNomeLambda($captura, $consulta, $resumo = false);

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join(
                $captura->tabela . '_referencia',
                $captura->tabela . '_referencia.id',
                '=',
                $captura->tabela . '_consulta.id_referencia'
            )
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function receitaFederalSimplesNacional(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $return = $this->__default($captura, $consulta);

            return $return;
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join(
                $captura->tabela . '_referencia',
                $captura->tabela . '_referencia.id',
                '=',
                $captura->tabela . '_consulta.id_referencia'
            )
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function bingCom(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('upminer');
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura['id']);
        $parsed = [];
        foreach ($IdsConsulta as $ids) {
            $dado = $db->table('documento')
                ->where('id_pesquisa_consulta', $ids->id)
                ->get();

            if (empty($dado[0])) {
                continue;
            }

            foreach ($dado as $value) {
                preg_match_all("/\((.*)\)/isu", $value->bags, $output_array);

                $bags = array();
                if (isset($output_array[1][0])) {
                    if (!is_array($output_array[1][0])) {
                        $dadosBags = str_replace(['""'], '', $output_array[1][0]);
                        preg_match_all('/"(.*?)"/isu', $dadosBags, $outbags);
                        $dadosBags = $outbags[1];
                    } else {
                        $dadosBags = $output_array[1][0];
                    }

                    foreach ($dadosBags as $dadosBag) {
                        $dadosBag = preg_replace('/\"/isu', '', $dadosBag);
                        $dadosBag = trim($dadosBag);
                        if (!empty($dadosBag)) {
                            $bags[] = $dadosBag;
                        }
                    }
                }
                $value->palavras_chaves = (!empty($bags)) ? $bags : "";
                $value->conteudo_relevante = $this->getRelevance($value->estado);
                $parsed[] = $value;
            }
        }

        if ($parsed == null) {
            return [];
        }
        return $parsed;
    }

    private function bingBrasil(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do bingCom
        return $this->bingCom($captura, $consulta, $resumo = false);
    }

    private function googleNews(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->genericSearchEngineGoogle($captura, $consulta, $resumo);
    }

    public function googleSearchEngineBrasil(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->genericSearchEngineGoogle($captura, $consulta, $resumo);
    }

    private function googleSearchEngine(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->genericSearchEngineGoogle($captura, $consulta, $resumo);
    }

    private function googleFormatPdfResult($captura, $consulta)
    {
        $arquivo = $this->__default($captura, $consulta);
    }

    private function genericSearchEngineGoogle(CapturaModel $captura, $consulta)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $dado = $this->getDadosSemRef($captura->id, $captura->tabela, $dossierID);

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        return $dado;
    }


    private function yahooBrasil(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $dado = $this->getDadosSemRef($captura->id, $captura->tabela, $dossierID);

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        $bags = explode("|", $dado[0]->bags);
        $dado[0]->palavras_chaves = (isset($bags[1])) ? explode(PHP_EOL, $bags[1]) : "";

        return $dado;
    }

    private function yahooCom(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do yahooBrasil
        return $this->yahooBrasil($captura, $consulta, $resumo = false);
    }

    private function InabilitadosBCBCpf(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $result = $this->__default($captura, $consulta);

            // a view ta chamando as variáveis com esse prefixo
            $prefix = 'InabilitadosBCB';

            $arr = (array)json_decode($result, true);

            $return = [];

            foreach ($arr as $item) {
                foreach ($item as $key => $value) {
                    $item[$prefix . $key] = $value;
                    unset($item[$key]);
                }

                $return[] = $item;
            }

            return collect($return)->toJson();
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('id_dado', 'data_hora', 'dado')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        $dado->dado = unserialize(pg_unescape_bytea(str_replace(["\\\\", '::bytea'], ["\\", ''], ($dado->dado))));
        $dado->dado = Funcoes::voToObject($dado->dado);

        $dado->dado = Funcoes::recreateArray($dado->dado);

        return $dado->dado;
    }

    private function receitaFederalCnd(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        // Verifica se tem resposta
        if ($results == null) {
            return [];
        }
        $results = is_string($results) ? json_decode($results, true) : $results;
        return [$results];
    }

    /** () CertidaoDebitosFederaisPGFN */
    private function CertidaoDebitosFederaisPGFN(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        if ($results == null) {
            return [];
        }
        $results = is_string($results) ? json_decode($results, true) : $results;
        return [$results];
    }

    /** () entidadesRelacionadas */
    private function entidadesRelacionadas(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        if ($results == null) {
            return [];
        }
        $results = is_string($results) ? json_decode($results, true) : $results;
        return [$results];
    }

    /** () receitaFederalCnd */
    private function receitaFederalCnd2Via(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);

        if ($results == null) {
            return [];
        }
        $results = is_string($results) ? json_decode($results, true) : $results;
        return [$results];
    }

    private function SpcQsaCnpj(CapturaModel $captura, $consulta, $resumo = false)
    {
        $response = $this->__default($captura, $consulta, $resumo);

        if ($response == '') {
            return false;
        }

    if(is_string($response)){
        $response = json_decode($response, true);
    }


        $socios = [];
        if (isset($response['socio']['detalhe-socio'])) {
            $socios = (isset($response['socio']['detalhe-socio'][0])) ? $response['socio']['detalhe-socio'] : [$response['socio']['detalhe-socio']];
        }

        $admins = [];
        if (isset($response['administrador']['detalhe-administrador'])) {
            $admins = (isset($response['administrador']['detalhe-administrador'][0])) ? $response['administrador']['detalhe-administrador'] : array($response['administrador']['detalhe-administrador']);

            foreach ($admins as $adkey => $value) {
                $admins[$adkey]['valor-participacao'] = '';
            }
        }

        //checa se é Socio e Administrador
        $check_socio_admin = [];
        foreach ($admins as $key => $admin) {
            if (!is_array($admin)) {
                continue;
            }
            if (isset($admin['documento'])) {
                $check_socio_admin[$admin['documento']] = $key;
            }
        }

        foreach ($socios as $key => $socio) {
            if (isset($socio['documento']) && array_key_exists($socio['documento'], $check_socio_admin)) {
                $socios[$key]['cargo-administracao'] .= '-ADMINISTRADOR';
                $socios[$key]['tipo-relacionamento'] .= '-ADMINISTRADOR';
                unset($admins[$check_socio_admin[$socio['documento']]]);
            }
        }

        $resultMerge = array_merge($socios, $admins);

        return $resultMerge;
    }

    private function SpcParticipacaoEmpresas(CapturaModel $captura, $consulta, $resumo = false)
    {
        $response = $this->__default($captura, $consulta, $resumo);

        $response = !is_array($response) ? json_decode($response, true) : $response;
        if (!is_array($response)) {
            $response = json_decode($response, true);
        }

        if ($response == '' || (isset($response['success']) && $response['success'] == '')) {
            return false;
        }

        return $response['empresas'];
    }

    private function BoaVistaScpcNet(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dossieID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $referencias = $consulta->getReferenciasDaCaptura($captura->id, $dossieID);

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('id_dado', 'data_hora', 'dado')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->whereIn($captura->tabela . '_consulta.id', $referencias)
            ->first();

        if (empty($dado) || empty($dado->dado)) {
            return [];
        }

        $dado->dado = json_decode($dado->dado, true);

        // transformação necessaria para não quebrar formato antigo salvo e funcionar o lambda
        if (isset($dado->dado['data'])) {
            $dado->dado = json_decode($dado->dado['data'], true);
        }

        if (!empty($dado->dado['consultas_anteriores'])) {
            $dado->dado['consultas_anteriores'] = $this->breakIntoColuns(2, $dado->dado['consultas_anteriores']);
        }

        if (!empty($dado->dado['consultas_anteriores_telefone'])) {
            $dado->dado['consultas_anteriores_telefone'] = $this->breakIntoColuns(
                6,
                $dado->dado['consultas_anteriores_telefone']
            );
        }

        if (!empty($dado->dado['consultas_anteriores_pj'])) {
            $dado->dado['consultas_anteriores_pj'] = $this->breakIntoColuns(2, $dado->dado['consultas_anteriores_pj']);
        }

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        return $dado->dado;
    }

    private function cfmNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // if(!isset($consulta->referencia) || empty($consulta->referencia)) return false;

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $arquivo = $this->__default($captura, $consulta, $resumo);

        // Verifica se tem resposta
        if (empty($arquivo)) {
            return [];
        }

        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        $newDados = array();

        foreach ($arquivo as $key => $dados) {
            foreach ($dados as $k => $dado) {
                if (is_array($dado)) {
                    foreach ($dado as $j => $d) {
                        $replace = array('\0', 'CFM_vo');
                        $newK = str_replace($replace, '', $k);
                        $newj = str_replace($replace, '', $j);
                        $newDados[$key][$newK][$newj] = $d;
                    }
                } else {
                    $replace = array('\0', 'CFM_vo');
                    $newK = str_replace($replace, '', $k);
                    $newDados[$key][$newK] = $dado;
                }
            }
        }

        // foreach ($arquivo as $key => $value) {
        //     foreach ($value as $k => $v) {
        //         if(!is_array($v)) $arquivo[$key][$k] = pg_unescape_bytea($v); //iconv(mb_detect_encoding($v),"ISO-8859-1//TRANSLIT", $v); //utf8_encode($v);
        //     }
        // }

        return $newDados;
    }

    private function fibraPepNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        $arquivo = stream_get_contents($dado->dado);

        $arquivo = pg_unescape_bytea($arquivo);

        $arquivo = str_replace('\000CFM_vo\000', '', $arquivo);

        $arquivo = @preg_replace('!s:(\d+):"(.*?)";!e', "'s:'.strlen('$2').':\"$2\";'", $arquivo);

        $arquivo = unserialize($arquivo);
        $arquivo = Funcoes::voToObject($arquivo);

        foreach ($arquivo as $key => $value) {
            foreach ($value as $k => $v) {
                if (!is_array($v)) {
                    $arquivo[$key][$k] = pg_unescape_bytea($v);
                } //iconv(mb_detect_encoding($v),"ISO-8859-1//TRANSLIT", $v); //utf8_encode($v);
            }
        }

        return $arquivo;
    }

    private function cefFgts(CapturaModel $captura, $consulta, $resumo = false)
    {
        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dossieID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $referencias = $consulta->getReferenciasDaCaptura($captura->id, $dossieID);

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->whereIn($captura->tabela . '_consulta.id', $referencias)
            ->first();

        if ($dado == null) {
            return '';
        }

        $arquivo = $dado->dado;

        return $arquivo;
    }

    private function kurrier(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');
        $criterio = explode("|", $consulta->criterio);

        $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);

        $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;

        if (isset($_GET['is_workflow'])) {
            $parsedData = [];
        } else {
            $parsedData = array(
                "tipos" => array(),
                "tipoQtd" => array(),
                "instancias" => array(),
                "instanciasQtd" => array(),
                "valorMin" => array(),
                "valorMax" => array(),
                "valores" => array(),
                "ufs" => array(),
                "ufQtd" => array(),
                "classes" => array(),
                "classesQtd" => array(),
                "descricoes" => array()
            );
            $replaceMoney = array("R", "$", ".");
        }

        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            # code...
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->orderBy('id')->get();
            }

            if (empty($dados)) {
                continue;
            }

            if (!isset($_GET['is_workflow'])) {
                $totalProcessos = count($dados);

                if ($resumo) {
                    return $totalProcessos;
                }

                $parsedData["totalProcessos"] = $totalProcessos;

                if (is_array($dados)) {
                    usort($dados, [$this, 'sortByRelevancia']);
                }
            }

            foreach ($dados as $k => &$d) {
                if (Funcoes::isJson($d->dado)) {
                    $d->dado = json_decode($d->dado);
                } else {
                    try {
                        $escapedData = stream_get_contents($d->dado);
                        $escapedData = json_decode($escapedData);
                    } catch (Exception $e) {
                        $escapedData = pg_unescape_bytea($d->dado);
                        $escapedData = json_decode($escapedData);
                    }

                    if ($escapedData === false && !is_array($escapedData)) {
                        $escapedData = pg_unescape_bytea(pg_unescape_bytea($d->dado));
                        $escapedData = json_decode($escapedData);
                        $d->dado = $escapedData;
                    } else {
                        $d->dado = $escapedData;
                    }
                }

                if (isset($_GET['is_workflow'])) {
                    $parsedData[] = $d->dado;
                } else {
                    if (isset($d->dado->partes)) {
                        // Seta a parte mais proxima DBJusNomeDetalhado
                        $parteMaisProxima = $this->parteMaisProxima($nome, $nomes, $d->dado->partes);

                        foreach ($parteMaisProxima['descricoes']['descricao'] as $key => $qtdDesc) {
                            @$parsedData['descricao'][$key] += $qtdDesc;
                        }

                        foreach ($parteMaisProxima['descricoes']['descricoes'] as $key => $nameDesc) {
                            if (!in_array($nameDesc, $parsedData['descricoes'])) {
                                $parsedData['descricoes'][] = $nameDesc;
                            }
                        }

                        if (isset($parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'])) {
                            @$d->dado->nome_mais_proximo['nome'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'];
                            @$d->dado->nome_mais_proximo['tipo'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['descricao'];
                        }
                    }

                    if (isset($d->dado->instancias[0]->nome)) {
                        @$parsedData['instanciasQtd'][$d->dado->instancias[0]->nome]++;
                        if (!in_array($d->dado->instancias[0]->nome, $parsedData['instancias'])) {
                            $parsedData['instancias'][] = $d->dado->instancias[0]->nome;
                        }
                        if (empty($d->dado->instancias[0]->classes)) {
                            $d->dado->instancias[0]->classes = 'Não Informada';
                        }

                        if (!is_array($d->dado->instancias[0]->classes)) {
                            @$parsedData['classesQtd'][$d->dado->instancias[0]->classes]++;
                            if (!in_array($d->dado->instancias[0]->classes, $parsedData['classes'])) {
                                $parsedData['classes'][] = $d->dado->instancias[0]->classes;
                            }
                        }
                    } else {
                        @$parsedData['classesQtd']['Não Informada']++;
                        if (!in_array('Não Informada', $parsedData['classes'])) {
                            $parsedData['classes'][] = 'Não Informada';
                        }
                    }

                    if (empty($d->dado)) {
                        dd($k);
                    }
                    continue;

                    @$parsedData['ufQtd'][$d->dado->uf]++;
                    if (!in_array($d->dado->uf, $parsedData['ufs'])) {
                        $parsedData['ufs'][] = $d->dado->uf;
                    }

                    if (isset($d->dado->detalhes)) {
                        //Limita o numero de diarios para 25
                        if (isset($d->dado->detalhes->diarios) && count($d->dado->detalhes->diarios) > 25) {
                            $diarios = array_chunk($d->dado->detalhes->diarios, 25);
                            $d->dado->detalhes->diarios = $diarios[0];
                            unset($diarios);
                        }

                        //Limita o numero de andamentos para 25
                        if (isset($d->dado->detalhes->andamentos) && count($d->dado->detalhes->andamentos) > 25) {
                            $andamentos = array_chunk($d->dado->detalhes->andamentos, 25);
                            $d->dado->detalhes->andamentos = $andamentos[0];
                            unset($andamentos);
                        }

                        if (strlen($d->dado->detalhes->valor) > 1) {
                            $d->dado->valor_causa_value = trim(
                                str_replace(",", "", str_replace($replaceMoney, "", $d->dado->detalhes->valor))
                            );
                        }

                        if (strlen($d->dado->detalhes->valor) > 1 and !preg_match(
                                "/c/i",
                                $d->dado->detalhes->valor,
                                $output
                            )) {
                            $parsedData["valores"][] = trim(
                                str_replace(",", ".", str_replace($replaceMoney, "", $d->dado->detalhes->valor))
                            );
                        }
                    }

                    if (!empty($d->dado->partes)) {
                        $d->dado->partes = $this->breakIntoColuns(2, $d->dado->partes);
                    }
                }
            }

            if (!isset($_GET['is_workflow'])) {
                $dados['id_pesquisa_consulta'] = $dados[0]->id_pesquisa_consulta;

                unset($dados['id_pesquisa_consulta']);

                $parsedData["dados"] = $dados;


                if (empty($parsedData['valores'])) {
                    $parsedData['valores'][] = 0;
                }

                $parsedData["valorMax"]["mask"] = max($parsedData["valores"]);
                $parsedData["valorMin"]["mask"] = min($parsedData["valores"]);
                $parsedData["valorMax"]["value"] = trim(
                    str_replace(",", ".", str_replace($replaceMoney, "", $parsedData["valorMax"]["mask"]))
                );
                $parsedData["valorMin"]["value"] = trim(
                    str_replace(",", ".", str_replace($replaceMoney, "", $parsedData["valorMin"]["mask"]))
                );


                // foreach ($parsedData["dados"] as &$dados) {
                //     foreach ($dados as &$dado) {
                //         if(is_array($dado))  usort($dado, [$this, 'sortByRelevancia']);
                //     }
                // }

                // $parsedData["valoresMask"] = array_unique($parsedData["valoresMask"]);
                $parsedData["valores"] = array_unique($parsedData["valores"]);
                sort($parsedData["valores"]);

                if (!isset($parsedData["valoresMask"])) {
                    $parsedData["valoresMask"] = array();
                }

                foreach ($parsedData["valores"] as $valor) {
                    if ($valor > 0) {
                        $parsedData["valoresMask"][] = "R$" . number_format($valor, 2, ',', '.');
                    }
                }

                if (count($parsedData["classes"]) > 1 || count($parsedData["valores"]) > 1 || count(
                        $parsedData["tipos"]
                    ) > 1 || count($parsedData["instancias"]) > 1 || count($parsedData["ufs"]) > 1) {
                    $parsedData["showFilter"] = true;
                }
            }
        }

        if (isset($_GET['download']) || isset($_GET['debug'])) {
            $parsedData["showFilter"] = false;
        }

        return $parsedData;
    }

    private function BaseOffEscavadorV2(CapturaModel $captura, $consulta, $resumo = false)
    {
        // Banco do upMiner
        $db = DB::connection('upminer');
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        $parsedData = array();

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $value->dado->conteudo_relevante = $this->getRelevance(
                    $value->estado
                );
                $parsedData[] = $value->dado;
            }
        }

        return $parsedData;
    }

    private function BaseOffEscavador(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');
        $criterio = explode("|", $consulta->criterio);

        $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);

        $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;

        $parsedData = [];

        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $dados = $db->table($captura->tabela)->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id')->get();

            if (empty($dados)) {
                continue;
            }

            foreach ($dados as $k => &$d) {
                if (Funcoes::isJson($d->dado)) {
                    $d->dado = json_decode($d->dado);
                } else {
                    try {
                        $escapedData = stream_get_contents($d->dado);
                        $escapedData = json_decode($escapedData);
                    } catch (Exception $e) {
                        $escapedData = pg_unescape_bytea($d->dado);
                        $escapedData = json_decode($escapedData);
                    }

                    if ($escapedData === false && !is_array($escapedData)) {
                        $escapedData = pg_unescape_bytea(pg_unescape_bytea($d->dado));
                        $escapedData = json_decode($escapedData);
                        $d->dado = $escapedData;
                    } else {
                        $d->dado = $escapedData;
                    }
                }

                $d->dado->conteudo_relevante = $this->getRelevance($d->estado);
                $parsedData[] = $d->dado;
            }
        }

        return $parsedData;
    }

    /**
     * Recupera e formata os dados da fonte DBJusNomeDetalhado
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return void
     * <AUTHOR>
     * Revisão Jonathan Maria Machado - 2018-12-18 - Ajustes para PSR-2
     *                                             - Revisão nas variáveis e regras, ajuste
     *                                               de bugs entre os indices dos arrays.
     *                                             - Resolução de bug: Quando não havia dados em um dos resultados dos parametros,
     *                                               ignorava os outros resultados e retornava um array vazio
     *
     * Revisão Jefferson Mesquita 24/05/2019 - Troca da função getConsultasIds por getConsultasIdsByIdPesquisaConsulta
     * @version 1.0.0
     */
    private function DBJusNomeDetalhado(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = [];

        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table($captura->tabela)->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');
            $dados = $query->get();
            unset($query);

            if (empty($dados)) {
                continue;
            }

            foreach ($dados as $k => &$d) {
                if (Funcoes::isJson($d->dado)) {
                    $d->dado = json_decode($d->dado);
                } else {
                    try {
                        $escapedData = stream_get_contents($d->dado);
                        $escapedData = json_decode($escapedData);
                    } catch (Exception $e) {
                        $escapedData = pg_unescape_bytea($d->dado);
                        $escapedData = json_decode($escapedData);
                    }

                    if ($escapedData === false && !is_array($escapedData)) {
                        $escapedData = pg_unescape_bytea(pg_unescape_bytea($d->dado));
                        $escapedData = json_decode($escapedData);
                        $d->dado = $escapedData;
                    }
                    $d->dado = $escapedData;
                }

                $d->dado->conteudo_relevante = $this->getRelevance($d->estado);
                $parsedData[] = $d->dado;
            }
        }


        if (isset($_GET['download'])
            || isset($_GET['debug'])
        ) {
            $parsedData["showFilter"] = false;
        }

        return $parsedData;
    }

    private function DBJusNomeResumido(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();

        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);
        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();

            foreach ($data as $key => &$dado) {
                if (Funcoes::isJson($dado->dado)) {
                    $dado->dado = json_decode($dado->dado);
                } else {
                    try {
                        $escapedData = stream_get_contents($dado->dado);
                        $escapedData = json_decode($escapedData);
                    } catch (Exception $e) {
                        $escapedData = pg_unescape_bytea($dado->dado);
                        $escapedData = json_decode($escapedData);
                    }

                    if ($escapedData === false && !is_array($escapedData)) {
                        $escapedData = pg_unescape_bytea(pg_unescape_bytea($dado->dado));
                        $escapedData = json_decode($escapedData);
                        $dado->dado = $escapedData;
                    }
                    $dado->dado = $escapedData;
                }
                $dado->dado->conteudo_relevante = $this->getRelevance($dado->estado);
                $parsedData[] = $dado->dado;
            }
        }

        return $parsedData;
    }

    private function procurados(CapturaModel $captura, $consulta, $resumo = false)
    {
        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return '';
        }

        $arquivo = base64_decode($dado->dado);

        return $arquivo;
    }

    private function detranSp(CapturaModel $captura, $consulta, $resumo = false)
    {
        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return '';
        }

        // $arquivo = pg_unescape_bytea(pg_unescape_bytea($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function basePepCpf(CapturaModel $captura, $consulta, $resumo = false)
    {
        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela)
            ->select('dado', 'data_hora')
            ->where($captura->tabela . '.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return '';
        }

        return $dado;
    }

    private function basePepNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela)
            ->where($captura->tabela . '.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return '';
        }

        return $dado;
    }

    private function ofac(CapturaModel $captura, $consulta, $resumo = false)
    {
        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        if (!$dado = $db->table($captura->tabela)->where('id', $consulta->referencia)->get()) {
            return [];
        }

        $response = [];

        if ($res = $db->table($captura->tabela . '_nascimento')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['data_nascimento'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_local_nascimento')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['local_nascmento'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_cidadania')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['cidadania'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_programa')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['programa'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_id')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['id'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_endereco')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['endereco'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_aka')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['aka'][] = $v;
            }
        }

        if ($res = $db->table($captura->tabela . '_vessel')->where('id', $dado->id)->get()) {
            foreach ($res as $v) {
                $response['vessel'][] = $v;
            }
        }

        // Verifica se tem resposta
        if ($response == null) {
            return '';
        }

        return $response;
    }

    private function anp(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join(
                $captura->tabela . '_referencia',
                $captura->tabela . '_referencia.id',
                '=',
                $captura->tabela . '_consulta.id_referencia'
            )
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();

        // Verifica se tem resposta
        if ($dado == null) {
            return [];
        }

        // $arquivo = pg_unescape_bytea(stream_get_contents($dado->dado));
        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }

    private function SpcScore_3(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return [$arquivo];
    }

    private function SpcScore_12(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_48(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }
        return $arquivo;
    }

    private function BoavistaScore_23(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_51(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_14(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_28(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_52(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }
        return $arquivo;
    }

    private function BoavistaScore_81(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }
        return $arquivo;
    }

    private function BoavistaScore_79(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }
        return $arquivo;
    }

    private function BoavistaScore_11(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_37(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }
        if (!isset($arquivo['resultado'])) {
            return array();
        }

        return $arquivo;
    }

    private function BoavistaScore_9(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }

    private function BoavistaScore_8(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }
        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }

        return $arquivo;
    }


    private function anvisaFarmaciaDrogaria(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $this->setTable($captura, $consulta);
            $retorno = $this->__default($captura, $consulta);
            if (is_array($retorno)) {
                return $retorno;
            }
            $retorno = json_decode($retorno, true);

            return $retorno;
        }

        $arquivo = Funcoes::voToObject($this->__default($captura, $consulta));

        // $consulta->referencia = 128519;
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        return $arquivo;
    }

    private function anvisaConsultaProcessos(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            if (is_array($arquivo)) {
                return $arquivo;
            }
            return json_decode($arquivo, true);
        }

        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        if (count(json_decode($arquivo, true)) > 0) {
            $arquivo = Funcoes::voToObject($this->__default($captura, $consulta));
            $arquivo['html'] = Funcoes::descompactaHtml($arquivo['html']);
        }

        return $arquivo;
    }


    private function cvmConsolidaFundo(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if ($captura->pdf) {
            $_arquivo = json_decode($arquivo, true);

            if (empty($_arquivo) && !isset($_arquivo[0]['regulamento'])) {
                return $arquivo;
            }

            $_arquivo = collect($_arquivo);
            $pdfs = collect([
                $_arquivo->pluck('regulamento.*.pdf'),
                $_arquivo->pluck('demonstracao_contabil.*.pdf')
            ])
                ->flatten(2)
                ->filter();

            $arquivo = $_arquivo->put('pdf', $pdfs)->toJson();
        }

        return $arquivo;
    }

    private function cvmCadastroParticipantes(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            return is_string($arquivo) ? json_decode($arquivo, true) : $arquivo;
        }

        $arquivo = Funcoes::voToObject($arquivo);

        return $arquivo;
    }

    private function cvmCadastroHtml(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            return is_string($arquivo) ? json_decode($arquivo, true) : $arquivo;
        }

        $arquivo = Funcoes::voToObject($arquivo);

        return $arquivo;
    }

    private function informacaoJuridicaDocumento(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (is_array($this->__default($captura, $consulta, $resumo))) {
            $arquivo = $this->__default($captura, $consulta, $resumo);
        } else {
            $arquivo = json_decode($this->__default($captura, $consulta, $resumo), true);
        }
        if (empty($arquivo)) {
            return [];
        }
        if (!isset($arquivo['resultados'][0])) {
            $arquivo['resultados'] = array($arquivo['resultados']);
        }

        return $arquivo;
    }

    private function informacaoJuridicaNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->informacaoJuridicaDocumento($captura, $consulta, $resumo);
    }

    private function dtec_flex_paga(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        if (is_string($arquivo)) {
            $arquivo = json_decode($arquivo, true);
        }
        return !empty($arquivo['data']) ? $arquivo['data'] : $arquivo;
    }

    private function dtec_flex_privada(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao da dtec_flex_paga
        return $this->dtec_flex_paga($captura, $consulta, $resumo);
    }

    private function dtec_amb_paga(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao da dtec_flex_paga
        return $this->dtec_flex_paga($captura, $consulta, $resumo);
    }

    private function dtec_amb_privada(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao da dtec_flex_paga
        return $this->dtec_flex_paga($captura, $consulta, $resumo);
    }

    private function dtec_paga(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao da dtec_flex_paga
        return $this->dtec_flex_paga($captura, $consulta, $resumo);
    }

    private function dtec_privada(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao da dtec_flex_paga
        return $this->dtec_flex_paga($captura, $consulta, $resumo);
    }

    private function ppeTitularPaga(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo);

        if (!isset($arquivo->data->identificacaoPpeTitular->cpf) && !isset($arquivo->data->identificacaoPpeTitular->mensagem)) {
            return array();
        }

        // if(isset($arquivo->data->relacionados)){
        //     $arquivo->relacionados = $this->breakIntoColuns(2, $arquivo->data->relacionados);
        // }

        return $arquivo;
    }

    /**
     * Pega os dados baseados na referencia
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     * @return void
     * @version 1.0.0 - Sem Nome
     *          1.1.0 - Mudanças para lambda e validação para tratamento antigo com lambda
     */
    private function policiaFederalAntecedenteCriminal(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta, $resumo);

        if (empty($arquivo)) {
            return array();
        }

        if (!empty($consulta->data_estado)) {
            $dataCriacao = explode(" ", $consulta->data_estado);
        } else {
            if (!empty($consulta->data_faturamento)) {
                $dataCriacao = explode(" ", $consulta->data_faturamento);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                }
            }
        }

        if (!empty($captura->data_migracao_lambda)
            && isset($dataCriacao[0])
            && !empty($dataCriacao[0])
            && strtotime($captura->data_migracao_lambda) <= strtotime($dataCriacao[0])
        ) {
            $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);

            return $arquivo['data'] ?? $arquivo;
        }

        $arquivo = Funcoes::voToObject($arquivo);

        if (!isset($arquivo['aviso'])) {
            $arquivo['aviso'] = '';
        }

        if (!empty($arquivo['aviso'])) {
            $arquivo['aviso'] = utf8_encode($arquivo['aviso']);
            $arquivo['aviso'] = str_replace(array('&atilde;o', '&ecirc;'), array('ão', 'ê'), $arquivo['aviso']);
        }

        return $arquivo;
    }

    /**
     * Pega os dados baseados na referencia
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     * @return void
     * @version 1.0.0 - Função oabCna
     */
    private function oabCna(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta, $resumo);

        if (empty($arquivo)) {
            return array();
        }

        if (!empty($consulta->data_estado)) {
            $dataCriacao = explode(" ", $consulta->data_estado);
        } else {
            if (!empty($consulta->data_faturamento)) {
                $dataCriacao = explode(" ", $consulta->data_faturamento);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                }
            }
        }

        if (!empty($captura->data_migracao_lambda)
            && isset($dataCriacao[0])
            && !empty($dataCriacao[0])
            && strtotime($captura->data_migracao_lambda) <= strtotime($dataCriacao[0])
        ) {
            $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);
            return $arquivo;
        }

        $arquivo = Funcoes::voToObject($arquivo);

        return $arquivo;
    }

    private function mpfRelevancia(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $dossieID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;

        $pesquisaConsuta = PesquisaConsulta::where('id_pesquisa_consulta', $dossieID)->where('captura', $captura->id)->first();

        if ($pesquisaConsuta) {
            $dossieID = $pesquisaConsuta->id;
        }

        $dados = $db->table($captura->tabela)->select()
            ->where('id_pesquisa_consulta', '=', $dossieID)
            ->where('id_captura', '=', $captura->id)
            ->orderBy('id', 'DESC')->get()->toArray();

        $data = array();
        if (is_array($dados) && count($dados) > 0) {
            if ($resumo) {
                return count($dados);
            }
            foreach ($dados as $key => $dado) {
                $parsedDado = json_decode($dado->dado, true);
                if (!empty($parsedDado['detalhes'][0]['num_processo'])) {
                    $parsedDado['conteudo_relevante'] = $this->getRelevance($dado->estado);
                    $data[] = $parsedDado;
                }
            }
            usort($data, function ($a, $b) {
                return ($a < $b) ? -1 : 1;
            });
        }
        return $data;
    }


    /**
     * Função para pegar resultados dos diários oficiais
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     *
     * @return array
     */
    private function diariosOficiais(CapturaModel $captura, $consulta, $resumo = false)
    {
        $company = null;

        if (!is_null(session()->get('user'))) {
            $company = session()->get('user')->company;
            if (empty($company)) {
                throw new CustomException('Não foi possível definir o cliente');
            }

            $companyUser = Usuario::where('login', $company)->first();

            if (empty($companyUser)) {
                throw new CustomException('Não foi possível definir o companyUser');
            }
        }

        $db = DB::connection('upminer');

        $parametrizacao = $db->table("captura_estrutura_lote_consulta")->select()
            ->where('id_lote_consulta', '=', $consulta->id_lote_consulta)
            ->where('id_captura', '=', $consulta->captura)
            ->first();

        if (isset($parametrizacao)) {
            $parametrizacao = explode('|', $parametrizacao->criterio);
        }

        $palavras = array();
        if (isset($parametrizacao[1]) && !empty($parametrizacao[1])) {
            $palavras = explode(',', $parametrizacao[1]);
        }
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);
        $maisResultados = false;

        $newDados = [];
        foreach ($IdsConsulta as $key => $ids) {
            $arrIdPesquisaConsulta = [$ids->id];
            if (!empty($ids->id_pesquisa_consulta)) {
                $arrIdPesquisaConsulta[] = $ids->id_pesquisa_consulta;
            }

            $dados = $db->table("processo_classificacao")
                ->whereIn('id_pesquisa_consulta', $arrIdPesquisaConsulta)
                ->where('id_captura', $captura->id);
            if ($captura->pdf) {
                $dados->where('estado', 'R');
            }

            $dados = $dados->orderBy('id', 'DESC')->get();

            if (empty($dados)) {
                continue;
            }

            $dados = $dados->toArray();

            foreach ($dados as $key => &$dado) {
                if (
                    isset($companyUser)
                    && $companyUser->dossie_service_version == 2
                    && gettype($dado->dado) == 'string'
                ) {
                    $dado->dado = json_decode($dado->dado);
                } else {
                    if (@pg_unescape_bytea($dado->dado)) {
                        $dado->dado = pg_unescape_bytea($dado->dado);
                        $dado->dado = json_decode($dado->dado);
                    } else {
                        $dado->dado = stream_get_contents($dado->dado);
                        $dado->dado = json_decode($dado->dado);
                    }
                }

                if (empty($dado->dado)) {
                    continue;
                }

                if (!empty($dado->dado->data)) {
                    $dado->dado->data = str_replace("\\", "", $dado->dado->data);
                }

                if (!empty($dado->dado->url)) {
                    $dado->dado->url = str_replace("\\", "", $dado->dado->url);
                }

                if (
                    !$maisResultados
                    && isset($dado->dado->maisResultados)
                    && $dado->dado->maisResultados
                ) {
                    $maisResultados = $dado->dado->maisResultados;
                }

                if ($dado->estado == '') {
                    $dado->estado = $dado->dado->estado;
                } else {
                    $dado->dado->estado = $dado->estado;
                }

                $dado->dado->estado = $this->regexFixString($dado->dado->estado);

                $dado->dado->snippet = utf8_encode(pg_unescape_bytea(pg_unescape_bytea($dado->dado->snippet)));

                $dado->dado->snippet = $this->regexFixString($dado->dado->snippet);

                $dado->dado = json_decode(json_encode($dado->dado), true);
                $conteudoRelevante = $this->getRelevance($dado->estado);
                $dado->dado['caderno'] = $this->regexFixString($dado->dado['caderno']);
                $newDados[] = [
                    'conteudo_relevante' => $conteudoRelevante,
                    "data" => $dado->dado['data'],
                    "texto" => $dado->dado['snippet'],
                    "estado" => $dado->dado['estado'],
                    "pagina" => $dado->dado['pagina'],
                    "caderno" => $dado->dado['caderno'],
                    "posicao" => null,
                    "filename" => null,
                    "link_pdf" => $dado->dado['url'],
                    "relevancia" => null,
                    "maisResultados" => $dado->dado['maisResultados'],
                ];
            }
        }

        return [
            "max_result" => null,
            "resultados" => $newDados,
            "total_docs" => null,
            "search_time" => null,
            "pagina_atual" => null,
            "total_pagina" => null,
        ];
    }

    private function regexFixString($value)
    {
        return preg_replace_callback(
            '/u([0-9a-fA-F]{4})/',
            fn($match) => mb_convert_encoding('&#x' . $match[1] . ';', 'UTF-8', 'HTML-ENTITIES'),
            $value
        );
    }

    /**
     * Função para pegar resultados dos diários oficiais paga
     *
     * @param CapturaModel $captura
     * @param array $consulta
     * @param boolean $resumo
     *
     * @return array
     */
    private function diariosOficiaisPaga(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->diariosOficiais($captura, $consulta, $resumo);
    }

    private function cade(CapturaModel $captura, $consulta, $resumo = false)
    {
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        // Banco do upMiner
        $db = DB::connection('upminer')
            ->table($captura->tabela)
            ->select(['dado', 'estado', 'id'])
            ->whereIn('id_pesquisa_consulta', collect($IdsConsulta)->pluck('id'))
            ->whereIdCaptura($captura->id);

        if ($resumo) {
            return (int)$db->count();
        }

        $dados = collect($db->orderBy('id')->get());

        if ($dados->isEmpty()) {
            return [];
        }

        $decode = function ($data) {
            return json_decode($data, false, JSON_UNESCAPED_UNICODE);
        };

        if (!$this->isNewLambdaCaptura($captura, $consulta)) {
            $decode = function ($data) {
                return json_decode(pg_unescape_bytea($data));
            };
        }

        $results = [];

        foreach ($dados as $dado) {
            $dado->dado = $decode($dado->dado);
            foreach ($dado->dado as $key => $value) {
                $key = preg_replace('/[^0-9]/', '', $key);
                $value->conteudo_relevante = $this->getRelevance($dado->estado);
                $newData = [$key => $value];
                $results[] = $newData;
            }
        }

        return collect($results)->toArray();
    }


    private function dadosCadastraisArg(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        // $arquivo = pg_unescape_bytea($arquivo);
        // $arquivo = @unserialize(pg_unescape_bytea($arquivo));
        $arquivo = Funcoes::RdsCaptureData($arquivo);
        $arquivo = @unserialize($arquivo);
        if (strpos($arquivo, "a:") === 0) {
            return Funcoes::voToObject($arquivo);
        }
        return $arquivo;
    }


    private function listaOnu(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (empty($arquivo)) {
            return array();
        }

        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);

        if ($resumo && isset($arquivo["hits"]["hits"])) {
            return count($arquivo["hits"]["hits"]);
        } else {
            if ($resumo) {
                return count($arquivo);
            }
        }

        if (isset($arquivo["hits"]["hits"])) {
            $arquivo = $arquivo["hits"]["hits"];
        }

        return $arquivo;
    }

    private function cadastroPf(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);
        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);

        if (isset($arquivo["telefones"][0])) {
            $telefones = $this->breakIntoColuns(4, $arquivo["telefones"]);
            $arquivo["telefones"] = $telefones;
        }

        if (isset($arquivo["emails"][0])) {
            $emails = $this->breakIntoColuns(3, $arquivo["emails"]);
            $arquivo["emails"] = $emails;
        }


        return $arquivo;
    }

    private function cadastroPfNova(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);
        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);

        if (isset($arquivo["telefones"][0])) {
            $telefones = $this->breakIntoColuns(4, $arquivo["telefones"]);
            $arquivo["telefones"] = $telefones;
        }

        if (isset($arquivo["emails"][0])) {
            $emails = $this->breakIntoColuns(3, $arquivo["emails"]);
            $arquivo["emails"] = $emails;
        }

        return $arquivo;
    }

    private function cadastroPj(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);
        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);

        if (isset($arquivo["telefones"][0])) {
            $telefones = $this->breakIntoColuns(4, array_slice($arquivo["telefones"], 0, 12));
            $arquivo["telefones"] = $telefones;
        }

        return $arquivo;
    }

    private function cadastroPjNovo(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);
        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);
        if (!empty($arquivo["telefones"])) {
            $telefones = $this->breakIntoColuns(4, array_slice($arquivo["telefones"], 0, 12));
            $arquivo["telefones"] = $telefones;
        }

        return $arquivo;
    }

    private function qsaBoaVista(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);

        if (isset($arquivo["telefones"])) {
            $telefones = $this->breakIntoColuns(4, $arquivo["telefones"]);
            $arquivo["telefones"] = $telefones;
        }


        return $arquivo;
    }

    private function chequeSemFundo(CapturaModel $captura, $consulta, $resumo = false)
    {
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $return = $this->__default($captura, $consulta);
            if (is_string($return)) {
                $return = json_decode($return, true);
            }
            return !empty($return) ? ['aCCF' => $return] : [];
        }

        $db = DB::connection('pgsql_captura3');

        $dado = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia) // 411205
            ->first();

        if ($dado == null) {
            return 0;
        }

        $dados = unserialize(Funcoes::RdsCaptureData($dado->dado));

        $dados = Funcoes::voToObject($dados);

        $parsedDat = [];

        foreach ($dados[0] as $key => $value) {
            $parsedData['aCCF'] = $value;
        }

        if ($resumo) {
            return count($parsedData['aCCF']);
        }

        return $parsedData;
    }

    /**
     * Retorna as informações da consulta da fonte 130 - Pessoa Gold Boa vista para a view
     * Revisão: William Macedo 22/05/2018 - Adicionado ao retorno parametros selecionados para a busca
     * @param CapturaModel $captura
     * @param string $consulta json da consulta
     * @param boolean $resumo não é utilizado nesse método
     * @return array
     * @version 1.0.0
     *
     */
    private function PessoaGoldBoaVista(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta);
        $arquivo = is_array($arquivo) ? $arquivo : json_decode($arquivo, true);

        $arquivo = $this->fixSeparatedArrayPessoaGold($arquivo);

        $segmentos = $arquivo["informations"]["consultas_segmento"] ?? false;

        if ($segmentos) {
            for ($i = 3; $i < count($segmentos); $i++) {
                if (isset($segmentos[$i]['consultas_mes'])) {
                    $arquivo["informations"]["consultas_segmento"]["consultas_mes"][] = $segmentos[$i];
                    unset($arquivo["informations"]["consultas_segmento"][$i]);
                } elseif (isset($segmentos[$i]['ultimos_consultantes'])) {
                    $arquivo["informations"]["consultas_segmento"]["ultimos_consultantes"][] = $segmentos[$i];
                    unset($arquivo["informations"]["consultas_segmento"][$i]);
                }
            }
        }

        $consultas = $arquivo["informations"]["consultas"] ?? false;

        if ($consultas) {
            for ($i = 3; $i < count($consultas); $i++) {
                if (isset($consultas[$i]['consultas_mes'])) {
                    $arquivo["informations"]["consultas"]["consultas_mes"][] = $consultas[$i];
                    unset($arquivo["informations"]["consultas"][$i]);
                } elseif (isset($consultas[$i]['ultimos'])) {
                    $arquivo["informations"]["consultas"]["ultimos"][] = $consultas[$i];
                    unset($arquivo["informations"]["consultas"][$i]);
                }
            }
        }

        if (!empty($arquivo["informations"]["consultas_segmento"]["consultas_mes"])) {
            $colunasConsultaMes = $this->breakIntoColuns(
                6,
                $arquivo["informations"]["consultas_segmento"]["consultas_mes"]
            );
            $arquivo["informations"]["consultas_segmento"]["consultas_mes"] = $colunasConsultaMes;
        }

        if (!empty($arquivo["informations"]["consultas"]["consultas_mes"])) {
            $colunasConsulta = $this->breakIntoColuns(6, $arquivo["informations"]["consultas"]["consultas_mes"]);
            $arquivo["informations"]["consultas"]["consultas_mes"] = $colunasConsulta;
        }

        //passa os parametros selecionados
        $consulta = json_decode($consulta, true);
        $criterio = explode("|", $consulta["criterio"]);
        $parametros = (isset($criterio[1])) ? explode(",", $criterio[1]) : [];

        $arquivo["parametros"] = $parametros;

        return $arquivo;
    }

    /*
    *Função Criada para arrumar os itens do array da fonte PessoaGoldBoaVista
    */
    private function fixSeparatedArrayPessoaGold($data)
    {
        foreach ($data["informations"] as $key => $dados) {
            foreach ($dados as $k => $dado) {
                $data["informations"][$k] = $data["informations"][$key][$k];
                unset($data["informations"][$key]);
                break;
            }
        }


        // itens padrão 1
        $fixOne = array("extra_informacoes", "registro_debitos", "falencias_empresariais");
        foreach ($fixOne as $iten) {
            if (@$data["informations"][$iten]) {
                foreach ($data["informations"][$iten] as $key => $dados) {
                    $data["informations"][$iten][key($dados)] = $dados[key($dados)];
                    unset($data["informations"][$iten][$key]);
                }
            }
        }


        // itens padrão 2
        $fixTwo = array("registro_debitos", "falencias_empresariais");
        foreach ($fixTwo as $iten) {
            if (@$data["informations"][$iten]) {
                foreach ($data["informations"][$iten] as $key => $dados) {
                    if ($key != "mensagem_informativa") {
                        foreach ($dados as $k => $dado) {
                            $data["informations"][$iten][$key][key($dado)] = $dado[key($dado)];
                            unset($data["informations"][$iten][$key][$k]);
                        }
                    }
                }
            }
        }

        //iten customizado outras_grafias
        if (@$data["informations"]["outras_grafias"]) {
            foreach ($data["informations"]["outras_grafias"] as $key => $grafias) {
                foreach ($grafias as $j => $grafia) {
                    foreach ($grafia as $l => $graf) {
                        $data["informations"]["outras_grafias"][$key][$j][key($graf)] = $graf[key($graf)];
                        unset($data["informations"]["outras_grafias"][$key][$j][$l]);
                    }
                }
            }
        }

        $acoes_extra = array();
        //iten customizado extra_acoes
        if (isset($data["informations"]["extra_acoes"][0])) {
            $data["informations"]["extra_acoes"] = $data["informations"]["extra_acoes"][0];
            if (isset($data["informations"]["extra_acoes"]['acoes_civeis'])) {
                foreach ($data["informations"]["extra_acoes"]['acoes_civeis'] as $key => &$extra_acoes) {
                    foreach ($extra_acoes as $ek => $acoes) {
                        $acoes_extra[] = $acoes;
                    }
                }
            }
        }

        $data["informations"]["extra_acoes"] = $acoes_extra;

        //iten customizado participacoes_empresas
        $empresasArr = @$data["informations"]["participacoes_empresas"][1]["empresas"];
        // se sim tem mais de uma empresa
        if (@$empresasArr[0]["empresa"]) {
            foreach ($empresasArr as $key => $dados) {//key =0
                foreach ($dados as $k => $dado) {// k = empresa
                    foreach ($dado as $l => $d) {//l = 0
                        $data["informations"]["participacoes_empresas"][1]["empresas"][$key]["empresa"][key(
                            $d
                        )] = $d[key($d)];
                        unset($data["informations"]["participacoes_empresas"][1]["empresas"][$key]["empresa"][$l]);
                    }
                }
            }
        } elseif ($empresasArr) {
            $empresas = array();
            foreach ($empresasArr as $key => $empresa) {
                $empresas[key($empresa)] = $empresa[key($empresa)];
                unset($data["informations"]["participacoes_empresas"][1]["empresas"][$key]);
            }

            $data["informations"]["participacoes_empresas"][1]["empresas"][0]["empresa"] = $empresas;
        }


        //iten customizado registro_debitos
        $empresasArr = @$data["informations"]["registro_debitos"]["ultimas_ocorrencias"];
        // se sim tem mais de um débito
        if (@$empresasArr[0]["registro_debito"]) {
            foreach ($empresasArr as $key => $dados) {//key =0
                foreach ($dados as $k => $dado) {// k = registro_debito
                    foreach ($dado as $l => $d) {//l = 0
                        $data["informations"]["registro_debitos"]["ultimas_ocorrencias"][$key]['registro_debito'][key(
                            $d
                        )] = $d[key($d)];
                        unset($data["informations"]["registro_debitos"]["ultimas_ocorrencias"][$key]['registro_debito'][$l]);
                    }
                }
            }
        } elseif ($empresasArr["registro_debito"]) {
            $debitos = array();

            foreach ($empresasArr["registro_debito"] as $key => $debito) {
                $debitos[key($debito)] = $debito[key($debito)];
                unset($data["informations"]["registro_debitos"]["ultimas_ocorrencias"]["registro_debito"]);
            }

            $data["informations"]["registro_debitos"]["ultimas_ocorrencias"][0]["registro_debito"] = $debitos;
        }

        //iten customizado Falencia Empresariais
        $falenciaArr = @$data["informations"]["falencias_empresariais"]["ultimas_ocorrencias"];
        // se sim tem mais de uma falencia
        if (@$falenciaArr[0]["ocorrencia"]) {
            foreach ($falenciaArr as $key => $dados) {//key =0
                foreach ($dados as $k => $dado) {// k = ocorrencia
                    foreach ($dado as $l => $d) {//l = 0
                        $data["informations"]["falencias_empresariais"]["ultimas_ocorrencias"][$key]['ocorrencia'][key(
                            $d
                        )] = $d[key($d)];
                        unset($data["informations"]["falencias_empresariais"]["ultimas_ocorrencias"][$key]['ocorrencia'][$l]);
                    }
                }
            }
        } elseif ($falenciaArr["ocorrencia"]) {
            $debitos = array();

            foreach ($falenciaArr["ocorrencia"] as $key => $debito) {
                $debitos[key($debito)] = $debito[key($debito)];
                unset($data["informations"]["falencias_empresariais"]["ultimas_ocorrencias"]["ocorrencia"]);
            }

            $data["informations"]["falencias_empresariais"]["ultimas_ocorrencias"][0]["ocorrencia"] = $debitos;
        }
        return $data;
    }

    /**
     * Lista Francesa (Novo) - 247
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Costa <<EMAIL>>
     * @revision Felipe Alves
     * Alterado a resposta para quando vier + de 1 resultado
     */
    private function listaFrancesaNovo(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('pgsql_captura3');

        // Por algum motivo a tabela common.lista_francesa em qa e prod estão totalmente diferentes.
        if (env('APP_ENV') == 'qa') {
            $criterio = '%"nome":"' . strtoupper($consulta['criterio']) . '"%';

            $dados = $db->table($captura->tabela)
                ->select('*')
                ->whereRaw('unaccent(input::text) ILIKE unaccent(?)', [$criterio])
                ->orderBy('id')
                ->get();
        } else {
            $criterio = strtoupper($consulta['criterio']);

            $dados = $db->table($captura->tabela)->select('*')
                ->whereRaw('unaccent(nome) = unaccent(?)', [$criterio])
                ->orderBy('id')->get();
        }

        if (empty($dados)) {
            return null;
        }

        $parsedData = [];
        foreach ($dados as $key => $value) {
            $parsedData[] = json_decode($value->dado) ?? $value;
        }
        return $parsedData;
    }

    /**
     * Transparência Bolsa Familia (Novo) - 243
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Hugo <<EMAIL>>
     */
    private function transparenciaBolsaFamiliaNovo(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $consultas = $this->getConsultaIds($dossierID, $captura->id);

        $parsedData = array();

        foreach ($consultas as $consulta) {
            $dados = $db->table($captura->tabela)->select()
            ->where('id_pesquisa_consulta', '=', $consulta->id)
            ->where('id_captura', '=', $captura->id)
            ->orderBy('id')->get()->toArray();

            // Verifica se tem resposta
            if (empty($dados)) {
                return [];
            }

            if ($resumo) {
                return count($dados);
            }

            if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
                foreach ($dados as $i_dado => $dado) {
                    $arquivo = Funcoes::RdsCaptureData($dado->dado);
                    $arquivo = json_decode($arquivo, true);

                    $arquivo['conteudo_relevante'] = $this->getRelevance($dado->estado);
                    $parsedData[] = $arquivo;
                }
            }
        }

        return $parsedData;
    }


    /**
     * World-Check One API Pilot - 270
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     *
     * @return array
     * @version 1.0
     *
     * <AUTHOR>
     *
     */
    private function WorldCheckOneApiPilot(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->WorldCheckOneApi($captura, $consulta, $resumo);
    }

    /**
     * World-Check One API - 241
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     *
     * @return array
     * @version 1.0
     * @version 1.1 Jefferson Mesquita 05/06/2019 - Alterar a função getConsultaIds para getConsultasIdsByIdPesquisaConsulta
     *
     * <AUTHOR>
     *
     */
    private function WorldCheckOneApi(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $consultas = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        $parsedData = [];

        foreach ($consultas as $consulta) {
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $consulta->id)
                    ->where('id_captura', '=', $captura->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $consulta->id)
                    ->where('id_captura', '=', $captura->id)
                    ->orderBy('id')->get();
            }

            // Verifica se tem resposta
            if ($dados == null) {
                return [];
            }

            if ($resumo) {
                return count($dados);
            }
            if (count($dados) > 0 && isset($dados[0]->dado)) {
                foreach ($dados as $i_dado => $dado) {
                    $arquivo = Funcoes::RdsCaptureData($dado->dado);
                    $arquivo = json_decode($arquivo, true);

                    if (isset($_GET['is_workflow'])) {
                        $parsedData[] = $arquivo;
                    } else {
                        $dados[$i_dado]->dado = $arquivo;

                        $dados[$i_dado]->dado['id'] = $dado->id;
                        $dados[$i_dado]->dado['estado'] = $dado->estado;
                        $dados[$i_dado]->dado['id_pesquisa_consulta'] = $dado->id_pesquisa_consulta;

                        $parsedData[] = $dados[$i_dado]->dado;
                    }
                }
            }

            if (!isset($_GET['is_workflow'])) {
                if (is_array($dados)) {
                    usort($dados, [$this, 'sortByRelevancia']);
                }
            }
        }

        return $parsedData;
    }

    private function WorldCheckOneApiPaga(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->WorldCheckOneApi($captura, $consulta, $resumo);
    }


    /**
     *  Função busca e organiza os dados da fonte Banco Nacional de Mandado de Prisão - BNMP (CNJ)
     *
     * @param CapturaModel $captura
     * @param $consulta
     * @param bool $resumo
     * @return  array()
     *
     **<AUTHOR> - Raildo Santos - 03/09/2018: Recalcula o comprimeto do serialize caso o unserialize falhe.
     *
     * @version 1.1
     *
     * <AUTHOR>
     *
     */

    private function MandadosPrisao(CapturaModel $captura, $consulta, $resumo = false)
    {
        // Banco do upMiner
        $db = DB::connection('upminer');
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            $parsedData = array();

            foreach ($IdsConsulta as $key => $ids) {
                $query = $db->table('processo_classificacao')->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->orderBy('id');

                $data = $query->get();

                foreach ($data as $key => $value) {
                    $value->dado = json_decode($value->dado);
                    $parsedData[] = $value->dado;
                }
            }

            return $parsedData;
        }


        foreach ($IdsConsulta as $ids) {
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->orderBy('id')->get();
            }

            if (isset($_GET['is_workflow'])) {
                $parsedData = [];
            } else {
                $parsedData = array(
                    "classes" => array(),
                    "classeQtde" => array(),
                    "assuntos" => array(),
                    "assuntoQtd" => array(),
                    "situacao" => array(),
                    "situacaoQtd" => array()
                );
            }

            // Verifica se tem resposta
            if ($dados == null) {
                return [];
            }

            if ($resumo) {
                return count($dados);
            }

            if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
                foreach ($dados as $i_dado => $dado) {
                    $arquivo = pg_unescape_bytea($dados[$i_dado]->dado);
                    $arquivo = pg_unescape_bytea($arquivo);
                    $arquivo = pg_unescape_bytea($arquivo);
                    $arquivo = pg_unescape_bytea($arquivo);
                    $arquivo = pg_unescape_bytea($arquivo);
                    $arquivo = pg_unescape_bytea($arquivo);

                    //ajusta os dados antes de dar unserialize
                    //$arquivo = @preg_replace('!s:(\d+):"(.*?)";!e', "'s:'.strlen('$2').':\"$2\";'", $arquivo);
                    $arquivo = stripslashes($arquivo);
                    $arquivo = str_replace("\n", "", $arquivo);
                    try {
                        $arquivo = unserialize($arquivo);
                    } catch (Exception $e) {
                        $arquivo = stripslashes($arquivo);
                        $pattern = array('¿', '()', "\n");
                        $arquivo = str_replace($pattern, '', $arquivo);
                        $arquivo = preg_replace_callback('/s:(\d+):"(.*?)";/', function ($matches) {
                            return 's:' . strlen($matches[2]) . ':"' . $matches[2] . '";';
                        }, $arquivo);

                        $arquivo = unserialize($arquivo);
                    }

                    if (isset($_GET['is_workflow'])) {
                        $parsedData[] = $arquivo;
                    } else {
                        $dados[$i_dado]->dado = $arquivo;

                        $dados[$i_dado]->dado['id_dado'] = $dados[$i_dado]->dado['id'];
                        $dados[$i_dado]->dado['id'] = $dados[$i_dado]->id;
                        $dados[$i_dado]->dado['estado'] = $dados[$i_dado]->estado;
                        $dados[$i_dado]->dado['id_pesquisa_consulta'] = $dados[$i_dado]->id_pesquisa_consulta;

                        if ($dados[$i_dado]->dado['assuntoDelito'] == "") {
                            $dados[$i_dado]->dado['assuntoDelito'] = "Não Informado";
                        }
                        if ($dados[$i_dado]->dado['situacao'] == "") {
                            $dados[$i_dado]->dado['situacao'] = "Não Informada";
                        }
                        if ($dados[$i_dado]->dado['classe'] == "") {
                            $dados[$i_dado]->dado['classe'] = "Não Informada";
                        }


                        @$parsedData['classeQtde'][$dados[$i_dado]->dado["classe"]]++;
                        @$parsedData['situacaoQtd'][$dados[$i_dado]->dado["situacao"]]++;
                        @$parsedData['assuntoQtd'][$dados[$i_dado]->dado["assuntoDelito"]]++;

                        if (!in_array($dados[$i_dado]->dado['classe'], $parsedData['classes'])) {
                            $parsedData['classes'][] = $dados[$i_dado]->dado['classe'];
                        }

                        if (!in_array($dados[$i_dado]->dado['assuntoDelito'], $parsedData['assuntos'])) {
                            $parsedData['assuntos'][] = $dados[$i_dado]->dado['assuntoDelito'];
                        }

                        if (!in_array($dados[$i_dado]->dado['situacao'], $parsedData['situacao'])) {
                            $parsedData['situacao'][] = $dados[$i_dado]->dado['situacao'];
                        }


                        if (is_array($dados[$i_dado]->dado['documentos'])) {
                            foreach ($dados[$i_dado]->dado['documentos'] as $key => $docs) {
                                $doc = explode("-", $docs);

                                $doc[1] = preg_replace("/[^0-9\s]/", "", $doc[1]);

                                $dados[$i_dado]->dado['documentos'][strtolower(trim($doc[0]))] = trim($doc[1]);
                                unset($dados[$i_dado]->dado['documentos'][$key]);
                            }
                        }
                    }
                }
            }

            if (!isset($_GET['is_workflow'])) {
                if (is_array($dados)) {
                    usort($dados, [$this, 'sortByRelevancia']);
                }

                if (count($parsedData["classes"]) > 1 || count($parsedData["assuntos"]) > 1) {
                    $parsedData["showFilter"] = true;
                }

                $parsedData['dados'] = $dados;
            }
        }

        return $parsedData;
    }

    private function getDataFromReceitaPF($ref)
    {
        $db = DB::connection('pgsql_captura3');

        $dado = $db->table('receita_federal.comprovante_cpf_consulta')
            ->select('dado', 'data_hora')
            ->join(
                'receita_federal.comprovante_cpf',
                'receita_federal.comprovante_cpf' . '.id',
                '=',
                'receita_federal.comprovante_cpf_consulta.id_dado'
            )
            ->where('receita_federal.comprovante_cpf' . '_consulta.id', $ref)
            ->first();

        if (empty($dado)) {
            return false;
        }

        $arquivo = Funcoes::RdsCaptureData($dado->dado);

        $arquivo = Funcoes::voToObject($arquivo);

        return $arquivo;
    }

    private function getDataFromReceitaPJ($ref)
    {
        $db = DB::connection('pgsql_captura3');

        $dado = $db->table('receita_federal.comprovante_cnpj_consulta')
            ->select('dado', 'data_hora')
            ->join(
                'receita_federal.comprovante_cnpj',
                'receita_federal.comprovante_cnpj' . '.id',
                '=',
                'receita_federal.comprovante_cnpj_consulta.id_dado'
            )
            ->where('receita_federal.comprovante_cnpj' . '_consulta.id', $ref)
            ->first();

        if (empty($dado)) {
            return false;
        }

        $arquivo = Funcoes::RdsCaptureData($dado->dado);
        $arquivo = Funcoes::voToObject($arquivo);

        return $arquivo;
    }

    private function getCriterioName($id_lote_consulta, $criterio)
    {
        $db = DB::connection('upminer');

        $criterio = strtolower($criterio);
        $replaces = array(".", "-", "/");
        $cleanCriterio = str_replace($replaces, "", $criterio);


        if (preg_match("/[0-9]+/", $criterio, $output)) {
            if (strlen($cleanCriterio) == 11) {
                $receitaPF = $db->table("pesquisa_consulta")->select()->where(
                    ['id_lote_consulta' => $id_lote_consulta, 'captura' => 1]
                )->orderBy('id')->get();

                if ($receitaPF->isEmpty()) {
                    return $criterio;
                }

                $criterio = str_replace($replaces, "", $criterio);

                foreach ($receitaPF as $receita) {
                    if (preg_match_all("/$criterio/", $receita->criterio)) {
                        $referencia = $receita->referencia;
                        break;
                    }
                }

                $dadosPF = $this->getDataFromReceitaPF($referencia);

                if (!empty($dadosPF)) {
                    return explode(" ", $dadosPF["nome"]);
                }
            } else {
                $receitaPJ = $db->table("pesquisa_consulta")->select()->where(
                    ['id_lote_consulta' => $id_lote_consulta, 'captura' => 2]
                )->orderBy('id')->get();

                if (empty($receitaPJ)) {
                    return $criterio;
                }

                $criterio = str_replace($replaces, "", $criterio);

                foreach ($receitaPJ as $receita) {
                    if ($receita->criterio == $criterio) {
                        $referencia = $receita->referencia;
                        break;
                    }
                }

                if (!isset($referencia)) {
                    return explode(" ", $criterio);
                }

                $dadosPJ = $this->getDataFromReceitaPJ($referencia);
                if (!empty($dadosPJ)) {
                    return $nomes = explode(" ", $dadosPJ["nome_empresarial"]);
                }
            }
        } else {
            return explode(" ", $criterio);
        }
    }

    private function trfParametrizadoNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function perfisTrfCnpj(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function perfisTjCnpj(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function perfisTjCnpjNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function trfParametrizadoCpf(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function trfParametrizadoCnpj(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function tjBrasilParametrizadoCpfNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function tjBrasilParametrizado(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function tjBrasilParametrizadoCnpjNome(CapturaModel $captura, $consulta, $resumo = false)
    {
        // retorno igual ao do tjBrasilParametrizadosCpfCnpj
        return $this->tjBrasilParametrizadosCpfCnpj($captura, $consulta);
    }

    private function tjBrasilParametrizadosCpfCnpj(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $criterio = explode("|", $consulta->criterio);

        $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);

        $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;

        $replaceMoney = array("R", "$", ".", " ");
        $parsedData = array(
            "descricoes" => array(),
            "descricao" => array(),
            "classeNames" => array(),
            "classesQtd" => array(),
            "valorMin" => array(),
            "valorMax" => array(),
            "valores" => array(),
            "areaNames" => array(),
            "areaQtd" => array()
        );

        $replaces = array(
            "Reqte" => "Requerente",
            "Reqdo" => "Requerido",
            "Reqda" => "Requerido",
            "Embargdo" => "Embargado",
            "Embargte" => "Embargante"
        );
        $classes = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('id_captura', '=', $captura->id)
                    ->orderBy('id')->get();
            }

            if (empty($dados)) {
                continue;
            }

            foreach ($dados as $key => $dado) {
                try {
                    // $dados[$key]->dado = strip_tags($dados[$key]->dado);
                    $dados[$key]->dado = preg_replace("/\n/", "", $dados[$key]->dado);
                } catch (Exception $e) {
                    // $dados[$key]->dado = strip_tags(stream_get_contents($dados[$key]->dado));
                    $dados[$key]->dado = preg_replace("/\n/", "", $dados[$key]->dado);
                }
            }

            $cl = 1;
            if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
                $classes = array();
                $providers = array();

                foreach ($dados as &$dado) {
                    if (is_array($dado->dado)) {
                        $dado->dado = serialize(pg_escape_bytea(json_encode($dado->dado)));
                    } elseif (!is_array($dado->dado)) {
                        $escapedData = pg_unescape_bytea($dado->dado);
                        $escapedData = @preg_replace(
                            '!s:(\d+):"(.*?)";!e',
                            "'s:'.strlen('$2').':\"$2\";'",
                            $escapedData
                        );
                        $escapedData = strip_tags($escapedData);


                        if (@$escapedData = unserialize($escapedData)) {
                            $dado->dado = $escapedData;
                        } else {
                            $escapedData = pg_unescape_bytea(pg_unescape_bytea($dado->dado));
                            $escapedData = @preg_replace(
                                '!s:(\d+):"(.*?)";!e',
                                "'s:'.strlen('$2').':\"$2\";'",
                                $escapedData
                            );
                            $escapedData = unserialize($escapedData);
                            $dado->dado = $escapedData;
                        }

                        if ($dado->dado == false) {
                            continue;
                        }

                        $dado->dado = self::recursivePgUnescapeBytea($dado->dado);
                    } else {
                        $dado->dado = strip_tags(json_encode(stream_get_contents($dado->dado)));
                        $dado->dado = json_decode($dado->dado, true);
                    }
                    // if(!isset($dado->dado['partes'][0]) || !isset($dado->dado['partes'])) continue;

                    if (!isset($dado->dado['area']) == "") {
                        $dado->dado['area'] = "Não Informada";
                    }
                    if (!isset($dado->dado['partes'])) {
                        if (isset($dado->dado['info'])) {
                            $dado->dado['partes'] = $dado->dado['info'];
                        } else {
                            throw new CustomException("Partes não existentes no array");
                        }
                    }
                    // Seta a parte mais proxima TJsBRASIL
                    $parteMaisProxima = $this->parteMaisProxima($nome, $nomes, $dado->dado['partes']);

                    //retorna o array com no maximo a quantidade espeificada no segundo parametro, feito par limitar a quantidade partes
                    $dado->dado['partes'] = Funcoes::returnChunkArrayIndex($dado->dado['partes'], 50, 0);


                    foreach ($parteMaisProxima['descricoes']['descricao'] as $key => $qtdDesc) {
                        @$parsedData['descricao'][$key] += $qtdDesc;
                    }

                    foreach ($parteMaisProxima['descricoes']['descricoes'] as $key => $nameDesc) {
                        if (!in_array($nameDesc, $parsedData['descricoes'])) {
                            $parsedData['descricoes'][] = $nameDesc;
                        }
                    }

                    if (isset($parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'])) {
                        @$dado->dado['nome_mais_proximo']['nome'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'];
                        @$dado->dado['nome_mais_proximo']['descricao'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['descricao'];
                    }

                    if (!isset($dado->dado['valor'])) {
                        $dado->dado['valor'] = "";
                    }
                    if (!isset($dado->dado['classe_area'])) {
                        $dado->dado['classe_area'] = "Não Informada";
                    }

                    if (!isset($dado->dado['classe'])) {
                        $dado->dado['classe'] = 'Não Informada';
                    }


                    $dado->dado['valor'] = preg_replace("/ /", "", $dado->dado['valor']);
                    $dado->dado['classe'] = preg_replace("/\n/", "", $dado->dado['classe']);

                    if (strlen($dado->dado['valor']) > 1) {
                        $dado->dado['valor_causa_value'] = trim(
                            str_replace(",", "", str_replace($replaceMoney, "", $dado->dado['valor']))
                        );
                    }
                    $parsedData["valoresMask"][] = str_replace(" ", "", $dado->dado['valor']);

                    if (strlen($dado->dado['valor']) > 1 and !preg_match("/c/i", $dado->dado['valor'], $output)) {
                        $parsedData["valores"][] = trim(
                            str_replace(",", ".", str_replace($replaceMoney, "", $dado->dado['valor']))
                        );
                    }

                    if (!empty($dado->dado['classe'])) {
                        @$parsedData['classes'][$dado->dado['classe']][] = $dado;

                        @$parsedData['areaQtd'][$dado->dado['classe_area']]++;
                        if (!in_array($dado->dado['classe_area'], $parsedData['areaNames'])) {
                            $parsedData['areaNames'][] = $dado->dado['classe_area'];
                        }


                        @$parsedData['classesQtd'][$dado->dado['classe']]++;


                        if (!in_array($dado->dado['classe'], $parsedData['classeNames'])) {
                            $parsedData['classeNames'][] = $dado->dado['classe'];
                        }
                    }
                }
            }

            if (!empty($parsedData['classeNames']) && !empty($parsedData["valores"])) {
                $parsedData["valorMax"]["mask"] = max($parsedData["valores"]);
                $parsedData["valorMin"]["mask"] = min($parsedData["valores"]);
                $parsedData["valorMax"]["value"] = trim(
                    str_replace(",", ".", str_replace($replaceMoney, "", $parsedData["valorMax"]["mask"]))
                );
                $parsedData["valorMin"]["value"] = trim(
                    str_replace(",", ".", str_replace($replaceMoney, "", $parsedData["valorMin"]["mask"]))
                );


                foreach ($parsedData["classes"] as $classes) {
                    foreach ($classes as $classe) {
                        if (is_array($classe)) {
                            usort($classe, [$this, 'sortByRelevancia']);
                        }
                    }
                }

                $parsedData["valores"] = array_unique($parsedData["valores"]);
                sort($parsedData["valores"]);

                if (!isset($parsedData["valoresMask"])) {
                    $parsedData["valoresMask"] = array();
                }

                foreach ($parsedData["valores"] as $valor) {
                    if ($valor > 0) {
                        $parsedData["valoresMask"][] = "R$" . number_format($valor, 2, ',', '.');
                    }
                }
            }
        }

        return $parsedData;
    }

    /*
    *Busca os estados ou TRFs pesquisados nos tribunais
    */
    private function getTribunaisPesquisados($id_dossie, $id_captura)
    {
        $id_dossie = (isset($_GET['id_dossie'])) ? $_GET['id_dossie'] : $id_dossie;

        $db = DB::connection('upminer');

        $pesquisa_consulta = $db->table("pesquisa_consulta")->select('criterio')
            ->where(function ($q) use ($id_dossie) {
                $q->where('id_pesquisa_consulta', $id_dossie)
                    ->orWhere(function ($q) use ($id_dossie) {
                        $q->where('id', $id_dossie)
                            ->whereNull('id_pesquisa_consulta');
                    });
            })
            ->where('captura', $id_captura)
            ->orderBy('id')
            ->get();

        $uf = [];

        foreach ($pesquisa_consulta as $key => $criterio) {
            $criterios = explode("|", $criterio->criterio);
            $uf = $criterios[1];

            $uf = str_replace('trf', 'TRF ', $uf);

            $ufs[] = $uf;
        }

        return $ufs;
    }

    /**
     * Função de busca e organização dos dados da fonte Processos no Superior Tribunal de Justiça (STJ)
     *
     * @param CapturaModel $captura
     * @param $consulta
     * @param bool $resumo
     *
     * @return array
     * <AUTHOR> - ??
     * Revisão Jefferson Mesquita - 24/05/2019 - trocar função getConsultasIds por getConsultasIdsByIdPesquisaConsulta
     * @version 1.1
     *
     */
    private function strProcessoGenerica(CapturaModel $captura, $consulta, $resumo = false)
    {
        // Banco do upMiner
        $db = DB::connection('upminer');
        $criterio = explode("|", $consulta->criterio);

        if (!isset($_GET['is_workflow'])) {
            $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);
            $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;
        }

        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura['id']);
        $parsedData = [];
        if (!empty($consulta->data_faturamento)) {
            $dataCriacao = explode(" ", $consulta->data_faturamento);
        } else {
            if (!empty($consulta->data_estado)) {
                $dataCriacao = explode(" ", $consulta->data_estado);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                } else {
                    $dataCriacao = array();
                }
            }
        }
        foreach ($IdsConsulta as $ids) {
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->orderBy('id')->get();
            }

            // Verifica se tem resposta
            if ($dados == null) {
                return [];
            }
            if ($captura->data_migracao_lambda != '' && strtotime($captura->data_migracao_lambda) <= strtotime(
                    @$dataCriacao[0]
                )) {
                if (is_array($dados) && count($dados) > 0) {
                    foreach ($dados as $dado) {
                        $validacaoString = trim(str_replace('"', "", $dado->dado));
                        if (empty($validacaoString)) {
                            continue;
                        }
                        $parsedDado = json_decode($dado->dado, true);
                        if (!isset($parsedDado['numero_stf']) || trim($parsedDado['numero_stf']) == '-') {
                            continue;
                        }
                        if (isset($parsedDado["partes"])) {
                            //Seta a parte mais proxima
                            if (!isset($_GET['is_workflow'])) {
                                $parteMaisProxima = $this->parteMaisProxima($nome, $nomes, $parsedDado["partes"]);

                                if (isset($parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'])) {
                                    $parsedDado['nome_mais_proximo']['nome'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'];
                                    $parsedDado['nome_mais_proximo']['descricao'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['descricao'];
                                }
                            }
                        }
                        $parsedDado["estado"] = $dado->estado;
                        $parsedDado["pesquisa_id"] = $dado->id_pesquisa_consulta;
                        $parsedDado["id"] = $dado->id;

                        if (isset($_GET['is_workflow'])) {
                            $parsedData[] = $parsedDado;
                        } else {
                            $dado->dado = $parsedDado;
                            $parsedData[] = $dado;
                        }
                    }
                } else {
                    foreach ($dados as $key => $value) {
                        $value->dado = json_decode($value->dado);
                        $value->dado->conteudo_relevante = !empty($value->conteudo_relevante) ? $this->getRelevance(
                            $value->conteudo_relevante
                        ) : "default";
                        $parsedData[] = $value->dado;
                    }
                }
            } else {
                if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
                    foreach ($dados as $key => $value) {
                        if (is_resource($dados[$key]->dado)) {
                            $dado = $dados[$key]->dado;
                            $dado = stream_get_contents($dado);

                            $dado = stripslashes($dado);
                            $dado = stripslashes($dado);

                            $dado = str_replace('\000', chr(0), $dado);
                            $dado = preg_replace('@\\\0+@', "\0", $dado);

                            $dado = str_replace("''", "'", $dado);
                            $dado = str_replace("''", "'", $dado);
                        } else {
                            $dado = $dados[$key]->dado;
                            $dado = str_replace('\\\\', '\\', $dado);
                            $dado = str_replace('\\\\', '\\', $dado);
                            $dado = str_replace('\\\\', '\\', $dado);
                            $dado = str_replace('\\\\', '\\', $dado);
                            $dado = pg_unescape_bytea($dado);
                        }

                        $dado = @unserialize($dado);

                        if (isset($_GET['is_workflow'])) {
                            $parsedData[] = $dado;
                        } else {
                            $dado["estado"] = $dados[$key]->estado;
                            $dado["pesquisa_id"] = $dados[$key]->id_pesquisa_consulta;
                            $dado["id"] = $dados[$key]->id;
                            $dados[$key]->dado = $dado;

                            // if(!isset($dados[$key]->dado['numero_protocolo'])){
                            //     unset($dados[$key]);
                            //     continue;
                            // }

                            if (isset($dado["partes"])) {
                                //Seta a parte mais proxima
                                $parteMaisProxima = $this->parteMaisProxima($nome, $nomes, $dado["partes"]);

                                if (isset($parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'])) {
                                    @$dados[$key]->dado['nome_mais_proximo']['nome'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'];
                                    @$dados[$key]->dado['nome_mais_proximo']['descricao'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['descricao'];
                                }
                            }
                        }
                    }

                    if (isset($_GET['is_workflow'])) {
                        return $parsedData;
                    }

                    foreach ($dados as $dado) {
                        $parsedData[] = $dado;
                    }
                }
            }
        }

        return $parsedData;
    }

    private function stfProcesso(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->strProcessoGenerica($captura, $consulta);
    }

    private function TJUnificado(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        if (!$resumo) {
            $criteria = (isset($consulta->childrens[0]['criterio']) ? $consulta->childrens[0]['criterio'] : $consulta->criterio);
            $criterio = explode("|", $criteria);
            $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);

            $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;

            $replaceMoney = array("R", "$", ".");

            $nome = preg_replace(
                array(
                    "/(á|à|ã|â|ä)/",
                    "/(Á|À|Ã|Â|Ä)/",
                    "/(é|è|ẽ|ê|ë)/",
                    "/(É|È|Ẽ|Ê|Ë)/",
                    "/(í|ì|ĩ|î|ï)/",
                    "/(Í|Ì|Ĩ|Î|Ï)/",
                    "/(ó|ò|õ|ô|ö)/",
                    "/(Ó|Ò|Õ|Ô|Ö)/",
                    "/(ú|ù|ũ|û|ü)/",
                    "/(Ú|Ù|Ũ|Û|Ü)/",
                    "/(ç)/",
                    "/(Ç)/",
                    "/(ñ)/",
                    "/(Ñ)/"
                ),
                explode(" ", "a A e E i I o O u U c C n N"),
                $nome
            );

            $nome = strtolower($nome);

            $replaces = array(
                "reqte" => "Requerente",
                "reqdo" => "Requerido",
                "reqda" => "Requerido",
                "embargdo" => "Embargado",
                "embargte" => "Embargante"
            );
            $classes = array();
        }

        $totalProcessos = '';

        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $searchIds = $this->getConsultaIds($dossierID, $captura['id'], true);
        if (!$searchIds) {
            return false;
        }

        $remove_repitidos = array();

        $dados = $db->table($captura->tabela)->select(
            "{$captura->tabela}.*",
            'pesquisa_consulta.criterio AS criterio_consulta'
        )
            ->whereIn("{$captura->tabela}.id_pesquisa_consulta", $searchIds)
            ->where("{$captura->tabela}.id_captura", '=', $captura->id)
            ->join('pesquisa_consulta', 'pesquisa_consulta.id', '=', $captura->tabela . '.id_pesquisa_consulta')
            ->orderBy("{$captura->tabela}.id")->get();

        $dados = $dados ? $dados->toArray() : $dados;

        if (empty($dados)) {
            return false;
        }

        $newDados = [];
        $cl = 1;
        if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
            $classes = array();
            $providers = array();
            foreach ($dados as &$dado) {
                $dado->criterio_consulta = explode("|", $dado->criterio_consulta)[0];

                try {
                    $dado->dado = \pg_unescape_bytea($dado->dado);
                    $dado->dado = Functions::encoding($dado->dado);
                    $dado->dado = json_decode($dado->dado, true);
                } catch (Exception $e) {
                    $dado->dado = stream_get_contents($dado->dado);
                    $dado->dado = Functions::encoding($dado->dado);
                    $dado->dado = json_decode($dado->dado, true);
                }

                if (!empty($dado->dado['processos'][0]['numero_processo_unificado'])) {
                    $dado->dado = $dado->dado['processos'][0];
                }


                if (empty($dado->dado['numero_processo_unificado'])) {
                    continue;
                }

                if (!isset($dado->dado['msg'])) {
                    $dado->dado['msg'] = "";
                }

                $dado->dado['conteudo_relevante'] = $dado->conteudo_relevante;
                $newDados[] = $dado->dado;
            }
        }

        return $newDados;
    }

    private function TJUnificadoOff(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->TJUnificado($captura, $consulta, $resumo);
    }

    private function TJUnificadoEnriquecido(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->TJUnificado($captura, $consulta, $resumo);
    }

    private function LexisNexis(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $notLanguages = array(
            'negativeNewsPersonCheckQuery',
            'negativeNewsCompanyCheckQuery',
            'PEPSources',
            'sanctionAndWarnings',
            'biographicalSources',
            'legalSources',
            'directorAndShareholders',
            'dbDunMarket'
        );

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $ids) {
            $dados = $db->table($captura->tabela)->select()->where(
                ['id_pesquisa_consulta' => $ids->id, 'id_captura' => $captura->id]
            )->orderBy('id')->get();

            if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
                if ($resumo) {
                    return count($dados);
                }

                $classes = array();
                $providers = array();

                foreach ($dados as $key => &$dado) {
                    $dado->dado = pg_unescape_bytea($dado->dado);
                    $dado->dado = json_decode($dado->dado, true);

                    if ($key == 0) {
                        $parsedData['id_pesquisa_consulta'] = $dado->id_pesquisa_consulta;
                    }
                }
            }


            for ($i = 0; $i < count($dados); $i++) {
                if (isset($dados[$i]->dado['vazios'])) {
                    foreach ($dados[$i]->dado['vazios'] as $vazio) {
                        if (!in_array($vazio, $notLanguages)) {
                            @$parsedData['dados']['languages'][$vazio] = array();
                        } else {
                            $parsedData['dados'][$vazio] = array();
                        }
                    }
                } else {
                    if (!in_array($dados[$i]->dado['tipo'], $notLanguages)) {
                        @$parsedData['dados']['languages'][$dados[$i]->dado['tipo']][] = $dados[$i];
                    } else {
                        @$parsedData['dados'][$dados[$i]->dado['tipo']][] = $dados[$i];
                    }
                }
            }
        }

        return $parsedData;
    }

    private function recursiveStriPos($texto = null, $search = array())
    {
        $prox = 0;

        if (!is_array($search)) {
            $search = array($search);
        }

        $replaces = array(
            " a ",
            " as ",
            " e ",
            " o ",
            " os ",
            " ao ",
            " aos ",
            " na ",
            " nas ",
            " no ",
            " nos ",
            " da ",
            " das ",
            " de ",
            " do ",
            " dos "
        );
        $texto = strtolower($texto);
        $texto = str_replace($replaces, " ", $texto);

        foreach ($search as $key => $sear) {
            if (in_array(" " . $sear . " ", $replaces)) {
                unset($search[$key]);
            }
        }

        foreach ($search as $value) {
            if (stripos($texto, $value) !== false) {
                $prox++;
            }
        }

        return ($prox > 0) ? array('aprox' => $prox) : false;
    }

    /**
     * Função de busca e organização dos dados da fonte Processos no Superior Tribunal de Justiça (STJ)
     *
     * @param CapturaModel $captura
     * @param $consulta
     * @param bool $resumo
     * @return array
     * @throws Exception
     * @version 1.2
     *
     * <AUTHOR> - ??
     * Revisão Maximiliano Minucelli - 23/04/2018: Recalcular o comprimento do serialize caso o unserialize falhe.
     * @version 1.1
     * Revisão Jefferson Mesquita - 24/05/2019 - trocar função getConsultasIds por getConsultasIdsByIdPesquisaConsulta
     */
    private function StjProcessoNome(CapturaModel $captura, $consulta, $resumo = false, $fonte_antiga = false)
    {
        // Banco do upMiner
        $db = DB::connection('upminer');
        if (strpos($consulta->criterio, '|')) {
            $criterio = explode("|", $consulta->criterio);
        } else {
            $criterio = array();
            $criterio[] = $consulta->criterio;
        }

        if (!isset($_GET['is_workflow'])) {
            $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);
            $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;
            //Colocar  o tipo de  parte  se  necessario
            $tiposDeParte = array(
                "recorrente",
                "agravante",
                "embargado",
                "procurador",
                "recorrido",
                "relator",
                "paciente"
            );
        }

        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura['id']);


        $parsedData = [];

        foreach ($IdsConsulta as $ids) {
            if ($captura->pdf) {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->where('estado', '=', 'R')
                    ->orderBy('id')->get();
            } else {
                $dados = $db->table($captura->tabela)->select()
                    ->where('id_pesquisa_consulta', '=', $ids->id)
                    ->orderBy('id')->get();
            }

            // Verifica se tem resposta
            if ($dados == null) {
                return [];
            }

$dados = json_decode($dados,true);
            if ((!$fonte_antiga) &&
                isset($captura->data_migracao_lambda) &&
                (strtotime($consulta->data_criacao) >= strtotime($captura->data_migracao_lambda))

            ) {
                if (is_array($dados) && count($dados) > 0 &&
                    isset($dados[0]['dado'])
                ) {
                    foreach ($dados as $i_dado => $dado) {
                        $arquivo = Funcoes::RdsCaptureData($dado['dado']);
                        $arquivo = json_decode($arquivo, true);

                        if (isset($_GET['is_workflow'])) {
                            $parsedData[] = $arquivo;
                        } else {
                            $dados[$i_dado]['dado'] = $arquivo;

                            $dados[$i_dado]['dado']['id'] = $dado['id'];
                            $dados[$i_dado]['dado']['estado'] = $dado['estado'];
                            $dados[$i_dado]['dado']['id_pesquisa_consulta'] = $dado['id_pesquisa_consulta'];
                            $dados[$i_dado]['dado']['conteudo_relevante'] = 'default';
                            if (!isset($dados[$i_dado]['dado']['numero_unico'])) {
                                unset($dados[$i_dado]);
                                continue;
                            }

                            // A variavel vai ser inicializada toda vez nessa parte, para não pegar partes de outros processos.
                            $partes = [];
                            foreach ($tiposDeParte as $k => $tipo) {
                                if (isset($dados[$i_dado]['dado'][$tipo])) {
                                    $partes[] = array(
                                        "tipo" => $tipo,
                                        "nome" => $dados[$i_dado]['dado'][$tipo]
                                    );
                                }
                            }

                            if (isset($partes)) {
                                //Seta a parte mais proxima StjProcessoNome
                                $parteMaisProxima = $this->parteMaisProxima($nome, $nomes, $partes);

                                if (isset($parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'])) {
                                    $parteMaisProxima = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo'];
                                    $dados[$i_dado]['dado']['nome_mais_proximo'] = $parteMaisProxima;
                                }
                            }
                            // $parsedData[] = $dados[$i_dado]->dado;
                        }
                    }

                    if (isset($_GET['is_workflow'])) {
                        return $parsedData;
                    }

                    foreach ($dados as $dado) {
                        $parsedData[] = $dado['dado'];
                    }
                }

                continue;
            }


            if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
                foreach ($dados as $key => $value) {
                    $validaAux = true;
                    if (is_resource($dados[$key]->dado)) {
                        $dado = $dados[$key]->dado;
                        $dado = stream_get_contents($dado);

                        $dado = stripslashes($dado);
                        $dado = stripslashes($dado);

                        $dado = str_replace('\000', chr(0), $dado);
                        $dado = preg_replace('@\\\0+@', "\0", $dado);

                        $dado = str_replace("''", "'", $dado);
                        $dado = str_replace("''", "'", $dado);
                    } else {
                        $dado = $dados[$key]->dado;
                        $dado = str_replace('\\\\', '\\', $dado);
                        $dado = str_replace('\\\\', '\\', $dado);
                        $dado = str_replace('\\\\', '\\', $dado);
                        $dado = str_replace('\\\\', '\\', $dado);
                        $dado = pg_unescape_bytea($dado);
                    }


                    try {
                        $dado = unserialize($dado);
                    } catch (Exception $e) {
                        $dado = preg_replace_callback(
                            '/s:([0-9]+):\"(.*?)\";/',
                            function ($matches) {
                                return "s:" . strlen($matches[2]) . ':"' . $matches[2] . '";';
                            },
                            $dado
                        );

                        try {
                            $dado = unserialize($dados[$key]->dado);
                        } catch (Exception $e) {
                            $arquivo = Funcoes::RdsCaptureData($value->dado);
                            $arquivo = json_decode($arquivo, true);

                            $dados[$key]->dado = $arquivo;

                            $dados[$key]->dado['id'] = $value->id;
                            $dados[$key]->dado['estado'] = $value->estado;
                            $dados[$key]->dado['id_pesquisa_consulta'] = $value->id_pesquisa_consulta;
                            $validaAux = false;
                        }
                    }

                    if (isset($_GET['is_workflow'])) {
                        $parsedData[] = $dado;
                    } else {
                        if ($validaAux == true) {
                            $dado["estado"] = $dados[$key]->estado;
                            $dado["pesquisa_id"] = $dados[$key]->id_pesquisa_consulta;
                            $dado["id"] = $dados[$key]->id;
                            $dados[$key]->dado = $dado;

                            if (!isset($dados[$key]->dado['numero_unico'])) {
                                unset($dados[$key]);
                                continue;
                            }

                            foreach ($tiposDeParte as $k => $tipo) {
                                if (isset($dados[$key]->dado[$tipo])) {
                                    $partes[] = array("tipo" => $tipo, "nome" => $dados[$key]->dado[$tipo]);
                                }
                            }

                            if (isset($partes)) {
                                //Seta a parte mais proxima StjProcessoNome
                                $parteMaisProxima = $this->parteMaisProxima($nome, $nomes, $partes);

                                if (isset($parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'])) {
                                    @$dados[$key]->dado['nome_mais_proximo']['nome'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['nome'];
                                    @$dados[$key]->dado['nome_mais_proximo']['descricao'] = $parteMaisProxima['parteMaisProxima']['nome_mais_proximo']['descricao'];
                                }
                            }
                        }
                    }
                }

                if (isset($_GET['is_workflow'])) {
                    return $parsedData;
                }

                foreach ($dados as $dado) {
                    $parsedData[] = $dado;
                }
            }
        }

        return $parsedData;
    }

    private function stjProcesso(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->StjProcessoNome($captura, $consulta, false, false);
    }

    private function TRFUnificado(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $criterio = explode("|", $consulta->criterio);

        $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);

        $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;

        $replaceMoney = array("R", "$", ".");

        $searchIds = $this->getSearchIds($consulta->id, $captura['id']);
        if (!$searchIds) {
            return false;
        }

        $dados = $db->table($captura->tabela)->select(
            "{$captura->tabela}.*",
            'pesquisa_consulta.criterio AS criterio_consulta'
        )
            ->join('pesquisa_consulta', 'pesquisa_consulta.id', '=', $captura->tabela . '.id_pesquisa_consulta')
            // ->where($captura->tabela . '.id_pesquisa_consulta', '=', $ids->id)
            ->whereIn("{$captura->tabela}.id_pesquisa_consulta", $searchIds)
            ->orderBy($captura->tabela . '.id')
            ->get()->toArray();

        // Verifica se tem resposta
        if (empty($dados)) {
            return false;
        }

        $newData = [];
        if (is_array($dados) && count($dados) > 0 && isset($dados[0]->dado)) {
            foreach ($dados as $key => $d) {
                $d->criterio_consulta = explode("|", $d->criterio_consulta)[0];

                if (is_resource($d->dado)) {
                    $dado = $d->dado;
                    $dado = stream_get_contents($dado);

                    $dado = stripslashes($dado);
                    $dado = stripslashes($dado);

                    $dado = str_replace('\000', chr(0), $dado);
                    $dado = preg_replace('@\\\0+@', "\0", $dado);

                    $dado = str_replace("''", "'", $dado);
                    $dado = str_replace("''", "'", $dado);
                } else {
                    $dado = $dados[$key]->dado;
                    $dado = str_replace('\\\\', '\\', $dado);
                    $dado = str_replace('\\\\', '\\', $dado);
                    $dado = str_replace('\\\\', '\\', $dado);
                    $dado = str_replace('\\\\', '\\', $dado);
                    $dado = pg_unescape_bytea($dado);
                }

                $dado_replaced = preg_replace_callback('!s:(\d+):"(.*?)";!', function ($m) {
                    return "s:" . strlen($m[2]) . ":\"{$m[2]}\";";
                }, $dado);

                $d->dado = @unserialize($dado_replaced);

                if (Funcoes::isSerialize($d->dado)) {
                    $d->dado = @unserialize($dado);
                }

                if (!is_object($d->dado)) {
                    $d->dado = json_decode(json_encode((object)$d->dado), false);
                }
                if (empty($d->dado) || is_object($d->dado) && empty(get_object_vars($d->dado))) {
                    continue;
                }

                if (!empty($d->dado->classe) && preg_match('/title="(.*?)"/', $d->dado->classe, $matches)) {
                    $d->dado->classe = $matches[1];
                }
                if (!property_exists($d->dado, 'numero_processo_unificado')) {
                    continue;
                }

                if (empty($d->dado->classe)) {
                    $d->dado->classe = "Outros";
                }

                if (isset($d->dado) & is_array($d->dado) & empty($d->dado)) {
                    continue;
                }

                $newData[] = json_decode(json_encode($d->dado), true);
            }
        }

        return $newData;
    }

    private function TRFUnificadoOff(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->TRFUnificado($captura, $consulta, $resumo);
    }

    private function TRFUnificadoEnriquecido(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->TRFUnificado($captura, $consulta, $resumo);
    }

    private function parteMaisProxima($nome, $nomes, $partes)
    {
        $abrev = ["ind/", "com/"];
        $full = ["industria", "comercio"];

        $replaces = array(
            "reqte" => "Requerente",
            "reqdo" => "Requerido",
            "reqda" => "Requerido",
            "embargdo" => "Embargado",
            "embargte" => "Embargante"
        );

        $parteMaisProxima = ['nome_mais_proximo' => []];

        $descricoes = ['descricao' => [], 'descricoes' => []];

        $partes = (array)$partes;

        $primeira_parte = '';
        $primeira_descricao = '';

        foreach ($partes as &$parte) {
            $parte = (array)$parte;

            if (!isset($parte['nome'])) {
                $parte['nome'] = null;
            }

            if (empty($parte["nome"])) {
                if (!isset($parte['tipo'])) {
                    $parte['tipo'] = $parte['descricao'];
                }
                continue;
            }

            if (!isset($parte['tipo']) && !isset($parte['descricao'])) {
                $parte['tipo'] = "Não Informada";
            }

            if (!isset($parte['tipo'])) {
                $parte['tipo'] = $parte['descricao'];
            }

            $parte["nome"] = str_replace($abrev, $full, strtolower($parte["nome"]));

            $parte['tipo'] = trim($parte['tipo']);

            $parte["nome"] = isset($parte['nome']) ? str_replace(
                $abrev,
                $full,
                strtolower($parte["nome"])
            ) : "Não Encontrado";

            $parte["nome"] = preg_replace(
                array(
                    "/(á|à|ã|â|ä)/",
                    "/(Á|À|Ã|Â|Ä)/",
                    "/(é|è|ẽ|ê|ë)/",
                    "/(É|È|Ẽ|Ê|Ë)/",
                    "/(í|ì|ĩ|î|ï)/",
                    "/(Í|Ì|Ĩ|Î|Ï)/",
                    "/(ó|ò|õ|ô|ö)/",
                    "/(Ó|Ò|Õ|Ô|Ö)/",
                    "/(ú|ù|ũ|û|ü)/",
                    "/(Ú|Ù|Ũ|Û|Ü)/",
                    "/(ç)/",
                    "/(Ç)/",
                    "/(ñ)/",
                    "/(Ñ)/"
                ),
                explode(" ", "a A e E i I o O u U c C n N"),
                $parte["nome"]
            );

            $parte["nome"] = str_replace(".", "", $parte["nome"]);

            if (array_key_exists($parte["tipo"], $replaces)) {
                $parte["tipo"] = $replaces[$parte["tipo"]];
            }

            if ($primeira_parte == '' && $primeira_descricao == '') {
                $primeira_parte = strtoupper(($parte["nome"]));
                $primeira_descricao = strtoupper($parte["tipo"]);
            }

            if (strtolower($parte["nome"]) == strtolower($nome)) {
                $parteMaisProxima['nome_mais_proximo']['nome'] = strtoupper($parte["nome"]);
                $parteMaisProxima['nome_mais_proximo']['descricao'] = $parte["tipo"];

                @$descricoes['descricao'][$parte["tipo"]]++;
                if (!in_array($parte["tipo"], $descricoes['descricoes'])) {
                    $descricoes['descricoes'][] = $parte["tipo"];
                }

                break;
            } else {
                $prox = $this->recursiveStriPos($parte["nome"], $nomes);
                if (is_array($prox)) {
                    $nomeMaisProximo[$prox['aprox']] = array("nome" => $parte["nome"], "descricao" => $parte["tipo"]);
                }
            }
        }

        // Seta a parte mais proxima caso encontre
        if (!empty($nomeMaisProximo) && !isset($parteMaisProxima['nome_mais_proximo']['nome'])) {
            // pega a key da parte mais proxima
            $nameKey = max(array_keys($nomeMaisProximo));

            $parteMaisProxima['nome_mais_proximo']['nome'] = strtoupper($nomeMaisProximo[$nameKey]["nome"]);
            $parteMaisProxima['nome_mais_proximo']['descricao'] = $nomeMaisProximo[$nameKey]["descricao"];

            @$descricoes['descricao'][$parte["tipo"]]++;
            if (!in_array($parte["tipo"], $descricoes['descricoes'])) {
                $descricoes['descricoes'][] = $parte["tipo"];
            }
        }

        if (empty($nomeMaisProximo) && !isset($parteMaisProxima['nome_mais_proximo']['nome'])) {
            $parteMaisProxima['nome_mais_proximo']['nome'] = $primeira_parte;
            $parteMaisProxima['nome_mais_proximo']['descricao'] = $primeira_descricao;
        }

        return ['parteMaisProxima' => $parteMaisProxima, 'descricoes' => $descricoes];
    }

    public function sortByRelevancia($a, $b)
    {
        if ($a->estado == $b->estado) {
            return 0;
        }
        return ($a->estado > $b->estado) ? -1 : 1;
    }

    private function recursivePgUnescapeBytea($dados)
    {
        if (!is_array($dados)) {
            $dados = json_decode(pg_unescape_bytea($dados), true);
        }
        foreach ($dados as $key => $dado) {
            if (is_array($dado)) {
                $dados[$key] = self::recursivePgUnescapeBytea($dado);
            } else {
                $dados[$key] = pg_unescape_bytea(pg_unescape_bytea($dado));
            }
        }
        return $dados;
    }

    /*
    *Função Criada para quebrar os dados em colunas pra exibir na tela
    *recebe o total de colunas desejado e os dados
    *retorna um array com as colunas
    */
    private function breakIntoColuns($totalColunas, $dados)
    {
        //criado para sempre iniciar o array na posisao 0 caso o array vem com outro indice
        $newDados = array();
        foreach ($dados as $key => $value) {
            $newDados[] = $value;
        }


        $totalItens = count($newDados);
        $itensColuna = $totalItens / $totalColunas;

        $colunas = array();
        $resultado = array();

        // Caso O total de itens seja menor que o total de colunas seta um para cada coluna
        if ($totalColunas >= $totalItens) {
            foreach ($newDados as $key => $value) {
                $resultado['coluna' . $key][] = $value;
            }
            return $resultado;
        }


        for ($i = 0; $i < $totalColunas + 1; $i++) {
            $colunas[] = "coluna" . $i;
        }

        if ($itensColuna > 1) {
            $itens = 0;
            foreach ($colunas as $key => $coluna) {
                for ($i = $itens; $i < $itensColuna * $key; $i++) {
                    $resultado[$coluna][] = $newDados[$i];
                }
                $itens = $itensColuna * $key;
            }
        }

        return $resultado;
    }

    /**
     * Pesquisa dos ids de consulta por id_pesquisa_consulta
     * É feito desta maneira na verificação se existe resultado
     *
     * @param int $id_pesquisa_consulta
     * @param int $captura_id
     * @return array
     * @version 1.0.0
     * <AUTHOR> Mesquita 22/05/2019
     *
     */
    private function getConsultasIdsByIdPesquisaConsulta($id_pesquisa_consulta, $captura_id)
    {
        if (isset($_GET['id_dossie'])) {
            $dossieId = preg_replace("/[^0-9]+/", "", $_GET['id_dossie']);
        }

        $db = DB::connection('upminer');

        $id_pesquisa_consulta = isset($_GET['id_dossie']) ? $_GET['id_dossie'] : $id_pesquisa_consulta;

        $searchIds = $db->table("pesquisa_consulta")->select('id')
            ->where(function ($q) use ($id_pesquisa_consulta) {
                $q->where('id_pesquisa_consulta', $id_pesquisa_consulta)
                    ->where('estado', '=', '4')
                    ->orWhere('id', $id_pesquisa_consulta);
            })
            ->where('captura', $captura_id)
            ->orderBy('id')
            ->get();

        return $searchIds;
    }

    /*
    *Recupera o(s) correto (s) das fontes com mais de um resultado
    */
    private function getConsultaIds($id_dossie, $id_captura, $onlyIDs = false)
    {
        if (isset($_GET['id_dossie'])) {
            $id_dossie = $_GET['id_dossie'];
        }

        $db = DB::connection('upminer');

        $IdsConsulta = $db->table("pesquisa_consulta")->select('id', 'referencia', 'estado')
            ->where([
                ['id', '=', $id_dossie],
                ['captura', '=', $id_captura],
            ])
            ->orWhere([
                ['id_pesquisa_consulta', '=', $id_dossie],
                ['captura', '=', $id_captura],
            ])->orderBy('id');

        if ($onlyIDs) {
            $IdsConsulta = $IdsConsulta->pluck('id')
                ->toArray();
        } else {
            $IdsConsulta = $IdsConsulta->get();
        }

        // dd($IdsConsulta, $id_captura);

        return $IdsConsulta;
    }

    /**
     *Recupera o(s) id_pequisa_consulta correto (s) das fontes com mais de um resultado
     *
     * @param int $dossierId
     * @param int $captureId
     * @return array()
     * @version 1.0.0
     * <AUTHOR> Melo - 26/04/2018
     * @review:
     *
     */
    private function getSearchIds($dossierId, $captureId)
    {
        if (isset($_GET['id_dossie'])) {
            $dossierId = $_GET['id_dossie'];
        }

        $db = DB::connection('upminer');


        $searchIds = $db->table("pesquisa_consulta")->select('id')
            ->where(function ($q) use ($dossierId) {
                $q->where('id_pesquisa_consulta', $dossierId)
                    ->where('estado', '=', '4')
                    ->orWhere(function ($q) use ($dossierId) {
                        $q->where('id', $dossierId)
                            ->whereNull('id_pesquisa_consulta');
                    });
            })
            ->where('captura', $captureId)
            ->orderBy('id')
            ->get();

        $ids = [];
        if (empty($searchIds)) {
            return false;
        }
        foreach ($searchIds as $key => $id) {
            $ids[] = $id->id;
        }

        return $ids;
    }

    private function pgrProcesso(CapturaModel $captura, $consulta, $resumo = false)
    {
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $dbu = DB::connection('upminer');

        $processo_classificacao = $dbu->table("processo_classificacao")->select()
            ->where('id_pesquisa_consulta', '=', $consulta->id)
            ->where('id_captura', '=', $captura->id)
            ->orderBy('id', 'DESC')->get();


        if (empty($processo_classificacao)) {
            return [];
        }

        foreach ($processo_classificacao as $key => $d) {
            $processo_classificacao[$key]->dado = unserialize(Funcoes::RdsCaptureData($d->dado));
        }

        if (!$processo_classificacao) {
            return [];
        }

        return $processo_classificacao;
    }


    //  /**
    //   * World Check - 10
    //   */
    // private function worldCheck(CapturaModel $captura, $consulta, $resumo = false)
    // {
    //      if (empty($captura->tabela)) {
    //          throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
    //      }

    //      $processo_classificacao = \App\Models\Cliente\ProcessoClassificacao::select('id')->where('id_pesquisa_consulta', '=', $consulta->id)->get();

    //      if ($processo_classificacao)
    //      {

    //      } else return [];
    // }

    /**
     * World Check Local - 55
     */
    private function worldCheckLocal(CapturaModel $captura, $consulta)
    {
        if ($captura->id != $consulta->captura) {
            $consulta = $consulta->getChildrens[0];
        }
        if (empty($captura->tabela)) {
            throw new CustomException("Sem tabela cadastrada na captura: " . $captura->metodo, 1);
        }

        $dbu = DB::connection('upminer');
        $parsed = [];
        /**
         *  se for new lambda captura essa variável conterá todos os resultados
         */
        $parciais = $dbu->table("processo_classificacao")
            ->where('id_pesquisa_consulta', '=', $consulta->id)
            ->where('id_captura', '=', $captura->id)
            ->orderBy('id', 'DESC')->get();
        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            foreach ($parciais as $key => $data) {
                $dadoDecoded = json_decode($data->dado, true);
                $dadoDecoded['conteudo_relevante'] = $this->getRelevance($data->estado);
                $parsed [] = $dadoDecoded;
            }
            return $parsed;
        }

        $db = DB::connection('pgsql_captura3');

        $exatos = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $consulta->referencia)
            ->first();
        $exatos = (isset($exatos->dado)) ? Funcoes::RdsCaptureData($exatos->dado) : [];
        if (Funcoes::isSerialize($exatos)) {
            $exatos = str_replace('firstName', 'firstname', $exatos);
            $exatos = str_replace('lastName', 'lastname', $exatos);
            $exatos = str_replace('lastName', 'lastname', $exatos);
            $exatos = str_replace('externalSources', 'external_sources', $exatos);
            $exatos = str_replace('linkedTo', 'linked_to', $exatos);
            $exatos = Funcoes::voToObject($exatos);
        }

        if (empty($exatos) && empty($parciais)) {
            return [];
        }

        foreach ($exatos as $key => $dado) {
            $dado['match'] = str_replace("exata", "exato", $dado['match'] ?? "exato");
            $dado['conteudo_relevante'] = null;
            $parsed [] = $dado;
        }
        foreach ($parciais as $key => $d) {
            $d->dado = unserialize(Funcoes::RdsCaptureData($d->dado));
            $d->dado['match'] = str_replace("exata", "exato", $d->dado['match']);
            $d->dado['conteudo_relevante'] = $this->getRelevance($d->estado);
            $parsed[] = $d->dado;
        }

        return $parsed;
    }

    private function cmp($a, $b)
    {
        return $a['nome'] > $b['nome'];
    }

    /**
     * Recupera e formata os dados da fonte EmpresasPunidasSp
     *
     * @return void
     * <AUTHOR> Machado <<EMAIL>>
     * @version 1.0.0
     */
    private function EmpresasPunidasSp(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');
        $criterio = explode("|", $consulta->criterio);

        $nomes = $this->getCriterioName($consulta->id_lote_consulta, $criterio[0]);
        $nome = (is_array($nomes)) ? implode(" ", $nomes) : $nomes;

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table($captura->tabela)->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $parsedData[] = json_decode($value->dado);
            }
        }

        return $parsedData;
    }


    /**
     * Tse Doadores Lambda
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     * @return void
     * @version 1.0.0 - Função Tse Doadores
     * @version 1.1.0 - Jefferson Mesquita 24/05/2019 - Correção no array, as vezes o indice é 0 e outras é o ano
     */
    private function tseDoadores(CapturaModel $captura, $consulta, $resumo = false)
    {
        $results = $this->__default($captura, $consulta, $resumo);
        if (is_string($results)) {
            $results = json_decode($results, true);
        }

        if (!empty($consulta->data_faturamento)) {
            $dataCriacao = explode(" ", $consulta->data_faturamento);
        } else {
            if (!empty($consulta->data_estado)) {
                $dataCriacao = explode(" ", $consulta->data_estado);
            } else {
                if (!empty($consulta->data_criacao)) {
                    $dataCriacao = explode(" ", $consulta->data_criacao);
                } else {
                    $dataCriacao = array();
                }
            }
        }

        if ($captura->data_migracao_lambda != '' && strtotime($captura->data_migracao_lambda) <= strtotime(
                @$dataCriacao[0]
            )) {
            $fixData = [];

            if (is_array($results) && !empty($results)) {
                foreach ($results as $key => $tse) {
                    if (count($tse) > 0) {
                        if (isset($tse[0])) {
                            $tse = $tse[0];
                        }

                        $fixData[$tse['ano']]['nome'] = $tse['nome'];
                        $fixData[$tse['ano']]['tipo'] = $tse['tipo'];
                        $fixData[$tse['ano']]['cpfCnpj'] = $tse['cpfCnpj'];
                        $fixData[$tse['ano']]['ano'] = $tse['ano'];
                        $fixData[$tse['ano']]['existeResultado'] = false;

                        //se nao existir cria
                        if (!isset($fixData[$tse['ano']]['despesa'])) {
                            $fixData[$tse['ano']]['despesa'] = [];
                        }
                        if (!isset($fixData[$tse['ano']]['receita'])) {
                            $fixData[$tse['ano']]['receita'] = [];
                        }
                        if (!isset($fixData[$tse['ano']]['doador'])) {
                            $fixData[$tse['ano']]['doador'] = [];
                        }
                        if (!isset($fixData[$tse['ano']]['fornecedor'])) {
                            $fixData[$tse['ano']]['fornecedor'] = [];
                        }

                        if (array_key_exists("despesaReceita", $tse)) {
                            if (isset($tse['despesaReceita'][0])) {
                                $fixData[$tse['ano']]['despesa']['candidato'][] = $tse['despesaReceita'][0];
                            }
                            if (isset($tse['despesaReceita'][1])) {
                                $fixData[$tse['ano']]['despesa']['comite'][] = $tse['despesaReceita'][1];
                            }


                            if (isset($tse['despesaReceita'][2])) {
                                $fixData[$tse['ano']]['receita']['candidato'][] = $tse['despesaReceita'][2];
                            }
                            if (isset($tse['despesaReceita'][3])) {
                                $fixData[$tse['ano']]['receita']['comite'][] = $tse['despesaReceita'][3];
                            }
                        } else {
                            if (is_array(@$tse['despesa']) && !empty(@$tse['despesa'])) {
                                if (is_array(@$tse['despesa']['candidato']) && !empty(@$tse['despesa']['candidato'])) {
                                    foreach ($tse['despesa']['candidato'] as $kd => $despesa) {
                                        if (!isset($fixData[$tse['ano']]['despesa'][$kd])) {
                                            $fixData[$tse['ano']]['despesa']['candidato'][$kd] = [];
                                        }
                                        if (is_array($despesa) && !empty($despesa)) {
                                            $fixData[$tse['ano']]['despesa']['candidato'][$kd] = $despesa;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                } else {
                                    foreach ($tse['despesa'] as $kd => $despesa) {
                                        if (!isset($fixData[$tse['ano']]['despesa'][$kd])) {
                                            $fixData[$tse['ano']]['despesa'][$kd] = [];
                                        }
                                        if (is_array($despesa) && !empty($despesa)) {
                                            $fixData[$tse['ano']]['despesa'][$kd] = $despesa;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                }
                            }

                            if (is_array(@$tse['receita']) && !empty(@$tse['receita'])) {
                                if (is_array(@$tse['receita']['candidato']) && !empty(@$tse['receita']['candidato'])) {
                                    foreach ($tse['receita']['candidato'] as $kr => $receita) {
                                        if (!isset($fixData[$tse['ano']]['receita'][$kr])) {
                                            $fixData[$tse['ano']]['receita']['candidato'][$kr] = [];
                                        }
                                        if (is_array($receita) && !empty($receita)) {
                                            $fixData[$tse['ano']]['receita']['candidato'][$kr] = $receita;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                } else {
                                    foreach ($tse['receita'] as $kr => $receita) {
                                        if (!isset($fixData[$tse['ano']]['receita'][$kr])) {
                                            $fixData[$tse['ano']]['receita'][$kr] = [];
                                        }
                                        if (is_array($receita) && !empty($receita)) {
                                            $fixData[$tse['ano']]['receita'][$kr] = $receita;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                }
                            }

                            if (is_array(@$tse['doador']) && !empty(@$tse['doador'])) {
                                if ($tse['ano'] == 2002 || $tse['ano'] == 2004 || $tse['ano'] == 2006) {
                                    foreach ($tse['doador']['doador'] as $kr => $doador) {
                                        if (!isset($fixData[$tse['ano']]['doador'][$kr])) {
                                            $fixData[$tse['ano']]['doador']['doador'][$kr] = [];
                                        }
                                        if (is_array($doador) && !empty($doador)) {
                                            $fixData[$tse['ano']]['doador']['doador'][$kr] = $doador;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                }
                            }

                            if (is_array(@$tse['fornecedor']) && !empty(@$tse['fornecedor'])) {
                                if ($tse['ano'] == 2002 || $tse['ano'] == 2004 || $tse['ano'] == 2006) {
                                    foreach ($tse['fornecedor']['fornecedor'] as $kr => $fornecedor) {
                                        if (!isset($fixData[$tse['ano']]['fornecedor'][$kr])) {
                                            $fixData[$tse['ano']]['fornecedor']['fornecedor'][$kr] = [];
                                        }
                                        if (is_array($fornecedor) && !empty($fornecedor)) {
                                            $fixData[$tse['ano']]['fornecedor']['fornecedor'][$kr] = $fornecedor;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                }
                            }
                        }
                    }
                }
                unset($results);
            }
        } else {
            $fixData = [];

            if (is_array($results) && !empty($results)) {
                foreach ($results as $key => $tse) {
                    if (count($tse) > 0) {
                        if (@is_array($tse[0])) {
                            $fixData[$tse[0]['ano']]['uf'] = @$tse[0]['uf'];
                            $fixData[$tse[0]['ano']]['municipio'] = @$tse[0]['municipio'];
                            $fixData[$tse[0]['ano']]['cargo'] = @$tse[0]['cargo'];
                            $fixData[$tse[0]['ano']]['nome'] = @$tse[0]['nome'];
                            $fixData[$tse[0]['ano']]['tipo'] = @$tse[0]['tipo'];
                            $fixData[$tse[0]['ano']]['tipoComite'] = @$tse[0]['tipoComite'];
                            $fixData[$tse[0]['ano']]['partido'] = @$tse[0]['partido'];
                            $fixData[$tse[0]['ano']]['cpfCnpj'] = @$tse[0]['cpfCnpj'];
                            $fixData[$tse[0]['ano']]['ano'] = $tse[0]['ano'];
                            $fixData[$tse[0]['ano']]['existeResultado'] = false;

                            //se nao existir cria
                            if (!isset($fixData[$tse[0]['ano']]['despesa'])) {
                                $fixData[$tse[0]['ano']]['despesa'] = [];
                            }
                            if (!isset($fixData[$tse[0]['ano']]['receita'])) {
                                $fixData[$tse[0]['ano']]['receita'] = [];
                            }

                            if (array_key_exists("despesaReceita", $tse)) {
                                if (isset($tse[0]['despesaReceita'][0])) {
                                    $fixData[$tse[0]['ano']]['despesa']['candidato'][] = $tse[0]['despesaReceita'][0];
                                }
                                if (isset($tse[0]['despesaReceita'][1])) {
                                    $fixData[$tse[0]['ano']]['despesa']['comite'][] = $tse[0]['despesaReceita'][1];
                                }


                                if (isset($tse[0]['despesaReceita'][2])) {
                                    $fixData[$tse[0]['ano']]['receita']['candidato'][] = $tse[0]['despesaReceita'][2];
                                }
                                if (isset($tse[0]['despesaReceita'][3])) {
                                    $fixData[$tse[0]['ano']]['receita']['comite'][] = $tse[0]['despesaReceita'][3];
                                }
                                $fixData[$tse[0]['ano']]['existeResultado'] = true;
                            } else {
                                if (is_array(@$tse[0]['despesa']) && !empty(@$tse[0]['despesa'])) {
                                    foreach ($tse[0]['despesa'] as $kd => $despesa) {
                                        if (!isset($fixData[$tse[0]['ano']]['despesa'][$kd])) {
                                            $fixData[$tse[0]['ano']]['despesa'][$kd] = [];
                                        }
                                        if (is_array($despesa) && !empty($despesa)) {
                                            $fixData[$tse[0]['ano']]['despesa'][$kd] += $despesa;
                                        }
                                        $fixData[$tse[0]['ano']]['existeResultado'] = true;
                                    }
                                }

                                if (is_array(@$tse[0]['receita']) && !empty(@$tse[0]['receita'])) {
                                    foreach ($tse[0]['receita'] as $kr => $receita) {
                                        if (!isset($fixData[$tse[0]['ano']]['receita'][$kr])) {
                                            $fixData[$tse[0]['ano']]['receita'][$kr] = [];
                                        }
                                        if (is_array($receita) && !empty($receita)) {
                                            $fixData[$tse[0]['ano']]['receita'][$kr] = $receita;
                                        }
                                        $fixData[$tse[0]['ano']]['existeResultado'] = true;
                                    }
                                }
                            }
                        } else {
                            $fixData[$tse['ano']]['uf'] = @$tse['uf'];
                            $fixData[$tse['ano']]['municipio'] = @$tse['municipio'];
                            $fixData[$tse['ano']]['cargo'] = @$tse['cargo'];
                            $fixData[$tse['ano']]['nome'] = @$tse['nome'];
                            $fixData[$tse['ano']]['tipo'] = @$tse['tipo'];
                            $fixData[$tse['ano']]['tipoComite'] = @$tse['tipoComite'];
                            $fixData[$tse['ano']]['partido'] = @$tse['partido'];
                            $fixData[$tse['ano']]['cpfCnpj'] = @$tse['cpfCnpj'];
                            $fixData[$tse['ano']]['ano'] = $tse['ano'];
                            $fixData[$tse['ano']]['existeResultado'] = false;

                            //se nao existir cria
                            if (!isset($fixData[$tse['ano']]['despesa'])) {
                                $fixData[$tse['ano']]['despesa'] = [];
                            }
                            if (!isset($fixData[$tse['ano']]['receita'])) {
                                $fixData[$tse['ano']]['receita'] = [];
                            }

                            if (array_key_exists("despesaReceita", $tse)) {
                                if (isset($tse['despesaReceita'][0])) {
                                    $fixData[$tse['ano']]['despesa']['candidato'][] = $tse['despesaReceita'][0];
                                }
                                if (isset($tse['despesaReceita'][1])) {
                                    $fixData[$tse['ano']]['despesa']['comite'][] = $tse['despesaReceita'][1];
                                }


                                if (isset($tse['despesaReceita'][2])) {
                                    $fixData[$tse['ano']]['receita']['candidato'][] = $tse['despesaReceita'][2];
                                }
                                if (isset($tse['despesaReceita'][3])) {
                                    $fixData[$tse['ano']]['receita']['comite'][] = $tse['despesaReceita'][3];
                                }
                                $fixData[$tse['ano']]['existeResultado'] = true;
                            } else {
                                if (is_array(@$tse['despesa']) && !empty(@$tse['despesa'])) {
                                    foreach ($tse['despesa'] as $kd => $despesa) {
                                        if (!isset($fixData[$tse[0]['ano']]['despesa'][$kd])) {
                                            $fixData[$tse['ano']]['despesa'][$kd] = [];
                                        }
                                        if (is_array($despesa) && !empty($despesa)) {
                                            $fixData[$tse['ano']]['despesa'][$kd] += $despesa;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                }

                                if (is_array(@$tse['receita']) && !empty(@$tse['receita'])) {
                                    foreach ($tse['receita'] as $kr => $receita) {
                                        if (!isset($fixData[$tse[0]['ano']]['receita'][$kr])) {
                                            $fixData[$tse['ano']]['receita'][$kr] = [];
                                        }
                                        if (is_array($receita) && !empty($receita)) {
                                            $fixData[$tse['ano']]['receita'][$kr] = $receita;
                                        }
                                        $fixData[$tse['ano']]['existeResultado'] = true;
                                    }
                                }
                            }
                        }
                    }
                }
                unset($results);
            }
        }

        return $fixData;
    }

    /**
     * Recupera e formata os dados da fonte Info4c - Pep Desk [257]
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Machado - 2019-04-17
     * Revisão
     * @version 1.0.0
     */
    private function info4cPep(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->info4c($captura, $consulta, $resumo);
    }

    /**
     * Recupera e formata os dados da fonte Info4c - Sacntion Lists [258]
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Machado - 2019-04-17
     * Revisão
     * @version 1.0.0
     */
    private function info4cSanctionList(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->info4c($captura, $consulta, $resumo);
    }

    /**
     * Recupera e formata os dados da fonte Info4c - Watchlists & Blacklists [259]
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Machado - 2019-04-17
     * Revisão
     * @version 1.0.0
     */
    private function info4cWatchList(CapturaModel $captura, $consulta, $resumo = false)
    {
        return $this->info4c($captura, $consulta, $resumo);
    }

    /**
     * Recupera e formata os dados da fonte Info4c - Full Search [271]
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Machado - 2019-06-26
     * Revisão
     * @version 1.0.0
     */
    private function info4cFullSearch(CapturaModel $captura, $consulta, $resumo = false)
    {
        $info4cResult = $this->info4c($captura, $consulta, $resumo);
        $finalResult = [
            'info4cPep' => [],
            'info4cWatchList' => [],
            'info4cSanctionList' => []
        ];
        foreach ($info4cResult['dados'] as $result) {
            switch ($result->dado->source) {
                case 'Info4cPep':
                    $finalResult['info4cPep']['dados'][] = $result;
                    break;
                case 'Info4cWatchList':
                    $finalResult['info4cWatchList']['dados'][] = $result;
                    break;
                case 'Info4cSanctionList':
                    $finalResult['info4cSanctionList']['dados'][] = $result;
                    break;
            }
        }
        return $finalResult;
    }

    /**
     * Busca informações dentro da tabela processo_classificacao
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Machado - 2019-04-17
     * Revisão
     * Revisão Jefferson Mesquita 24/05/2019 - Troca da função getConsultasIds por getConsultasIdsByIdPesquisaConsulta
     * @version 1.0.0
     */
    private function info4c(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData['dados'][] = $value;
            }
        }
        return $parsedData;
    }

    /**
     * Busca informações dentro da tabela processo_classificacao
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Machado - 2019-08-13
     * Revisão
     * @version 1.0.0
     */
    private function cialDnB(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado, true);
                $value->dado['conteudo_relevante'] = $this->getRelevance($value->conteudo_relevante);
                $parsedData[] = [$value->dado];
            }
        }
        return $parsedData;
    }

    /**
     * Busca informações dentro da tabela processo_classificacao
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Mesquita 04/06/2019
     * Revisão
     * @version 1.0.0
     */
    private function SncrSerpro(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $value->conteudo_relevante = !empty($value->conteudo_relevante) ?
                    $this->getRelevance($value->conteudo_relevante) : 'default';
                $parsedData[] = $value;
            }
        }

        return $parsedData;
    }

    /**
     * Busca informações dentro da tabela processo_classificacao
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     * @return array
     * <AUTHOR> Mesquita 30/07/2019
     * Revisão
     * @version 1.0.0
     */
    private function Cfp(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $value->dado->conteudo_relevante = !empty($value->conteudo_relevante) ? $value->conteudo_relevante : 'default';
                $parsedData[] = $value->dado;
            }
        }
        return $parsedData;
    }

    private function Crq(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData['dados'][] = $value;
            }
        }
        return $parsedData;
    }

    private function Bndes(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $conteudRelevante['conteudo_relevante'] = $value->conteudo_relevante;
                $dataDado = json_decode($value->dado, true);
                $result = array_merge($dataDado, $conteudRelevante);
                $parsedData[] = $result;
            }
        }

        return $parsedData;
    }

    /**
     * (92) CVM - Documentos
     *
     * @param CapturaModel $captura
     * @param mixed $consulta
     * @param boolean $resumo
     *
     * @return array
     * @version 1.0
     *
     * <AUTHOR> Mesquita 13/08/2019
     *
     */
    private function cvmDocumentos(CapturaModel $captura, $consulta, $resumo = false)
    {
        $arquivo = $this->__default($captura, $consulta, $resumo);

        if ($this->isNewLambdaCaptura($captura, $consulta)) {
            if (is_array($arquivo)) {
                return $arquivo;
            }
            $arquivo = json_decode($arquivo, true);
            if (isset($arquivo['data'])) {
                return $arquivo['data'];
            }
        }
        return $arquivo;
    }

    /**
     * (275) InepIeb - Ensino Básico
     *
     * @param CapturaModel $captura
     * @param object $consulta
     * @param boolean $resumo
     *
     * @return array
     * @version 1.0.0
     *
     * <AUTHOR> Mesquita 13/08/2019
     *
     */
    private function InepIeb(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado, true);
                $value->dado['conteudo_relevante'] = $this->getRelevance($value->conteudo_relevante);
                $parsedData[] = $value->dado;
            }
        }

        return $parsedData;
    }

    private function EmecIes(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData['dados'][] = $value;
            }
        }

        return $parsedData;
    }

    private function Djen(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado, true);
                $value->dado['conteudo_relevante'] = !empty($value->dado['conteudo_relevante']) ?
                    $this->getRelevance($value->dado['conteudo_relevante']) : 'default';
                $parsedData[] = $value->dado;
            }
        }

        return $parsedData;
    }


    private function BingNews(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $arquivo = Funcoes::RdsCaptureData($value->dado);

                $value->dado = json_decode($arquivo, true);
                $value->dado['conteudo_relevante'] = $this->getRelevance($value->estado);
                $parsedData[] = $value->dado;
            }
        }

        return $parsedData;
    }

    private function WorldBank(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData['dados'][] = $value;
            }
        }

        return $parsedData;
    }


    private function LexisNexisBridgerInsightPf(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData['dados'][] = $value;
            }
        }

        return $parsedData;
    }

    private function LexisNexisBridgerInsightPj(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $value->dado->conteudo_relevante = 'default';
                $parsedData[] = $value->dado;
            }
        }

        return $parsedData;
    }

    private function transparenciaPep(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData['dados'][] = $value;
            }
        }

        return $parsedData;
    }

    private function Cuit(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $value->dado->conteudo_relevante = $this->getRelevance($value->estado);
                $parsedData[] = $value->dado;
            }
        }

        return $parsedData;
    }

    private function CqbProcesso(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $parsedData[] = $value;
                $parsedData['conteudo_relevante'] = $this->getRelevance($value->conteudo_relevante);
            }
        }
        return $parsedData;
    }

    private function baseEmpresas($captura, $consulta, $resumo = false)
    {
        $db = DB::connection('pgsql_captura3');
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);
        if (empty($IdsConsulta)) {
            return [];
        }

        $results = $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->where($captura->tabela . '_consulta.id', $IdsConsulta[0]->referencia)
            ->first();

        // baseEmpresas

        if (empty($results)) {
            return null;
        }
        $dado = Funcoes::RdsCaptureData($results->dado);
        if (is_string($dado) && strpos($dado, "a:") === 0) {
            return Funcoes::voToObject($dado);
        }
        return $dado;
    }

    private function BsmPad(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $dossierID = $consulta->id_pesquisa_consulta ? $consulta->id_pesquisa_consulta : $consulta->id;
        $IdsConsulta = $this->getConsultaIds($dossierID, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $conteudRelevante['conteudo_relevante'] = $value->conteudo_relevante;
                $dataDado = json_decode($value->dado, true);
                $result = array_merge($dataDado, $conteudRelevante);
                $parsedData[] = $result;
            }
        }

        return $parsedData;
    }

    private function dataNews(CapturaModel $captura, $consulta, $resumo = false)
    {
        $db = DB::connection('upminer');

        $parsedData = array();
        $IdsConsulta = $this->getConsultasIdsByIdPesquisaConsulta($consulta->id, $captura->id);

        foreach ($IdsConsulta as $key => $ids) {
            $query = $db->table('processo_classificacao')->select()
                ->where('id_pesquisa_consulta', '=', $ids->id)
                ->where('id_captura', '=', $captura->id)
                ->orderBy('id');

            $data = $query->get();
            foreach ($data as $key => $value) {
                $value->dado = json_decode($value->dado);
                $value->dado->conteudo_relevante = $this->getRelevance($value->estado);
                $parsedData[] = $value->dado;
            }
        }
        return $parsedData;
    }

    private function getRefs(int $dossieId, string $criterio): array
    {
        $db = DB::connection('upminer');
        $refs = $db->table("seguranca.summary_ref")->select('id_ref')
            ->join('summary_relevance', 'summary_relevance.id_summary_ref', '=', 'seguranca.summary_ref.id')
            ->where('summary_relevance.id_dossie', $dossieId)
            ->where('seguranca.summary_ref.criterio', 'like', "%{$criterio}%")
            ->get();
        return $refs->pluck('id_ref')->toArray();
    }

    private function ChatGPT(CapturaModel $captura, $consulta, $resumo = false)
    {
        $criterio = explode('|', $consulta->criterio)[0];
        $idRefs = $this->getRefs($consulta->id, $criterio);
        if (empty($idRefs)) {
            return [];
        }

        $db = DB::connection('pgsql_captura3');

        return $db->table($captura->tabela . '_consulta')
            ->select('dado', 'data_hora')
            ->join($captura->tabela, $captura->tabela . '.id', '=', $captura->tabela . '_consulta.id_dado')
            ->whereIn($captura->tabela . '_consulta.id', $idRefs)
            ->get()
            ->map(function ($item) {
                return json_decode($item->dado);
            })->toArray();
    }
}
