<?php

declare(strict_types=1);

namespace App\Domains\Reaction\Repositories;

use DossieSourcesModule\Sources\Entities\Capture;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use DossieSourcesModule\Sources\Entities\Cliente\DossieSourceReactions;

class DossieSourceReactionsRepository
{
    public function getReactions(array $queryParams): array
    {
        $data = DossieSourceReactions::where('dossie_id', $queryParams['dossieID'])
            ->where('source_id', $queryParams['dossieID'])
            ->get();

        $result = [];
        if (!empty($data)) {
            return $data->toArray();
        }

        return $result;
    }

    public function insertReactionSource(int $dossieID, array $inputs, int $userID): bool
    {
        $data = DossieSourceReactions::updateOrCreate(
            ['dossie_id' => $dossieID, 'source_id' => $inputs['source']],
            ['reaction_id' => $inputs['reaction'], 'user_id' => $userID]
        );

        if (!empty($data)) {
            return true;
        }

        return false;
    }

    public function deleteReactionSource(int $dossieID, int $sourceID): bool
    {
        $data = DossieSourceReactions::where(
            ['dossie_id' => $dossieID, 'source_id' => $sourceID]
        )->delete();
        if (!empty($data)) {
            return true;
        }

        return false;
    }
}
