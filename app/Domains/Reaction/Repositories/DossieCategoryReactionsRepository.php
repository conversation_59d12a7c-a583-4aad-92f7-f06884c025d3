<?php

declare(strict_types=1);

namespace App\Domains\Reaction\Repositories;

use Illuminate\Database\Eloquent\Collection;
use DossieSourcesModule\Sources\Entities\Cliente\DossieCategoryReactions;

class DossieCategoryReactionsRepository
{
    private DossieCategoryReactions $dossieCategoryReactions;

    public function __construct(
        DossieCategoryReactions $dossieCategoryReactions
    ) {
        $this->dossieCategoryReactions = $dossieCategoryReactions;
    }

    public function getReactions(array $queryParams): array
    {
        $data = $this->dossieCategoryReactions->where('dossie_id', $queryParams['dossieID'])
            ->where('source_group_alias', $queryParams['category'])
            ->get();

        $result = [];
        if (!empty($data)) {
            return $data->toArray();
        }

        return $result;
    }

    public function insert(int $dossieID, array $params, int $userID): bool
    {
        $data = $this->dossieCategoryReactions->updateOrCreate(
            ['dossie_id' => $dossieID, 'source_group_alias' => $params['category']],
            ['reaction_id' => $params['reaction'], 'user_id' => $userID]
        );

        if (!empty($data)) {
            return true;
        }

        return false;
    }

    public function delete(int $dossieID, string $category): bool
    {
        $data = $this->dossieCategoryReactions->where(
            ['dossie_id' => $dossieID, 'source_group_alias' => $category]
        )->delete();
        if (!empty($data)) {
            return true;
        }

        return false;
    }
}
