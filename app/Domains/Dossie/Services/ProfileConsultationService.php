<?php

namespace App\Domains\Dossie\Services;

use App\Actions\Score\ScoreApiClient;
use App\Domains\AuroraLog\Services\AuroraLogService;
use App\Domains\Dossie\Actions\ProfileConsultation\CreateNewGroupsByProfileAction;
use App\Domains\Dossie\Actions\ProfileConsultation\GetAllAvailableSourcesForProfileAction;
use App\Domains\Dossie\Actions\ProfileConsultation\GetSourcesByProfileAction;
use App\Domains\Dossie\Actions\ProfileConsultation\GetSourcesParametersByTypeAction;
use App\Domains\Dossie\Actions\ProfileConsultation\InactivateAllMonitoringForProfileAction;
use App\Domains\Dossie\Actions\ProfileConsultation\ProcessNewProfileAction;
use App\Domains\Dossie\Actions\ProfileConsultation\SetProfileStructureNivelAction;
use App\Domains\Dossie\Bundles\ProfileConsultationBundle;
use App\Domains\Dossie\Entities\Client\Consulta;
use App\Domains\Dossie\Entities\Dossie\ConsultaPerfilDefault;
use App\Domains\Dossie\Helpers\UFHelper;
use App\Domains\Dossie\Repositories\ChatGptPermissionRepository;
use App\Domains\Dossie\Repositories\GrupoRepository;
use App\Domains\Dossie\Repositories\ProfileConsultationRepository;
use App\Domains\Dossie\Transforms\TransformAllAvailableSourcesForProfile;
use App\Domains\Dossie\Transforms\TransformUpScoreProfiles;
use App\Domains\Monitoria\Repositories\MonitoriaDossieRepository;
use App\Domains\User\Services\UserGroupService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\CustomException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Throwable;
use Carbon\Carbon;
use App\Domains\Dossie\Helpers\DocumentoHelper;
use App\Domains\Dossie\Actions\ExtraDossiers\SpineSociosClient;

class ProfileConsultationService
{
    public function __construct(
        private readonly ProfileConsultationRepository $profileConsultationRepository,
        private readonly GrupoRepository $grupoRepository,
        private readonly UserGroupService $userGroupService,
        private readonly ChatGptPermissionRepository $chatGptPermissionRepository,
        private readonly MonitoriaDossieRepository $monitoriaDossieRepository,
        private readonly AuroraLogService $auroraLogService
    ) {
    }

    public function getById(int $perfilId, array $params = [], $mode = 'custom'): array
    {
        $groups = [];
        $basic = false;
        if (!empty($params['basic'])) {
            $basic = $params['basic'];
        }

        $inactive = false;
        if (!empty($params['inativo'])) {
            $inactive = $params['inativo'];
        }

        if ($basic) {
            $profile = $this->profileConsultationRepository->setMode($mode)->getById(
                $perfilId,
                $basic,
                $inactive
            );

            if (empty($profile)) {
                throw new CustomException(trans('profile.not-found', ['id' => $perfilId]));
            }

            foreach ($profile->getGrupoConsulta as $group) {
                $groups[] = $group->getGrupo->id;
            }

            return [
                'id' => $profile->id,
                'name' => $profile->nome,
                'icon' => $profile->icon_path ?? null,
                'version' => $profile->versao,
                'type' => $profile->tipo_perfil,
                'userGroups' => $groups,
                'objective' => $profile->objetivo
            ];
        }

        $profile = $this->profileConsultationRepository->setMode($mode)->getById(
            $perfilId,
            $basic,
            $inactive
        );

        if (!$profile) {
            throw new CustomException(trans('profile.not-found', ['id' => $perfilId]));
        }

        $captureGroups = $this->grupoRepository->getAllActiveGrupoCaptura();

        foreach ($captureGroups as $captureGroup) {
            if (!isset($groups[$captureGroup->alias])) {
                $groups[$captureGroup->alias] = [
                    'groupName' => $captureGroup->nome,
                    'types' => [],
                    'sources' => []
                ];
            }

            if (!in_array('pf', $groups[$captureGroup->alias]['types']) && $captureGroup->tipo == 1) {
                $groups[$captureGroup->alias]['types'][] = 'pf';
            }

            if (!in_array('pj', $groups[$captureGroup->alias]['types']) && $captureGroup->tipo == 2) {
                $groups[$captureGroup->alias]['types'][] = 'pj';
            }
        }

        $groupUsers = [];

        foreach ($profile->getGrupoConsulta as $searchGroup) {
            $groupUsers[] = $searchGroup->getGrupo->id;
        }

        $result = [
            'id' => $profile->id,
            'name' => $profile->nome,
            'icon' => $profile->icon_path ?? null,
            'version' => $profile->versao,
            'type' => $profile->tipo_perfil,
            'groups' => [],
            'userGroups' => $groupUsers,
            'objective' => $profile->objetivo
        ];

        $groups = app(GetSourcesByProfileAction::class)->execute($profile);
        $result['groups'] = $groups;

        return $result;
    }

    public function getListProfiles(array $params, $mode = 'custom'): array
    {
        $profiles = $this->profileConsultationRepository->setMode($mode)->getPaginatedByFilter($params);

        if (empty($profiles['data'])) {
            return $profiles;
        }

        // Ver se tem upScore nos perfis retornados
        $profilesIds = collect($profiles['data'])->pluck('idPerfil')->toArray();
        $scores = (new ScoreApiClient())->getScorebySearchProfiles($profilesIds);
        $scores = TransformUpScoreProfiles::handle($scores);

        foreach($profiles['data'] as &$profile) {
            $score = Arr::get($scores, $profile['idPerfil']) ?? null;
            $profile['score'] = null;
            $profile['score_active'] = null;

            if (!empty($score)) {
                $profile['score'] = $score['score_id'];
                $profile['score_active'] = $score['active'];
            }
        }

        return $profiles;
    }

    public function getAllProfiles(array $params, $mode = 'custom'): array
    {
        return $this->profileConsultationRepository
            ->setMode($mode)
            ->getAllProfiles($params)
            ->toArray();
    }

    public function listParametersByProfile(array $data): array
    {
        return $this->processEstrutura(
            $this->profileConsultationRepository->setMode($data['tipo'])->fetchStructure($data),
            $data
        );
    }

    public function details(array $data)
    {
        $profile = $this->profileConsultationRepository->setMode($data['mode'])->details($data['idPerfil']);

        $sources=[];
        foreach ($profile->getConsulta as $item) {
           $itemSource = $item->getSaidaCaptura;
            if (!empty($itemSource)) {
                $itemCategorySource = empty($itemSource->getGrupoCapturaPf)
                    ? $itemSource->getGrupoCapturaPj
                    : $itemSource->getGrupoCapturaPf;
                $sources[]= [
                    'sourceName' => $itemSource['nome'],
                    'sourceType' => $itemSource['id_tipo_captura'],
                    'categoryAlias' => $itemCategorySource['alias'],
                    'sourceMethod' => $itemSource['metodo'],
                    'sourceTypePeople' => $itemSource['tipo_pessoa'],
                ];
            }
        }
        $profile->groups = $profile?->getGrupoConsulta?->map(function ($item) {
            return [
                'groupId' => $item?->getGrupo['id'],
                'groupName' => $item?->getGrupo['nome'],
            ];
        });

        $profileUpscore = false;
        if ($data['mode'] == 'custom') {
            $upscore = app(ScoreApiClient::class)->getDetailsScoreProfile($data['idPerfil']);
            $profileUpscore = !empty($upscore);
        }

        $profile->hasUpscore = $profileUpscore;
        $profile->hasWorkflow = !empty($profile?->getGrupoConsulta?->first()?->id_workflow);
        $profile->hasFoxy = $profile?->permissionChat?->isNotEmpty();

        unset($profile['permissionChat'], $profile['getConsulta'], $profile['getGrupoConsulta']);

        return [
            'id' => $profile->id,
            'name' => $profile->name,
            'version' => $profile->version,
            'objective' => $profile->objective,
            'icon' => $profile->icon,
            'sources' => $sources,
            'groups' => $profile->groups,
            'hasUpscore' => $profile->hasUpscore,
            'hasWorkflow'  => $profile->hasWorkflow,
            'hasFoxy'  => $profile->hasFoxy
        ];
    }

    public function getConsultasByGrupo(array $data): array
    {
        return $this->profileConsultationRepository->getConsultasByGrupo([$data['tipo']]);
    }

    private function processEstrutura($estrutura, $data): array
    {
        $resposta = [];
        $perfisExpandidos = [];
        $tipoPessoa = null;
        $prioridadeCriterio = false;
        foreach ($estrutura as $captura) {
            if (!empty($captura['get_captura']) && !empty($data['type_profile'])) {
                $tipoPessoa = $this->setPersonType($captura, $tipoPessoa, $prioridadeCriterio, $data['type_profile']);
            }

            if (!$captura['get_captura']) {
                $this->processNonCaptura($captura, $perfisExpandidos);
                continue;
            }

            $this->processCaptura($captura, $resposta);
        }
        $tipoPessoa = $tipoPessoa ?? 3;

        $resultado = [
            'saidas' => $resposta,
            'tipo_pessoa' => $tipoPessoa,
        ];

        if (!empty($perfisExpandidos)) {
            $resultado['perfis_saida'] = $perfisExpandidos;
        }

        return $resultado;
    }

    private function setPersonType($captura, $personType, &$prioridadeCriterio, $profileType): mixed
    {
        $entradas = array_column($captura['get_captura']['get_campos_entrada'], 'campo');
        $prioridadePF = [1 => 'receitaFederalPf'];
        $prioridadePJ = [2 => 'receitaFederalPj'];

        if (!$prioridadeCriterio) {
            if (array_key_exists($captura['get_captura']['id'], $prioridadePF)) {
                $personType = 1;
                $prioridadeCriterio = true;
            } elseif (array_key_exists($captura['get_captura']['id'], $prioridadePJ)) {
                $personType = 2;
                $prioridadeCriterio = true;
            } elseif (in_array('nome_pf', $entradas) || in_array('nome_pj', $entradas) || in_array(
                'nomedatanasc_pf',
                $entradas,
                true
            )) {
                $personType = 3;
                $prioridadeCriterio = true;
            } elseif ($profileType == 'PF') {
                $personType = 1;
            } elseif ($profileType == 'PJ') {
                $personType = 2;
            }
        }

        return $personType;
    }

    private function processNonCaptura($captura, &$perfis_expand): void
    {
        if ($captura['get_perfil_pf']) {
            $perfis_expand[] = [
                'id' => $captura['get_perfil_pf']['id'],
                'nome' => $captura['get_perfil_pf']['nome'],
                'tipo' => 'pf',
                'saidas' => 1,
            ];
        }

        if ($captura['get_perfil_pj']) {
            $perfis_expand[] = [
                'id' => $captura['get_perfil_pj']['id'],
                'nome' => $captura['get_perfil_pj']['nome'],
                'tipo' => 'pj',
                'saidas' => 2,
                'campo' => $captura['campo'],
            ];
        }
    }

    private function processCaptura($captura, &$response): void
    {
        $response[] = [
            'id' => $captura['get_captura']['id'],
            'nome' => $captura['get_captura']['nome'],
            'captura' => $captura['get_captura']['metodo'],
            'tipo_pessoa' => $captura['get_captura']['tipo_pessoa'],
            'campo' => $captura['campo'],
        ];
    }

    public function inactivate(int $id, $mode = 'custom'): bool
    {
        $profile = $this->profileConsultationRepository->setMode($mode)->getById($id);

        if (!$profile) {
            throw new CustomException(trans('profile.not-found', ['id' => $id]));
        }

        return $this->profileConsultationRepository->inactivateById($id);
    }

    public function duplicate(array $params, $mode = 'custom'): array
    {
        $session = (object) session()->all();
        $profile = $this->profileConsultationRepository->setMode($mode)->getById($params['id_perfil']);

        // Checar por perfíl inativo
        if (!$profile) {
            $profile = $this->profileConsultationRepository
                ->setMode($mode)
                ->getById($params['id_perfil'], false, true);
        }

        if (!$profile) {
            throw new CustomException(trans('profile.not-found', ['id' => $params['id_perfil']]));
        }

        $structures = $profile->getConsulta;

        try {
            DB::beginTransaction();

            // Criar novo perfil duplicado
            $newProfile = $this->profileConsultationRepository->setMode($mode)->create([
                'nome' => $params['nome'],
                'versao' => 1,
                'ativo' => true,
                'id_usuario' => $session->setup['id_master'],
                'tipo_perfil' => $profile->tipo_perfil,
                'f_novo_upminer' => true
            ]);

            // Copiar toda a estrutura do perfil duplicado para o novo...
            foreach($structures as $structure) {
                $this->profileConsultationRepository->setMode($mode)->createEstrutura([
                    'id_consulta' => $newProfile->id,
                    'nivel' => $structure['nivel'],
                    'entrada' => $structure['entrada'],
                    'campo' => $structure['campo'],
                    'saida' => $structure['saida'],
                    'perfil_saida_pf' => $structure['perfil_saida_pf'],
                    'perfil_saida_pj' => $structure['perfil_saida_pj']
                ]);
            }

            if ($mode == 'custom') {
                foreach($params['grupos'] as $grupo) {
                    $grupoId = $grupo;
                    $grupo = $this->grupoRepository->getById($grupoId);

                    if (!$grupo) {
                        throw new CustomException(
                            trans('userGroup.group-not-found', ['id_grupo' => $grupoId])
                        );
                    }

                    $this->grupoRepository->createGrupoConsulta([
                        'id_grupo' => $grupoId,
                        'id_consulta' => $newProfile->id
                    ]);
                }
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();

            throw $e;
        }

        return [
            'id' => $newProfile->id,
            'nome' => $newProfile->nome,
            'grupos' => $params['grupos'] ?? [],
            'tipo_perfil' => $newProfile->tipo_perfil,
            'versao' => $newProfile->versao
        ];
    }

    public function exportExcel(int $perfilId, $mode = 'custom'): array
    {
        $params = [
            'basic' => false,
            'inativo' => false
        ];

        try {
            $profile = $this->getById($perfilId, $params, $mode);
        } catch (CustomException $e) {
            $params['inativo'] = true;
            $profile = $this->getById($perfilId, $params, $mode);
        }

        $groupsUsers = [];
        if ($mode == 'custom') {
            $groups = $this->userGroupService->getAll();

            foreach ($groups as $value) {
                $groupsUsers[$value['id']] = $value['nome'];
            }
        }

        return (new ProfileConsultationBundle())->downloadExcel($profile, $groupsUsers);
    }

    public function getSources(): array
    {
        return $this->profileConsultationRepository->getSources()->toArray();
    }

    public function getGroupUserProfile(): array
    {
        return $this->profileConsultationRepository->getGroupUserProfile()->toArray();
    }

    public function getSourcesHomonyms(int $perfilId): array
    {
        return $this->profileConsultationRepository->getSourcesHomonyms($perfilId)->toArray();
    }

    public function getSourcesByPerfilId(int $perfilId, $mode = 'custom'): array
    {
        $sources = [];
        $structures = $this->profileConsultationRepository
            ->setMode($mode)
            ->getSourcesByPerfilId($perfilId)
            ->toArray();

        if (!$structures) {
            return $sources;
        }

        foreach ($structures as $structure) {
            if (isset($structure['get_captura']['id'])) {
                $sources[] = [
                    'id' => $structure['get_captura']['id'],
                    'name' => $structure['get_captura']['nome']
                ];
            }
        }

        return $sources;
    }

    public function getPermissionChatGpt(int $perfilId, $mode = 'custom'): array
    {
        return $this->chatGptPermissionRepository->setMode($mode)->getAllByPerfilId($perfilId)
            ->map(function ($query) {
                return [
                    'id_perfil' => $query->id_perfil,
                    'id_action' => $query->id_action
                ];
            })
            ->toArray();
    }

    public function newPermissionChatGpt(int $perfilId, array $params, $mode = 'custom'): bool
    {
        $permissions = $params['permissions'] ?? [];
        $profile = $this->profileConsultationRepository->setMode($mode)->getById($perfilId);

        if (!$profile) {
            throw new CustomException(trans('profile.not-found', ['id' => $perfilId]));
        }

        $existingPermissions = $this->chatGptPermissionRepository->setMode($mode)->getAllByPerfilId($perfilId);

        if (!$existingPermissions->isEmpty()) {
            $existingPermissions->map(function ($permission) {
                $permission->delete();
            });
        }

        foreach ($permissions as $permission) {
            $this->chatGptPermissionRepository->create([
                'id_perfil' => $perfilId,
                'id_action' => $permission,
            ]);
        }

        return true;
    }

    public function create(array $params, $mode = 'custom'): array
    {
        try {
            DB::beginTransaction();

            $paramsProfile = [
                'nome' => $params['nome'],
                'tipo_perfil' => $params['tipo_perfil'],
                'versao'=> 1,
                'ativo' => true,
                'f_novo_upminer' => true,
                'data_criacao' => date('Y-m-d h:i:s'),
                'objetivo' => $params['objetivo'] ?? null,
                'icon_path' => $params['icon_path'] ?? null
            ];

            $profile = $this->profileConsultationRepository->setMode($mode)->create($paramsProfile);

            if (!empty($params['perfil'])) {
                $estrutura_arr = app(ProcessNewProfileAction::class)->execute(
                    $params,
                    $profile,
                    $mode
                );

                app(SetProfileStructureNivelAction::class)->execute($estrutura_arr);
            }

            if ($mode == 'custom') {
                app(CreateNewGroupsByProfileAction::class)->execute($params['grupos'], $profile);
            }

            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();

            throw $e;
        }

        $this->auroraLogService->saveLog(
            1,
            4,
            1,
            $profile->nome,
            [
                'var' => ['nome' => $profile->nome]
            ],
            [
                'id' => $profile->id,
                'request' => $params
            ]
        );

        return [
            'id' => $profile->id,
            'name' => $profile->nome,
            'groups' => $params['grupos'] ?? [],
            'date_created' => $profile->data_criacao,
            'objective' => $profile->objetivo,
            'icon' => $profile->icon,
            'type' => $profile->tipo_perfil,
            'version' => '1'
        ];
    }

    public function getParametersByType(string $type)
    {
        $type = $type === 'pf' ? 1 : 2;

        $filter_list = $this->getListParamsPf();

        if ($type == 2) {
            $filter_list = $this->getListParamsPj();
        }

        return app(GetSourcesParametersByTypeAction::class)->execute($filter_list);
    }

    public function getAllAvailableSourcesForProfile(array $params): array
    {
        $session = (object) session()->all();

        $filter_list = array_merge($this->getListParamsPf(), $this->getListParamsPj());

        if (!empty($params['type'])) {
            $type = $params['type'] == 'pf' ? 1 : 2;
            $filter_list = $type == 1 ? $this->getListParamsPf() : $this->getListParamsPj();
        }

        if (!empty($params['nome_campo'])) {
            $filter_list = [$params['nome_campo']];
        }

        $restritas = array_keys($session->contents['fontes_restritas']);

        $groups = app(GetAllAvailableSourcesForProfileAction::class)->execute($session, $restritas, $filter_list);

        return (new TransformAllAvailableSourcesForProfile())->handle($groups);
    }

    public function update(int $perfilId, array $params, $mode = 'custom'): array
    {
        $old_perfil = $this->profileConsultationRepository
            ->setMode($mode)
            ->getByTypeId($perfilId, $params['tipo_perfil']);

        if (!$old_perfil) {
            throw new CustomException(trans('profile.not-found', ['id' => $perfilId]));
        }

        $diffFields = ['diff' => [], 'add' => [], 'del' => []];

        $scoreApiClient = app(ScoreApiClient::class);
        $scoreId = $scoreApiClient->getDetailsScoreProfile($old_perfil->id)['id'] ?? null;

        try {
            DB::beginTransaction();
            $old_perfil->ativo = false;
            $old_perfil->save();

            $permissions = $this->chatGptPermissionRepository
                ->setMode($mode)
                ->getByPerfilId($old_perfil->id);

            $paramsProfile = [
                'nome' => $params['nome'] ?? $old_perfil->nome,
                'tipo_perfil' => $params['tipo_perfil'] ?? $old_perfil->tipo_perfil,
                'versao' => $old_perfil->versao + 1,
                'f_novo_upminer' => true,
                'ativo' => true,
                'objetivo' => $params['objetivo'] ?? null,
                'limpar_lote_flag' => $old_perfil->limpar_lote_flag,
                'icon_path' => $params['icon_path'] ?? null
            ];

            $new_perfil = $this->profileConsultationRepository->setMode($mode)->create($paramsProfile);

            if (!empty($permissions)) {
                foreach ($permissions as $permission){
                    $this->chatGptPermissionRepository->setMode($mode)->create([
                        'id_perfil' => $new_perfil->id,
                        'id_action' => $permission->id_action
                    ]);
                }
            }

            if ($old_perfil->nome != $new_perfil->nome) {
                $diffFields['diff'][] = array(
                    'field' => 'Nome',
                    'old' => $old_perfil->nome,
                    'new' => $new_perfil->nome
                );
            }

            if ($old_perfil->tipo_perfil != $new_perfil->tipo_perfil) {
                $diffFields['diff'][] = array(
                    'field' => 'Tipo Perfil',
                    'old' => $old_perfil->tipo_perfil == 1 ? 'CPF' : 'CNPJ',
                    'new' => $new_perfil->tipo_perfil == 1 ? 'CPF' : 'CNPJ'
                );
            }

            if (!empty($params['perfil'])) {
                $estrutura_arr = app(ProcessNewProfileAction::class)->execute(
                    $params,
                    $new_perfil,
                    $mode
                );

                app(SetProfileStructureNivelAction::class)->execute($estrutura_arr);

                if ($mode == 'custom') {
                    $grupos = app(CreateNewGroupsByProfileAction::class)->updateGroups(
                        $params['grupos'], $old_perfil, $new_perfil, $estrutura_arr
                    );
                }
            }

            app(InactivateAllMonitoringForProfileAction::class)->execute($old_perfil->id);

            DB::commit();
        } catch (Throwable $e) {
            DB::rollBack();

            throw $e;
        }

        $oldGrupos = $this->grupoRepository->getGrupoConsultaByPerfilId($old_perfil->id)->toArray();

        if ($mode == 'custom') {
            //VERIFICAÇÃO DE GRUPOS REMOVIDOS
            $newGrupos = array_flip($params['grupos']);
            foreach ($oldGrupos as $k => $v) {
                if (!isset($newGrupos[$k])) {
                    $diffFields['del'][] = array(
                        'label' => 'Grupo',
                        'value' => $v['nome'],
                        'relation' => 'grupo_consulta',
                        'data' => $v
                    );
                }
            }

            foreach($grupos as $grupo) {
                // LOG DE ADIÇÃO DE GRUPO
                if (!isset($oldGrupos[$grupo->id_grupo])) {
                    $diffFields['add'][] = array(
                        'label' => 'Grupo',
                        'value' => $grupo->nome,
                        'relation' => 'grupo_consulta',
                        'data' => $grupo
                    );
                }
            }
        }

        $this->auroraLogService->saveLog(
            1,
            4,
            2,
            "{$new_perfil->nome} v{$new_perfil->versao}",
            [
                'path' => 'log.perfil-editar',
                'var' => [
                    'nome' => "{$old_perfil->nome} v{$old_perfil->versao}",
                    'nome_novo' => "{$new_perfil->nome} v{$new_perfil->versao}"
                ]
            ],
            [
                'id' => $new_perfil->id,
                'id_old' => $old_perfil->id,
                'request' => $params,
                'changes' => $diffFields
            ]
        );

        // Depois de tudo feito sera necessario atualizar o upScore
        if ($scoreId) {
            $scoreApiClient->updateProfileIdWithScore($new_perfil->id, $scoreId);
        }

        return [
            'id' => $new_perfil->id,
            'name' => $new_perfil->nome,
            'groups' => $params['grupos'] ?? [],
            'type' => $new_perfil->tipo_perfil,
            'version' => $new_perfil->versao,
            'objective' => $new_perfil->objetivo,
            'icon' => $new_perfil->icon
        ];
    }

    public function delete(int $perfilId, string $obs, $mode = 'custom'): bool
    {
        $profile = $this->profileConsultationRepository->setMode($mode)->getById($perfilId, true);

        if (!$profile) {
            $profile = $this->profileConsultationRepository->setMode($mode)->getById($perfilId, true, true);

            if (!$profile) {
                throw new CustomException(trans('profile.not-found', ['id' => $perfilId]));
            }
        }

        $this->auroraLogService->saveLog(
            1,
            4,
            3,
            "{$profile->nome} v{$profile->versao}",
            [
                'path' => 'log.perfil-excluir',
                'var' => array('nome' => "{$profile->nome} v{$profile->versao}")
            ],
            [
                'id' => $profile->id,
                'obs' => $obs
            ]
        );

        return $this->profileConsultationRepository->setMode($mode)->setAsDeleted($perfilId, $obs);
    }

    public function getBatchsMonitoring(int $perfilId): array
    {
        $dossiers = $this->monitoriaDossieRepository->getBatchsMonitoring($perfilId);

        return $dossiers->groupBy('id_lote_consulta')->map(function($group) {
            return $group->pluck('id')->toArray();
        })->toArray();
    }

    public function getGooglePerfis(array $params, $paginated = true, $mode = 'custom'): array
    {
        $profiles = $this->profileConsultationRepository->setMode($mode)->getGooglePerfis($params, $paginated);

        if(empty($profiles['profiles'])) {
            return $profiles;
        }

        $countries = UFHelper::getGoogleArrayCountriesNames();
        $result = [];

        collect($profiles['profiles'])->each(function ($perfil, $key) use ($countries, &$result) {
            $paises = $perfil->getGoogleGlobal->transform(function ($item, $key) use ($countries) {
                $item->nome = $countries[$item->pais];
                return $item;
            })->toArray();

            $result[] = [
                "id" => $perfil->id,
                "name" => $perfil->nome,
                "countries" => $paises,
            ];
        });

        if (!empty($result)) {
            $profiles['profiles'] = $result;
        }

        return $profiles;
    }

    public function getWithGoogleGlobalById(int $profileId, $mode = 'custom'): null|Consulta|ConsultaPerfilDefault
    {
        return $this->profileConsultationRepository->setMode($mode)->getWithGoogleGlobalById($profileId);
    }

    /**
     *  Retorna a lista de campos permitidos para a entrada de uma fonte PF
     */
    public static function getListParamsPf()
    {
        return array('cpf', 'nome_pf', 'perfil_pf', 'perfil_pj', 'perfil_pfpj', 'nomedatanasc_pf');
    }

    /**
     *  Retorna a lista de campos permitidos para a entrada de uma fonte PJf
     */
    public static function getListParamsPj()
    {
        return array('cnpj', 'nome_pj', 'perfil_pf', 'perfil_pj', 'perfil_pfpj', 'cnpjuf_pj');
    }

    public static function getListParamsPfPj()
    {
        return array_merge(self::getListParamsPf(), self::getListParamsPj());
    }

    public function extraDossiersGenerated(array $params): array
    {
        $hasQsa = $this->profileConsultationRepository->setMode($params['mode'])->existInProfileSourcesQsa(
            $params['perfil']
        );
        $socios = 0;
        if ($hasQsa) {
            $documentoHelper = app(DocumentoHelper::class);
            $lambda = app(SpineSociosClient::class);
            $criterios =[];
            foreach ($params['criterios'] as $cpfCnpj) {
                if ($documentoHelper->validaDocumento($cpfCnpj)) {
                    $criterios[] = $documentoHelper->cleanDoc($cpfCnpj);
                }
            }

            $currentYear = (Carbon::now())->year;

            foreach ($criterios as $cpfCnpj) {
                try {
                    $response = $lambda->getSocios([
                        'cpf_cnpj' => $cpfCnpj,
                        'ano' => $currentYear
                    ]);

                    $result = count($response);
                } catch (Exception $e) {
                    $result = 0;
                }

                $socios += $result;
            }
        }
        return [
            "extra" => $socios
        ];
    }
}
