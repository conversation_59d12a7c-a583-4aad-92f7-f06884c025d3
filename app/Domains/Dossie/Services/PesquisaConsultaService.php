<?php

namespace App\Domains\Dossie\Services;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Repositories\LoteConsultaRepository;
use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;

use App\Domains\Dossie\Repositories\ProfileConsultationRepository;
use App\Domains\Dossie\Transforms\TranformHistorico;
use App\Domains\Dossie\Transforms\TransformCriterio;
use App\Domains\Dossie\Transforms\TransformCategorySource;
use App\Domains\Dossie\Transforms\TransformPesquisaSourceGroupByCategory;
use App\Domains\Tag\Repositories\TagsRepository;
use App\Domains\UpFlag\Repositories\UpFlagDossiesRepository;
use App\Domains\User\Entities\User;
use App\Domains\User\Entities\UserSetup;
use App\Domains\User\Repositories\UserRepository;
use Carbon\Carbon;
use Exception;
use App\Exceptions\CustomException;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PesquisaConsultaService
{
    private PesquisaConsultaRepository $pesquisaConsultaRepository;
    private TransformCriterio $transformCriterio;

    public function __construct(
        private readonly TransformPesquisaSourceGroupByCategory $transformPesquisaSourceGroupByCategory,
        private readonly TransformCategorySource $transformCategorySource,
        private readonly UserRepository $userRepository,
        private readonly LoteConsultaRepository $loteConsultaRepository,
        private readonly TagsRepository $tagsRepository,
        private readonly ProfileConsultationRepository $profileConsultationRepository,
        private readonly UpFlagDossiesRepository $upFlagDossiesRepository,
        PesquisaConsultaRepository $pesquisaConsultaRepository,
        TransformCriterio $transformCriterio
    ) {
        $this->pesquisaConsultaRepository = $pesquisaConsultaRepository;
        $this->transformCriterio = $transformCriterio;
    }

    public function getByID(int $id): PesquisaConsulta
    {
        return $this->pesquisaConsultaRepository->where('id', $id)->first();
    }

    public function getCriterioDossie(int $id): array
    {
        $dossie = $this->pesquisaConsultaRepository->getByID($id);
        return $this->transformCriterio->handle($dossie);
    }

    public function getByDossieIDandSourceID(int $dossieID, $sourceID): PesquisaConsulta
    {
        return $this->pesquisaConsultaRepository->getByDossieIDandSourceID($dossieID, $sourceID);
    }

    public function grupoFontes(int $dossieID)
    {
        $fontes = $this->pesquisaConsultaRepository->getStatusSource($dossieID);
        $fontes = $this->transformPesquisaSourceGroupByCategory->handle(
            [],
            $fontes,
            $dossieID
        );

        return $this->transformCategorySource->handle($fontes);
    }

    public function viewCapturaByDossie(array $viewCapturaByDossie)
    {
        return $this->pesquisaConsultaRepository->viewCapturaByDossie($viewCapturaByDossie);
    }

    /**
     * @param array $dataRequest
     * @return array
     */
    public function historicoDossie(array $dataRequest): array
    {
        $masterId = session('setup.id_master');
        $dossieId = $dataRequest['id_dossie'];
        $criterio = $dataRequest['criterio'];
        $criterioRegex = preg_replace('/[^A-Za-z0-9]/', '', $criterio);

        $pesquisaConsulta = $this->pesquisaConsultaRepository->historicoDossie($dossieId);
        $lote = $pesquisaConsulta->lote;

        if (!$lote) {
            throw new CustomException('Lote não existe');
        }

        $userSetup = $this->userRepository->setupUser($masterId);

        $dias_duplicidade = $userSetup->dias_aviso_duplicidade;

        if ($dias_duplicidade == null || $dias_duplicidade < 1) {
            $dias_duplicidade = 180;
        }

        $fromDate = Carbon::parse($lote->data_criacao)->subDays($dias_duplicidade);

        $historicos = $this->loteConsultaRepository->consultarHistoricos($lote, $fromDate, $criterio, $criterioRegex);

        return TranformHistorico::transformHistoricoCriterio($historicos, $criterio, $criterioRegex, $dossieId);
    }

    public function filters()
    {
        return [
            'tags' => $this->tagsRepository->index(),
            'perfis' => $this->profileConsultationRepository->getConsultasByGrupo([1, 2, 3]),
            'responsaveis' => $this->userRepository->getResponsaveisLote(),
        ];
    }

    /**
     * @return array{dias_duplicidade: mixed, total: int, lotes: array, upflags: mixed}
     */
    public function loteDuplicado(array $loteData): array
    {
        try {
            $this->userSetup = UserSetup::select('dias_aviso_duplicidade')
                ->fromMaster(session('setup.id_master'))
                ->first();
            $dossieDuplicate = $this->getDuplicidade($loteData['criterios']);
            $upFlags = $this->upFlagDossiesRepository->getFlagsByCriteria($loteData['criterios']);
            return [
                'dias_duplicidade' => $this->userSetup->dias_aviso_duplicidade,
                'total' => count($dossieDuplicate),
                'dossies' => $dossieDuplicate,
                'upflags' => $upFlags,
            ];
        } catch (Exception $exception) {
            throw new CustomException($exception->getMessage());
        }
    }

    public function getDuplicidade(array $criterios): array
    {
        $validateTimeInDays = $this->userSetup->dias_aviso_duplicidade ?? 180;
        $validateTimeInMinutes = $validateTimeInDays * 24 * 60;

        return $this->pesquisaConsultaRepository->getDuplicidadeConsulta($criterios, $validateTimeInMinutes);
    }
}
