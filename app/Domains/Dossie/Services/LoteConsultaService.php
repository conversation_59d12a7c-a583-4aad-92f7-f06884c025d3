<?php

namespace App\Domains\Dossie\Services;

use App\Domains\ConvertOldDossie\HasSourceResultOldDossieBySource;
use App\Domains\ConvertOldDossie\Helpers\OldResponseHelper;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Entities\Client\LoteConsulta;
use App\Domains\Dossie\Helpers\SearchNameHelper;
use App\Domains\Dossie\Repositories\LoteConsultaRepository;
use App\Domains\Monitoria\Repositories\MonitoriaDossieRepository;
use App\Domains\Monitoria\Services\MonitoriaService;
use App\Domains\Workflow\Repositories\WorkflowAnaliseDossieRepository;
use App\Domains\Dossie\Helpers\DossierLimitHelper;
use App\Domains\Dossie\Actions\DossierCreate\ProcessTypeProfileCheckAction;
use App\Domains\Dossie\Helpers\CheckCriteriaWithDossierTypeHelper;
use App\Domains\Dossie\Actions\DossierCreate\ProcessProfileSourcesAction;
use App\Domains\Dossie\Actions\DossierCreate\ProcessUpflagIfExistsAction;
use App\Domains\Dossie\Helpers\Traits\SourceParameter;
use App\Domains\Dossie\Repositories\ConsultaRepository;
use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;
use App\Domains\Dossie\Helpers\DocumentoHelper;
use App\Domains\Dossie\Repositories\LotesPorApiRepository;
use App\Domains\User\Repositories\UserRepository;
use App\Domains\AuroraLog\Services\AuroraLogService;
use App\Domains\Dossie\Actions\DossierCreate\ProcessArquivoPersonalizadoAction;
use App\Domains\Dossie\Actions\DossierCreate\ProcessSaveApiMetricAction;
use App\Exceptions\BatchNotProcessedException;
use App\Exceptions\BatchProcessingException;
use App\Exceptions\NoDossiersFound;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\CustomException;
use DB;
use App\Domains\Dossie\Services\FilaLoteService;
use Exception;

class LoteConsultaService
{
    use SourceParameter;

    private LoteConsultaRepository $loteConsultaRepository;
    private MonitoriaDossieRepository $monitoriaRepository;
    private WorkflowAnaliseDossieRepository $workflowAnaliseDossieRepository;
    public function __construct(
        LoteConsultaRepository $loteConsultaRepository,
        MonitoriaDossieRepository $monitoriaRepository,
        WorkflowAnaliseDossieRepository $workflowAnaliseDossieRepository,
        private readonly DossierLimitHelper $dossierLimitHelper,
        private readonly ConsultaRepository $consultaRepository,
        private readonly PesquisaConsultaRepository $pesquisaConsultaRepository,
        private readonly DocumentoHelper $documentoHelper,
        private readonly AuroraLogService $auroraLogService,
        private readonly CheckCriteriaWithDossierTypeHelper $checkCriteriaWithDossierTypeHelper,
        private readonly ProcessArquivoPersonalizadoAction $processArquivoPersonalizado,
        private readonly ProcessProfileSourcesAction $processProfileSources,
        private readonly ProcessSaveApiMetricAction $processSaveApiMetric,
        private readonly ProcessTypeProfileCheckAction $processTypeProfileCheck,
        private readonly ProcessUpflagIfExistsAction $processUpflagIfExists
    ) {
        $this->loteConsultaRepository = $loteConsultaRepository;
        $this->monitoriaDossieRepository = $monitoriaRepository;
        $this->workflowAnaliseDossieRepository = $workflowAnaliseDossieRepository;
    }

    public function getByID(int $id): LoteConsulta
    {
        return $this->loteConsultaRepository->where('id', $id)->first();
    }

    public function getOldResumoLote(int $idLote): array
    {
        $lote = $this->loteConsultaRepository->getResumoLote($idLote);
        if (!$lote) {
            throw new CustomException(trans('error.error-batch-not-found', [
                'id_lote' => $idLote
            ]));
        }

        $lote = $lote->toArray();

        if ($lote['estado'] == 1) {
            throw new BatchNotProcessedException(trans('error.error-batch-not-processed', [
                'id_lote' => $idLote
            ]));
        }

        if ($lote['estado'] == 2) {
            throw new BatchProcessingException(trans('error.error-batch-processing', [
                'id_lote' => $idLote
            ]));
        }

        // Nenhum dossiê encontrado para o lote
        if (count($lote['get_pesquisas']) <= 0) {
            throw new NoDossiersFound(trans('error.error-no-dossiers-found', [
                'id_lote' => $lote['id']
            ]));
        }

        $monitoria = $this->monitoriaDossieRepository->getByLoteId($lote['id']);

        if ($monitoria) {
            $monitoria = $monitoria->toArray();
        }

        $count = [];
        $fontes = [];

        foreach ($lote['get_pesquisas'] as $dossie) {
            $id_dossie = $dossie['id'];

            $count[$id_dossie] = array(
                'criterio' => trim(explode("|", $dossie['criterio'])[0]),
                'criterio_nome' => false,
                'data_processamento' => $dossie['data_estado'],
                'list' => []
            );

            //STATUS DOSSIE
            $status_workflow = $this->workflowAnaliseDossieRepository->getByID($id_dossie);

            if (isset($status_workflow->status_dossie_workflow)) {
                switch ($status_workflow->status_dossie_workflow) {
                    case 'aprovado':
                        $count[$id_dossie]['status_dossie_workflow'] = 'Aprovado';
                        $count[$id_dossie]['workflow_class'] = 'tag-label-green';
                        break;
                    case 'reprovado':
                        $count[$id_dossie]['status_dossie_workflow'] = 'Reprovado';
                        $count[$id_dossie]['workflow_class'] = 'tag-label-red';
                        break;
                    default:
                        $count[$id_dossie]['status_dossie_workflow'] = 'Em Análise';
                        $count[$id_dossie]['workflow_class'] = 'tag-label-yellow';
                        break;
                }
            } elseif (!empty($dossie['id_workflow'])) {
                $count[$id_dossie]['status_dossie_workflow'] = 'Em Análise';
                $count[$id_dossie]['workflow_class'] = 'tag-label-yellow';
            } else {
                $count[$id_dossie]['status_dossie_workflow'] = '';
            }

            $result = PesquisaConsulta::select(
                'id', 'captura', 'id_pesquisa_consulta', 'referencia', 'criterio', 'estado', 'dossie_versao'
            )->where(function ($q) use ($id_dossie) {
                $q->where('id', $id_dossie)->orWhere('id_pesquisa_consulta', $id_dossie);
            })
                ->with(['fonte' => function ($q) {
                    $q->select(
                        'id', 'nome', 'tipo_pessoa', 'metodo', 'tabela',
                        'detalhe_captura', 'captura_sem_referencia', 'data_migracao_lambda'
                    );
                }])
                ->orderBy('captura', 'ASC')
                ->get()
                ->toArray();

            foreach ($result as $fonte) {
                $captura = $fonte['fonte'];
                $metodo = $captura['metodo'];
                $fontes[$metodo] = $captura['nome'];
                // Checar versão do dossiê
                $versao = $fonte['dossie_versao'];
                try {
                    if (in_array($captura['id'], [1004, 1005, 166, 1007, 1006, 1008, 1009])) {
                        if (!isset($count[$id_dossie]['list'][$metodo])) {
                            $count[$id_dossie]['list'][$metodo] =
                                OldResponseHelper::countResultsByDossierVersion(
                                    $captura, $id_dossie, $versao,null
                                ) ?? array('itens' => array(), 'amount' => 0);
                        }

                        if (!isset($count[$id_dossie]['list'][$metodo]['itens'])) {
                            $count[$id_dossie]['list'][$metodo] = array();
                            $count[$id_dossie]['list'][$metodo]['itens'] = array();
                        }

                        if ($fonte['estado'] == 5) {
                            $count[$id_dossie]['list'][$metodo]['itens'][] = array(
                                'title' => explode('|', $fonte['criterio'])[1],
                                'amount' => 'Erro'
                            );
                        }

                    } elseif (in_array($captura['id'], [108, 109, 20, 147, 219])) {
                        if ($fonte['estado'] == 5) {
                            $count[$id_dossie]['list'][$metodo] = 'Erro';
                            continue;
                        }

                        $count[$id_dossie]['list'][$metodo] = OldResponseHelper::countResultsByDossierVersion(
                            $captura, $id_dossie, $versao, $fonte['referencia']
                        ) ?? 0;
                    } else {
                        if ($fonte['id_pesquisa_consulta'] === null) {;
                            if ($versao == PesquisaConsulta::VERSAO_OLD_DOSSIE) {
                                $count[$id_dossie]['criterio_nome'] =
                                    HasSourceResultOldDossieBySource::getCriterioNome(
                                        $captura, $id_dossie, $fonte['referencia']
                                    );
                            } else {
                                $count[$id_dossie]['criterio_nome'] = app(SearchNameHelper::class)->mount($id_dossie);
                            }
                        }

                        if ($fonte['estado'] == 5) {
                            $count[$id_dossie]['list'][$metodo] = 'Erro';
                            continue;
                        }

                        $id = $id_dossie;
                        if ($versao === PesquisaConsulta::VERSAO_OLD_DOSSIE) {
                            $id = $fonte['id'];
                        }

                        $count[$id_dossie]['list'][$metodo] = OldResponseHelper::countResultsByDossierVersion(
                            $captura, $id, $versao, $fonte['referencia']
                        ) ?? 0;

                    }
                } catch (\Exception $e) {
                    $count[$id_dossie]['list'][$metodo] = $e->getMessage();
                }
            }
        }

        // Percorrer registros encontrados e garantir que o retorno contenha todas as fontes do dossiê
        // (Principalmente para casos em que o lote não conseguiu prosseguir com o processamento de uma
        // fonte que necessita de uma outra como entrada)
        // Garantir também que a ordem das fontes de cada dossiê estejam na mesma ordem do array de fontes
        foreach ($count as $id_dossie => $this_count) {
            $dossie_list_registros = [];

            foreach ($fontes as $metodo => $fonte) {
                $registros = (isset($this_count['list'][$metodo])) ? $this_count['list'][$metodo] : 0;

                $dossie_list_registros[$metodo] = $registros;
            }

            $count[$id_dossie]['list'] = $dossie_list_registros;
        }

        return [
            'fontes' => $fontes,
            'registros' => $count,
            'monitoria' => $monitoria
        ];
    }
    public function create(array $data):array
    {
        // Verifica se fonte de entrada é PF ou PJ
        $typeProfile = $this->processTypeProfileCheck->execute($data['perfil'], $data['mode']);
        // validar se entradas confere com o tipo (e mapea criterios)
        $this->checkCriteriaWithDossierTypeHelper->checkCriteria(
            $data['criterios'],
            $typeProfile,
            $data['tipo'],
            $data['parametros']
        );

        // Verifica se ultrapassou o limite de dossies da conta e do usuário.
        $this->dossierLimitHelper->mount(count($data['criterios']['criteria']));

        $dossies_id = [];
        $profileCustom = $data['mode'] == 'custom' ? $data['perfil'] : null;
        $profileDefault = $data['mode'] == 'default' ? $data['perfil'] : null;

        // nessa versão 1 lote 1 dossie
        $process = $data['process'] ?? false;
        $fila = app(FilaLoteService::class);
        // Cria o Lote
        try {
            DB::beginTransaction();
            $profileSources = $this->processProfileSources->execute($data['perfil'], $data['mode']);

            foreach ($data['criterios']['criteria'] as $this_lote) {
                $lote = $this->loteConsultaRepository->create(
                    [
                        'id_usuario' => session('setup.id_usuario'),
                        'id_consulta' =>  $profileCustom ,
                        'data_estado' => date('Y-m-d H:i:s'),
                        'tipo' => $data['tipo'],
                        'data_criacao' => date('Y-m-d H:i:s'),
                        'arquivo' => pg_unescape_bytea(implode(PHP_EOL, [$this_lote])),
                        'id_centro_custo' => session('centro_custo.selected'),
                        'id_grupo' => session('setup.grupo_usuario'),
                        'tag_id' => $data['tag'] ?? null,
                        'id_consulta_perfil_default' => $profileDefault
                    ]
                );

                $this->processSaveApiMetric->execute($lote, $data['api'] ?? null);

                //verifica se algun criteio esta em upFlag
                $this->processUpflagIfExists->execute([$this_lote], $lote?->id);

                $this->setParameter($profileSources['sources'], $data['parametros'], $this_lote, $lote);

                $grupoConsulta = null;
                if (!$lote->id_consulta_perfil_default) {
                    $grupoConsulta = $this->consultaRepository->getDataWorkflow($lote->id_grupo, $lote->id_consulta);
                }

                $dossie = $this->pesquisaConsultaRepository->create(
                    [
                        'id_lote_consulta' => $lote?->id,
                        'captura' => $profileSources['dossieIDSource'],
                        'criterio' => $this->documentoHelper->validaDocumento($this_lote) ?
                        $this->documentoHelper->cleanDoc($this_lote) :
                        $this->documentoHelper->limpaString($this_lote),
                        'data_estado' => date('Y-m-d H:i:s'),
                        'data_criacao' => date('Y-m-d H:i:s'),
                        'data_faturamento' => date('Y-m-d H:i:s'),
                        'id_workflow' => $grupoConsulta->id_workflow ?? null,
                        'id_versao_workflow' => $grupoConsulta->id_versao_workflow ?? null,
                    ]
                );

                $dossie->criteria = $this_lote;
                $dossie->lote = $lote;
                $dossies_id[] = $dossie;

                $this->auroraLogService->saveLog(
                    2,
                    6,
                    1,
                    $lote->id,
                    array(
                        'path' => 'log.lote-criar',
                        'var' => array('api' => session('api', false)? '(API)' : '', 'lote' => $dossie?->id_lote_consulta)
                    ),
                    array(
                        'request' => request()->all()
                    )
                );
            }
            
            DB::commit();
            
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
        //arquivo
        if (!empty($data['file'])) {
            $this->processArquivoPersonalizado->execute(
                $data['file'],
                $dossies_id,
                $data['criterios'],
                $process
            );
        }

        if (empty($data['file']) && $process) {
            $fila->addDossieFila(
                $dossies_id,
                session('company')
            );
        }

        return ['qtd' => count($dossies_id), 'dossies' => $dossies_id];
    }
}
