<?php

namespace App\Domains\Dossie\Services;

use App\Actions\Clients\Upminer\SessionClient;
use App\Domains\ConvertOldDossie\ConvertCommentsOldDossie;
use App\Domains\ConvertOldDossie\Utils\Funcoes;
use App\Domains\DicionarioSource\Repositories\Seguranca\CaptureRepository;
use App\Domains\Dossie\Actions\Dossie\Score\GetDossiersScoreAction;
use App\Domains\Dossie\Actions\DossierCreate\ProcessHomonymsAction;
use App\Domains\Dossie\Bundles\RemoveLimitadorResultadoCapturaBundle;
use App\Domains\Dossie\Bundles\SaveLimitadorResultadoCapturaBundle;
use App\Domains\Dossie\Entities\Seguranca\Captura;
use App\Domains\Dossie\Helpers\ErroConsulta;
use App\Domains\Dossie\Helpers\ErrorDossier;
use App\Domains\Dossie\Helpers\ErrorDossierTreatment;
use App\Domains\Dossie\Helpers\Functions;
use App\Domains\Dossie\Repositories\CapturaRepository;
use App\Domains\Dossie\Repositories\LimitCapturaRepository;
use App\Domains\Dossie\Repositories\LoteConsultaRepository;
use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;
use App\Domains\Dossie\Transforms\TransformCapturaFilterList;
use App\Domains\Dossie\Transforms\TransformCardInfo;
use App\Domains\Dossie\Transforms\TransformDataLimitCaptura;
use App\Domains\User\Entities\UserSetup;
use App\Domains\User\Repositories\GroupRepository;
use App\Domains\User\Repositories\UserRepository;
use App\Domains\User\Services\UserService;
use Exception;
use App\Exceptions\CustomException;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

class DossieService
{
    public const STATUS_PROCESSED = '4';
    public const STATUS_FAILURE = '5';
    private LoteConsultaRepository $loteConsultaRepository;
    private TransformCardInfo $transformCardInfo;
    private UserService $userService;
    private PesquisaConsultaRepository $pesquisaConsultaRepository;
    private CaptureRepository $captureRepository;
    private FilaLoteService $filaLoteService;


    public function __construct(
        CaptureRepository $captureRepository,
        LoteConsultaRepository $loteConsultaRepository,
        TransformCardInfo $transformCardInfo,
        PesquisaConsultaRepository $pesquisaConsultaRepository,
        UserService $userService,
        FilaLoteService $filaLoteService,
        private readonly UserRepository $userRepository,
        private readonly GroupRepository $groupRepository,
        private readonly SaveLimitadorResultadoCapturaBundle $saveLimitadorResultadoCapturaBundle,
        private readonly RemoveLimitadorResultadoCapturaBundle $removeLimitadorResultadoCapturaBundle,
        private readonly CapturaRepository $capturaRepository,
        private readonly LimitCapturaRepository $limitCapturaRepository,
    ) {
        $this->loteConsultaRepository = $loteConsultaRepository;
        $this->transformCardInfo = $transformCardInfo;
        $this->userService = $userService;
        $this->pesquisaConsultaRepository = $pesquisaConsultaRepository;
        $this->captureRepository = $captureRepository;
        $this->filaLoteService = $filaLoteService;
    }

    public function getListDossie(array $filters = []): array
    {
        $session = session()->all();
        $notsave_perpage = !empty($filters['notsave_per_page']) ? $filters['notsave_perpage'] : false;

        // Atualiza o per_page da sessão
        if (isset($filters['per_page']) && !$notsave_perpage) {
            if ($filters['per_page'] != $session['setup']['itens_pagina']) {
                // Salvar setup
                $usuario_setup = UserSetup::select()->where('id_usuario', $session['setup']['id_usuario'])->first();

                if ($usuario_setup !== null) {
                    $usuario_setup->qtd_itens_pagina = $filters['per_page'];
                    $usuario_setup->save();
                }

                (new SessionClient($session['key']))->updatePerPage($filters['per_page']);
                session(['setup.itens_pagina' => $filters['per_page']]);
            }
        }

        $dossiers = $this->pesquisaConsultaRepository->getPaginatedByFilter($session, $filters);
        $dossierIds = collect($dossiers['dossies'])->pluck('id')->toArray();
        $dossierDerivationsIds = collect($dossiers['dossies'])->pluck('derivacao.id')->toArray();
        $dossierIds = array_filter(array_merge($dossierIds, $dossierDerivationsIds));

        if (!empty($dossiers['dossies'])) {
            // Ver se tem score
            $scoreIds = (new GetDossiersScoreAction())->execute($dossierIds);

            foreach ($dossiers['dossies'] as &$dossier) {
                $dossier['score'] = $scoreIds[$dossier['id']] ?? null;

                if (!empty($dossier['derivacao'])) {
                    foreach ($dossier['derivacao'] as $derivatedDossier) {
                        $derivatedDossier['score'] = $scoreIds[$derivatedDossier['id']] ?? null;
                    }
                }
            }
        }

        return $dossiers;
    }

    public function getCardInfo(int $dossieID): array
    {
        $dossie = $this->pesquisaConsultaRepository->getDossieWithDerivados($dossieID);
        $lote = $this->loteConsultaRepository->getLoteUsuario($dossie->id_lote_consulta);
        $fontesComResultado = $this->captureRepository->statusWithResultDossieName($dossieID);
        $fontesSemResultado = $this->getNameSourceWithoutResult(
            $dossieID
        );
        $fontesIndisponiveis = $this->getNameSourceWithError(
            $dossieID
        );
        $masterLogo = $this->userService->getLogoConta();
        // não será adicionado campo visible pois esse card não pode ser ocultado
        return $this->transformCardInfo->cardInfoWithSources(
            $dossie,
            $lote,
            $fontesComResultado,
            $fontesSemResultado,
            $fontesIndisponiveis,
            $masterLogo
        );
    }

    public function getInfo(int $dossieID): array
    {
        $dossie = $this->pesquisaConsultaRepository->getDossieWithDerivados($dossieID);
        $lote = $this->loteConsultaRepository->getLoteUsuario($dossie->id_lote_consulta);

        $masterLogo = $this->userService->getLogoConta();
        $data = $this->transformCardInfo->cardInfo(
            $dossie,
            $lote,
            $masterLogo
        );

        if ($dossie->dossie_convertido) {
            app(ConvertCommentsOldDossie::class)->convert($dossieID);
        }

        return $data;
    }

    public function getCardSourceWithoutResult(int $id): array
    {
        $fontesSemResultado = $this->getNameSourceWithoutResult(
            $id
        );
        return [
            "amount" => count($fontesSemResultado),
            "fontes" => $fontesSemResultado
        ];
    }

    public function getCardSourceWithResult(int $id): array
    {
        $data = $this->captureRepository->statusWithResultDossieName($id);
        return [
            "amount" => count($data),
            "fontes" => $data
        ];
    }

    public function getNameSourceWithoutResult(int $id): array
    {
        return $this->captureRepository->statusDossieName(
            $id,
            self::STATUS_PROCESSED
        );
    }

    public function getNameSourceWithError(int $id): array
    {
        return $this->captureRepository->statusDossieName(
            $id,
            self::STATUS_FAILURE
        );
    }

    public function dossiLoteNavigate(int $dossieId)
    {
        $dossies = $this->pesquisaConsultaRepository->dossieLoteNavigate($dossieId);

        $posicao = array_search($dossieId, $dossies);

        $back = null;
        $next = null;

        if ($posicao !== false) {
            $back = ($posicao > 0) ? $dossies[$posicao - 1] : null;
            $next = ($posicao < count($dossies) - 1) ? $dossies[$posicao + 1] : null;
        }

        return [
            'dossie_id' => $dossieId,
            'back' => $back,
            'next' => $next,
        ];
    }

    public function viewCapturaByDossie(array $viewCapturaByDossie)
    {
        $dossie = $this->pesquisaConsultaRepository->viewCapturaByDossie($viewCapturaByDossie);
        return json_decode($dossie?->result?->result);
    }

    public function reprocessDossies(array $params): array
    {
        $session = (object)session()->all();
        $dossiesIds = $params['dossies'];
        $cliente = $session->company;

        if ($session->admin && !empty($params['company'])) {
            $cliente = $params['company'];
        }

        $dossies = [];
        foreach ($dossiesIds as $dossieId) {
            $dossie = $this->pesquisaConsultaRepository->getByID($dossieId);

            if (!empty($dossie->id_pesquisa_consulta)) {
                throw new CustomException(
                    "Não é possível efetuar o reprocessamento a partir de um dossiê filho.
                    Dossie principal: {$dossie->id_pesquisa_consulta}"
                );
            }

            $dossies[] = $dossie;
        }

        return $this->filaLoteService->addDossieFila($dossies, $cliente);
    }

    public function storeLimitDossie(array $data)
    {
        try {
            DB::beginTransaction();
            $user = $this->userRepository->getUserById($data['user_id']);
            $company = session('company');
            $admin = session('admin');

            $userCompany = explode('_', $user->login)[0];
            
            if ($userCompany !== $company && !$admin) {
                throw new CustomException("Você não pode alterar dados desse usuário.", Response::HTTP_FORBIDDEN);
            }

            $this->userRepository->setLimitDossie($user, $data);
            DB::commit();
            return $user;
        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function getLimitDossie(array $data)
    {
        return $this->userRepository->getUserByCompany($data);
    }

    public function storeLimitResultSource(array $data): bool|array
    {
        try {
            $captura = $this->capturaRepository->getCapturaByIdName($data['fonte']);
            $grupo = $this->groupRepository->getGrupo($data['grupo']);

            if ($data['qtd'] === 0) {
                return $this->removeLimitadorResultadoCapturaBundle->execute($grupo, $captura);
            }
            return $this->saveLimitadorResultadoCapturaBundle->execute($grupo, $captura, $data['qtd']);
        } catch (Exception $exception) {
            throw $exception;
        }
    }

    public function getLimitResultSource()
    {
        $filterListPerfil = Functions::getListParamsPfPj();
        $capturaFilterList = TransformCapturaFilterList::handle(
            $this->capturaRepository->filterCaptura(session('setup.id_master'), $filterListPerfil)
        );
        foreach ($capturaFilterList as $key => $captura) {
            $capturaFilterList[$key]['alias'] = $key;
            $capturaFilterList[$key]['hasLimits'] = false;
        }

        $limiteCapturas = TransformDataLimitCaptura::handle(
            $this->limitCapturaRepository->getLimitadorCapturas()
        );
        $limitadorCapturas = ['grupos' => []];
        if (!empty($limiteCapturas['grupos'])) {
            foreach ($limiteCapturas['grupos'] as $grupo => $grupos) {
                foreach ($grupos['capturas'] as $captura) {
                    $limitadorCapturas['grupos'][$grupo][$captura['id']] = [
                        'id' => $captura['id'],
                        'captura' => $captura['captura'],
                        'quantidade' => $captura['qtd'],
                    ];

                    $capturaFilterList[$captura['id']]['hasLimits'] = true;
                }
            }
        }
        $capturaFilterList = array_values($capturaFilterList);

        $userGroup = $this->groupRepository->userGroupListMaster();
        $groupList = [];
        foreach ($userGroup as $group) {
            $groupList[$group['alias']] = [
                'id' => $group['id'],
                'nome' => $group['nome'],
                'alias' => $group['alias'],
            ];
        }
        return [
            'limitador_capturas' => $limitadorCapturas,
            'capturas' => $capturaFilterList,
            'grupos' => $groupList,
        ];
    }


    public function searchHomonyms(array $data)
    {
        $processHomonymsAction = new ProcessHomonymsAction();
        return $processHomonymsAction->execute($data);
    }

    public function checkErrors(array $data): array
    {
        $errors = [];
        $dossiesIds = $data['dossies'];

        foreach ($dossiesIds as $dossieId) {
            $dossies = $this->pesquisaConsultaRepository->getWithErrorById($dossieId);

            if ($dossies->count() > 0) {
                foreach ($dossies as $dossie) {
                    $source = Captura::getMetodoById($dossie->captura);

                    if (!$source) {
                        continue;
                    }

                    $sourceName = $source->metodo;

                    if (!empty($sourceName) && gettype($sourceName) === "string") {

                        $errorCheck = ErrorDossier::$sourceName($dossie);

                        if ($errorCheck !== true) {
                            if (!isset($errors[$sourceName])) {
                                $errors[$sourceName] = [];
                            }

                            $result['dossierId'] = $dossie->id_pesquisa_consulta ?? $dossie->id;
                            $errors[$sourceName][] = array_merge($result, $errorCheck);
                        }
                    }
                }
            }
        }

        return $errors;
    }

    public function fixErrors(array $data): bool
    {
        $method = Funcoes::CamelCase($data['error']);
        ErrorDossierTreatment::$method($data['data'], $data['dossies']);

        return true;
    }
}
