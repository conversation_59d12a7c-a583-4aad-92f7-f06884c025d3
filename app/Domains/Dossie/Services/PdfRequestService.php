<?php

namespace App\Domains\Dossie\Services;

use App\Domains\Dossie\Repositories\PdfRequestRepository;
use App\Domains\Dossie\Transforms\TransformResponsePdfRequest;

class PdfRequestService
{
    public function __construct(
        private readonly PdfRequestRepository        $pdfRequestRepository,
        private readonly TransformResponsePdfRequest $transformResponsePdfRequest
    )
    {
    }

    public function findById(array $data): array
    {
        $dossieId = $data['dossie_id'];
        $requestId = $data['request_id'] ?? '';

        $data = $this->pdfRequestRepository->findById($dossieId,$requestId);
        return $this->transformResponsePdfRequest->handle($data);
    }

    public function getSessionUserPdf(string $token)
    {
        $data = $this->pdfRequestRepository->getSessionUserPdf($token);

        return json_decode($data, true)['session'] ?? [];
    }

}
