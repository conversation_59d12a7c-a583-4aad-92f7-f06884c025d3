<?php

declare(strict_types=1);

namespace App\Domains\Dossie\Entities\Client;

use App\Domains\Workflow\Entities\Client\Workflow;
use App\Domains\Workflow\Entities\Client\WorkflowAnaliseDossie;
use App\Domains\Workflow\Entities\Client\WorkflowAprovadores;
use App\Domains\Workflow\Entities\Client\WorkflowPesquisaConsulta;
use App\Domains\Workflow\Helpers\CheckIsInAnalisysByErrorHelper;
use DossieSourcesModule\Sources\Entities\Cliente\DossieCommentsOrigem;
use DossieSourcesModule\Sources\Entities\Cliente\DossieSourceReactions;
use DossieSourcesModule\Sources\Entities\Cliente\DossieTagsSource;
use DossieSourcesModule\Sources\Entities\Cliente\PesquisaConsulta as Dossie;
use DossieSourcesModule\Sources\Entities\Cliente\ResultServices;
use DossieSourcesModule\Sources\Entities\Seguranca\Capture;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class PesquisaConsulta extends Dossie
{
    const COMPANIES_WITH_PURGE = [
        /*
        exemplos:
            'company name' => ['hours' => x],
            'company name' => ['minutes' => y],
            'company name' => ['hours' => x, 'minutes' => y],
        */
        "bnymellonservios2aba3" => ['hours' => 4],
        "bny" => ['hours' => 4],
        'qatesting3202f' => ['hours' => 4], // Teste para qa
        'upsearch769dc' => ['hours' => 4], // Teste para prod
    ];

    public const FAILED = '5';
    public const VERSAO_OLD_DOSSIE = 1;
    public const VERSAO_NEW_DOSSIE = 2;

    protected $casts = [
        'data_criacao' => 'datetime',
        'data_estado' => 'datetime'
    ];
    protected $attributes = [
        'id_pesquisa_consulta' => null,
        'estado' => '1',
        'dossie' => '1',
        'status' => 'created',
        'dossie_versao' => 2,
        'id_monitoria' => null,
    ];
    protected $fillable = [
        'id_monitoria',
        'id_lote_consulta',
        'captura',
        'criterio',
        'data_estado',
        'data_criacao',
        'data_faturamento',
        'id_workflow',
        'id_versao_workflow',

    ];

    public $timestamps = false;

    public function brother()
    {
        return $this->hasMany(get_class(), 'id_lote_consulta', 'id_lote_consulta')->whereNull('id_pesquisa_consulta');
    }


    public function lote(): BelongsTo
    {
        return $this->belongsTo(LoteConsulta::class, 'id_lote_consulta', 'id');
    }

    public function derivados()
    {
        return $this->hasMany(LoteConsulta::class, 'sub_of', 'id');
    }

    public function workflowAnalise(): HasOne
    {
        return $this->hasOne(WorkflowAnaliseDossie::class, 'id_pesquisa_consulta', 'id');
    }

    public function workflowPesquisaDossie(): HasMany
    {
        return $this->hasMany(WorkflowPesquisaConsulta::class, 'id_pesquisa_consulta', 'id');
    }

    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class, 'id_workflow', 'id');
    }

    public function aprovadores(): BelongsTo
    {
        return $this->belongsTo(WorkflowAprovadores::class, 'id_workflow', 'id_workflow');
    }

    public function dossiePdf(): HasMany
    {
        return $this->hasMany(PdfRequestModel::class, 'data->id_dossie', 'id');
    }

    public function scopeAprovacaoAutomatica(Builder $q)
    {
        return $q->has('workflow.aprovacaoAutomatica');
    }

    public function scopeFailed(Builder $q)
    {
        return $q->where('estado', self::FAILED);
    }

    public function scopeOfParent(Builder $query, string $id): void
    {
        $query->where('id_pesquisa_consulta', $id);
    }

    public function scopeApprove(Builder $query, string $id): void
    {
        $query->ofParent($id)->aprovacaoAutomatica()->failed();
    }

    /**
     * Filtra a query para obter um dossiê pelo id
     */
    public function scopeOfDossie(Builder $q, int $id_dossie): Builder
    {
        return $q->where('id', $id_dossie)->orWhere('id_pesquisa_consulta', $id_dossie);
    }

    /**
     * Filtra uma query para obter dossiês que tenham workflow com aprovação automática
     */
    public function scopeHasAutomaticApprovals(Builder $q): Builder
    {
        return $q->has('workflow.aprovacaoAutomatica');
    }

    public function scopeLastHoursOrMinutes($query, int $hours = 0, int $minutes = 0)
    {
        return $query->where(
            'data_criacao',
            '>=',
            date('Y-m-d H:i:s', strtotime('-' . $hours . ' hour -' . $minutes . ' minutes'))
        );
    }

    public function comments(): BelongsTo
    {
        return $this->belongsTo(DossieCommentsOrigem::class, 'captura', 'captura_id')
            ->whereNull('workflow_id')
            ->whereNull('version_workflow_id')
            ->whereNull('dossie_id');
    }
    public function fonte(): BelongsTo
    {
        return $this->belongsTo(Capture::class, 'captura', 'id')
            ->select(
                'id',
                'nome',
                'tipo_pessoa',
                'id_grupo_captura_pj',
                'id_grupo_captura_pf'
            );
    }
    public function result()
    {
        return $this->belongsTo(ResultServices::class, 'captura', 'source_id');
    }
    public function tag(): BelongsTo
    {
        return $this->belongsTo(DossieTagsSource::class, 'captura', 'source_id');
    }

    public function reaction(): BelongsTo
    {
        return $this->belongsTo(DossieSourceReactions::class, 'captura', 'source_id');
    }

    public function captura(): BelongsTo
    {
        return $this->belongsTo(Capture::class, 'captura', 'id');
    }

    public function getReferenciasDaCaptura($capturaID, $dossieID) {
        return $this->select('referencia')
            ->where([
                ['id', '=', $dossieID],
                ['captura', '=', $capturaID]
            ])
            ->orWhere([
                ['id_pesquisa_consulta', '=', $dossieID],
                ['captura', '=', $capturaID]
            ])
            ->whereNotNull('referencia')
            ->pluck('referencia')->toArray();
    }

    public function getDossieSourceCriterioParams($sourceID, $dossieID) {
        return $this->select('criterio')
            ->where([
                ['id', '=', $dossieID],
                ['captura', '=', $sourceID]
            ])
            ->orWhere([
                ['id_pesquisa_consulta', '=', $dossieID],
                ['captura', '=', $sourceID]
            ])->first();
    }

    public function getChildrens(): HasMany
    {
        return $this->hasMany(get_class(), 'id_pesquisa_consulta');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(get_class(), 'id_pesquisa_consulta');
    }

    public function getWorkflowStatus(): string|null
    {
        if (!$this->workflow) {
            return Workflow::NO_WORKFLOW;
        }

        $this->load([
            'workflow',
            'workflowAnalise',
            'workflowPesquisaDossie'
        ]);

        // Casos em que o workflow não tem status
        if ($this->workflowAnalise) {

            $inAnalysisByError = false;
            if ($this->workflowAnalise->aprovado_automatico) {
                $inAnalysisByError = (new CheckIsInAnalisysByErrorHelper())->check($this->id);
            }

            $this->workflowAnalise->updateEmptyStatus($inAnalysisByError);

            if (!$inAnalysisByError) {
                $statusWorkflow =  $this->workflowAnalise->status_dossie_workflow ?? Workflow::IN_ANALYSIS;
                $workflowPesquisaDossie = $this->workflowPesquisaDossie->first() ?? $this->workflowPesquisaDossie;

                if (!$this->workflowAnalise->aprovado_automatico) {
                    return !empty($workflowPesquisaDossie->status)
                        ? $workflowPesquisaDossie->status
                        : $statusWorkflow;
                }

                return $statusWorkflow;
            }
        }

        if (!empty($this->id_workflow)) {
            return Workflow::IN_ANALYSIS;
        }
    }
}
