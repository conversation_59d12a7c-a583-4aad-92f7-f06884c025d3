<?php

namespace App\Domains\Dossie\Entities\Client;

use App\Domains\User\Entities\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\Dossie\Entities\Client\Pdfs;

class PdfRequestModel extends Model
{
    public const QUEUE = 'queue';
    public const PROCESSING = 'processing';
    public const SUCCESS = 'success';
    public const ERROR = 'error';

    protected $table = 'pdf_requests';

    protected $fillable = [
        "app_id",
        "user_id",
        "token",
        "company",
        "status",
        "data",
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function dossie(): BelongsTo
    {
        return $this->belongsTo(PesquisaConsulta::class, 'data->id_dossie');
    }

    public function pdf(): BelongsTo
    {
        return $this->belongsTo(Pdfs::class, 'id', 'pdf_request_id');
    }
}
