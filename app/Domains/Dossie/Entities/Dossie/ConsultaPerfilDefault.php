<?php

namespace App\Domains\Dossie\Entities\Dossie;

use App\Domains\Dossie\Entities\Client\GrupoConsulta;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use App\Domains\Dossie\Entities\Dossie\ChatGptPermissionPerfilDefault;
use Illuminate\Support\Facades\Storage;

class ConsultaPerfilDefault extends ModelBaseConsulta
{
    protected $table = 'dossie.consulta_perfil_default';
    protected $fillable = [
        'id',
        "nome",
        "versao",
        "ativo",
        "excluido",
        "data_exclusao",
        "id_usuario",
        "tipo_perfil",
        "data_criacao",
        "obs_exclusao",
        "limpar_lote_flag",
        "objetivo",
        "icon_path"
    ];

    protected $appends = ['icon'];
    public $timestamps = false;

    public function getIConAttribute()
    {
        if (empty($this->icon_path)) {
            return null;
        }
    
        // Se for uma URL completa
        if (filter_var($this->icon_path, FILTER_VALIDATE_URL)) {
            return $this->icon_path;
        }
    
        // Se for um caminho relativo
        return Storage::disk('s3-static')->exists($this->icon_path) ?
            Storage::disk('s3-static')->url($this->icon_path) :
            $this->icon_path;
    }

    public function getConsulta(): HasMany
    {
        return $this->hasMany(EstruturaConsultaPerfilDefault::class, 'id_consulta', 'id');
    }

    public function scopeSelectConsultasByGrupo(Builder $query)
    {
        return parent::scopeSelectConsultasByGrupo($query)
         ->addSelect('icon_path');
    }

    public function permissionChat()
    {
        return $this->hasMany(ChatGptPermissionPerfilDefault::class, 'id_perfil', 'id');
    }

    public function getGrupoConsulta()
    {
        return $this->hasMany(GrupoConsulta::class, 'id_consulta', 'id');
    }

    public function getGoogleGlobal(): HasMany
    {
        return $this->hasMany(GoogleGlobalPerfilDefault::class, 'id_consulta', 'id');
    }
}
