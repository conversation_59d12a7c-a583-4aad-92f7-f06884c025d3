<?php

namespace App\Domains\Dossie\Actions\Dossie\List;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Helpers\DocumentoHelper;
use App\Domains\Dossie\Transforms\NewStatusToOldTransform;
use App\Domains\Workflow\Entities\Client\Workflow;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class FilterListDossieAction
{
    public function execute(Builder $dossies, object $filters): Builder
    {
        // Filtra Data de Inicio
        $dossies->when(!empty($filters->data_inicio), function($query) use ($filters) {
            $startDate = Carbon::createFromFormat('Y-m-d', $filters->data_inicio)->startOfDay();
            $query->where(
                'data_criacao',
                '>=',
                $startDate
            );
        });

        // Filtra Data de Fim
        $dossies->when(!empty($filters->data_fim), function($query) use ($filters) {
            $endDate = Carbon::createFromFormat('Y-m-d', $filters->data_fim)->endOfDay();
            $query->where(
                'data_criacao',
                '<=',
                $endDate
            );
        });

        // Filtra por dossie
        $dossies->when(!empty($filters->id_dossie), $this->filterDossie($filters));

        // Filtra por Criterio
        $dossies->when(!empty($filters->criterio), function($query) use ($filters) {
            $this->filterCriterion($query, $filters);
        });

        // Filtra por Status
        $dossies->when(!empty($filters->status), function ($query) use ($filters) {
            return $this->filterStatus($query, $filters);
        });

        // Filtra por perfil
        $dossies->when(!empty($filters->id_perfil), function($query) use ($filters) {
            $this->filterProfile($query, $filters);
        });

        // Filtra por perfil default
        $dossies->when(!empty($filters->id_perfil_default), function($query) use ($filters) {
            $this->filterProfileDefault($query, $filters);
        });

        // Filtra por tag
        $dossies->when(!empty($filters->id_tag), function($query) use ($filters) {
            $this->filterTag($query, $filters);
        });

        // Caso seja passado o status de workflow, filtrar pelo workflow
        $dossies->when(!empty($filters->status_workflow), function($query) use ($filters) {
            $this->filterWorkflow($query, $filters);
        });

        // Filtra somente dossies que tem monitoria
        $dossies->when(!empty($filters->monitoria) && $filters->monitoria, function($query) {
            $query->whereHas('lote.getMonitoria', fn($query) => $query->where('status', 1)->where('ativo', 1));
        });

        // Filtra somente dossies que tem derivação
        $dossies->when(!empty($filters->derivacao) && $filters->derivacao, function($query) {
            $query->whereHas('derivados');
        });

        $dossies = $this->getDossiersWithLimitedHoursDays($dossies);

        return $dossies;
    }

    private function filterStatus($query, object $filters): mixed
    {
        $oldStatusMap = (new NewStatusToOldTransform())->handle($filters->status);
        $oldStatus = $oldStatusMap[0] ?? null;

        // Tratamento para os novos status: created e queue
        if (empty($oldStatusMap)) {
            return $query->where(function($query) use ($filters) {
                $query->whereIn('status', $filters->status);
            });
        }

        return $query->where(function ($query) use ($filters, $oldStatus) {
            $query->when($oldStatus == 4, function($query) use ($oldStatus) {
                $query->whereDoesntHave('getChildrens', function ($query) use ($oldStatus) {
                    $query->whereIn('estado', [1, 5]);
                })->where('estado', $oldStatus);
            });

            $query->when($oldStatus != 4, function($query) use ($oldStatus) {
                if ($oldStatus == 1) {
                    $query->whereHas('getChildrens', function ($query) use ($oldStatus) {
                        $query->where('estado', $oldStatus);
                    })->orWhere('estado', $oldStatus)->where(function($query) {
                        $query->where('status', '<>', 'queue')->where('status', '<>', 'created');
                    });
                } else {
                    $query->where('estado', $oldStatus)
                        ->orWhere(function ($q) use ($oldStatus) {
                            $q->whereHas('getChildrens', function ($q) use ($oldStatus) {
                                $q->where('estado', $oldStatus);
                            });
                        });
                }
            });
        });
    }

    private function filterDossie(object $filters): \Closure
    {
        return function ($query) use ($filters) {
            $filters->id_dossie = preg_replace('/\D+/isu', ',', $filters->id_dossie);
            $query->whereIn('id', explode(',', $filters->id_dossie));
        };
    }

    private function filterCriterion(Builder $query, object $filters): void
    {
        $documentoHelper = new DocumentoHelper();
        $clean = $documentoHelper->cleanDocument($filters->criterio);
        $masked = $documentoHelper->formatDocument($clean);

        $query->where(function($query) use ($clean, $masked) {
            $query->where(function ($query) use ($clean, $masked) {
                $query->where(function ($query) use ($clean, $masked) {
                    $query->where('criterio', 'ilike', '%' . $clean . '%');
                    $query->orWhere('criterio', 'ilike', '%' . $masked . '%');
                });
            })->orWhere(function ($query) use ($clean, $masked) {
                $query->whereHas('getChildrens', function ($query) use ($clean, $masked) {
                    $query->where(function ($query) use ($clean, $masked) {
                        $query->where('criterio', 'ilike', '%' . $clean . '%');
                        $query->orWhere('criterio', 'ilike', '%' . $masked . '%');
                    });
                });
            });
        });

    }

    private function filterProfile(Builder $query, object $filters): void
    {
        $query->whereHas('lote.perfil', function ($query) use ($filters) {
            $query->whereIn('id', $filters->id_perfil);
        });

        /**
         * Para a empresa Banco Fibra (95), no perfil de consulta "Conheça seu funcionário(426)",
         * só devem ser retornados os dossiês dos últimos 90 dias(2160 horas)
         */
        if (session('company') === 'fibra' && in_array(426, $filters->id_perfil)) {
            $query->lastHoursOrMinutes(2160);
        }
    }

    private function filterProfileDefault(Builder $query, object $filters): void
    {
        $query->whereHas('lote.perfilDefault', function ($query) use ($filters) {
            $query->whereIn('id', $filters->id_perfil_default);
        });
    }

    private function filterTag(Builder $query, object $filters): void
    {
        $query->whereHas('lote.tag', function ($query) use ($filters) {
            $query->whereIn('id', $filters->id_tag);
        });
    }

    private function filterWorkflow(Builder $query, object $filters): void
    {
        // Algumas das regras dependem do status selecionado, então é importante separar os blocos.
        $inAnalisysWorkflowStatus = $filters->status_workflow === Workflow::IN_ANALYSIS;

        if ($filters->status_workflow == 'sem_workflow') {
            $query->doesntHave('workflow');
            return;
        }

        $query->has('workflow');

        if ($filters->status_workflow == 'todos') {
            return;
        }

        if (!$inAnalisysWorkflowStatus) {
            $query->where(function ($query) use ($filters) {
                $query->whereNotIn(
                    'id', PesquisaConsulta::select('id')->failed()->hasAutomaticApprovals()->get()
                )->whereHas('workflowAnalise', function ($query) use ($filters) {
                        $query->where('status_dossie_workflow', $filters->status_workflow);
                    })->orWhere(function ($query) use ($filters) {
                        $query->whereHas('workflowAnalise', function ($query) use ($filters) {
                            $query->where('aprovado_automatico', false);
                        })
                            ->whereHas('workflowPesquisaDossie', function ($query) use ($filters) {
                                $query->where('status', $filters->status_workflow);
                            });
                    });
            });
        }

        if ($inAnalisysWorkflowStatus) {
            $query->where(function ($query) {
                $query->whereHas('workflowAnalise', function ($query) {
                    $query->where('status_dossie_workflow', Workflow::IN_ANALYSIS)
                        ->orWhere('status_dossie_workflow', '');
                })->doesnthave('workflowPesquisaDossie')
                    ->orWhere(function ($query) {
                        $query->doesnthave('workflowPesquisaDossie')
                            ->doesnthave('workflowAnalise');
                    });
            });
        }
    }

    /*
        Verifica se o cliente se aplica a regra de exibição de lotes apenas das últimas X horas e/ou Y minutos
    */
    private function getDossiersWithLimitedHoursDays(Builder $query)
    {
        $company = session('company');

        $companies = PesquisaConsulta::COMPANIES_WITH_PURGE;
        if (array_key_exists($company, $companies)) {
            return $query->lastHoursOrMinutes($companies[$company]['hours'] ?? 0, $companies[$company]['minutes'] ?? 0);
        }

        return $query;
    }
}
