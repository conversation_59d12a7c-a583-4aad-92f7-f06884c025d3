<?php

namespace App\Domains\Dossie\Actions\Dossie\List;

use App\Actions\Spine\SpinePfClient;
use App\Actions\Spine\SpinePjClient;
use App\Domains\Dossie\Helpers\DocumentoHelper;
use App\Domains\Dossie\Helpers\SearchNameHelper;

class GetCriterionNameAction
{
    public function __construct(
        private readonly DocumentoHelper $documentoHelper,
        private readonly SearchNameHelper $searchNameHelper,
        private readonly SpinePfClient $spinePfClient,
        private readonly SpinePjClient $spinePjClient
    )
    {}

    public function execute(string $document, int $dossieId): string
    {
        if ($document == 'N/A') {
            return 'N/A';
        }

        $name = $this->getBySearchName($dossieId);

        if ($name === '-') {
            $name = $this->getBySpine($document);
        }

        return $name;
    }

    private function getBySearchName(int $dossieId): string
    {
        return $this->searchNameHelper->mount($dossieId);
    }

    private function getBySpine(string $document): string
    {
        if ($this->documentoHelper->validaCnpj($document)) {
            return $this->spinePjClient->getNomeByDocumento($document);
        } elseif ($this->documentoHelper->validaCpf($document)) {
            return $this->spinePfClient->getNomeByDocumento($document);
        }

        return '-';
    }
}
