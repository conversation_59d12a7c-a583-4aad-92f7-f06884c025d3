<?php

namespace App\Domains\Dossie\Repositories;

use App\Domains\Dossie\Entities\Client\LoteConsulta;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use DB;

class LoteConsultaRepository
{
    private LoteConsulta $loteConsulta;

    public function __construct(LoteConsulta $loteConsulta)
    {
        $this->loteConsulta = $loteConsulta;
    }

    public function getByID(int $id): LoteConsulta
    {
        return $this->loteConsulta->findOrFail($id);
    }

    public function getLoteUsuario(int $id): LoteConsulta
    {
        return $this->loteConsulta->select('id', 'id_usuario', 'id_consulta', 'sub_of', 'tag_id','id_consulta_perfil_default')
            ->where("id", $id)
            ->with([
                'tag' => function ($query) {
                    $query->select('id', 'name', 'font_color', 'color');
                },
                'perfil' => function ($q) {
                    $q->select('id', 'nome', 'tipo_perfil');
                },
                'usuario' => function ($query) {
                    $query->select("id", "nome", "sobrenome");
                },
                'perfilDefault:id,nome',
                "dossieIrmaos" => function ($query) {
                    $query->select('sub_of', 'id', 'id_consulta', 'tag_id')
                        ->with([
                            'tag' => function ($query) {
                                $query->select('id', 'name', 'font_color', 'color');
                            },
                            'perfil' => function ($query) {
                                $query->select('id', 'nome', 'tipo_perfil');
                            },
                            'dossie' => function ($query) {
                                $query->select('id', 'estado', 'status', 'id_lote_consulta', 'criterio', 'data_criacao');
                            }
                        ]);
                }
            ])->first();
    }

    public function consultarHistoricos($lote, $fromDate, $criterio, $criterioRegex)
    {
        return $this->loteConsulta->select()
            ->where('id', '!=', $lote->id)
            ->where(function ($query) use ($lote) {
                $query->whereNull('id_grupo')
                    ->orWhere('id_grupo', $lote->id_grupo);
            })
            ->whereBetween('data_criacao', [$fromDate, $lote->data_criacao])
            ->with([
                'tag' => function ($query) {
                    $query->select('id', 'name', 'font_color', 'color');
                },
                'getPesquisas' => function ($query) {
                    $query->where('dossie', true)
                        ->where('id_pesquisa_consulta', null);
                }
            ])
            ->where('sub_of', null)
            ->where(function ($query) use ($criterio, $criterioRegex) {
                $query->whereRaw(
                    "regexp_replace(CAST(encode(arquivo, 'escape') as text), '[^0-9]*', '', 'g') ilike ?",
                    ["%{$criterio}%"]
                )
                    ->orWhereRaw(
                        "regexp_replace(CAST(encode(arquivo, 'escape') as text), '[^0-9]*', '', 'g') ilike ?",
                        ["%{$criterioRegex}%"]
                    );
            })
            ->orderBy('id', 'asc')
            ->get();
    }

    public function getResumoLote(int $idLote): LoteConsulta
    {
        return $this->loteConsulta->select('id', 'id_consulta', 'estado')
            ->where('estado', '<>', '0')
            ->where('id', '=', $idLote)
            ->with([
                'perfil',
                'getPesquisas' => function ($q) {
                    $q->whereNull('id_pesquisa_consulta')->orderBy('id');
                }
            ])
            ->first();
    }

    public function update(int $id, array $data): bool
    {
        $lote = $this->loteConsulta->findOrFail($id);
        return $lote->update($data);
    }

    public function getLotesforLimit(
        array $usersId,
        int $limit,
        string $lastDay = '',
        string $fisrtDayMonth = ''
    ) {
        $columnMonth = "(CASE WHEN data_criacao BETWEEN DATE_TRUNC('month',CURRENT_DATE) AND (DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month -1 day') THEN true ELSE false END) as mensal";
        return $this->loteConsulta->select(DB::raw($columnMonth), 'arquivo')
        ->when($lastDay, function ($q) use ($lastDay, $fisrtDayMonth) {
            $q->whereBetween('data_criacao', [$fisrtDayMonth, $lastDay]);
        })
        ->whereIn('id_usuario', $usersId)
        ->where('data_criacao', '<>', null)
        ->Where('arquivo', '<>', null)
        ->orderBy('data_criacao', 'DESC')
        ->limit($limit)
        ->get();
    }

    public function create(array $params): LoteConsulta
    {
        return $this->loteConsulta->create($params);
    }

    public function addTag(string $tagId, string $id): bool
    {
        $lote = $this->loteConsulta->findOrFail($id);
        $lote->tag_id = $tagId;
        return $lote->save();
    }

    public function removeTag(string $id): bool
    {
        $lote = $this->loteConsulta->findOrFail($id);
        $lote->tag_id = null;
        return $lote->save();
    }
}
