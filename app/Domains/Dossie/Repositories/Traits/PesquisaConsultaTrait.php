<?php

declare(strict_types=1);

namespace App\Domains\Dossie\Repositories\Traits;

use App\Domains\Dossie\Actions\Dossie\List\GetCriterionNameAction;
use App\Domains\Dossie\Entities\Client\LoteConsulta;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Enums\DossieStatusEnum;
use App\Domains\Dossie\Helpers\DocumentoHelper;
use App\Domains\Dossie\Helpers\StatusHelper;
use App\Domains\LogProcessDossie\Services\LogProcessDossieService;
use App\Domains\Monitoria\Entity\MonitoriaDossieAgendada;

trait PesquisaConsultaTrait
{
    public function sortDossieList(array $params): array
    {
        $response = [];
        if (empty($params['dossies'])) {
            return $response;
        }

        $dossies = $params['dossies'];
        $dossierDone = [];
        $documentoHelper = new DocumentoHelper();

        $session = (object)session()->all();
        // Monta dados da resposta
        foreach ($dossies as $dossie) {
            if (in_array($dossie->id, $dossierDone)) {
                continue;
            }
            $dossierDone[] = $dossie->id;
            $lote = $dossie->lote;

            if ($lote->perfilDefault) {
                $lote->perfil = $lote->perfilDefault;
                $lote->perfil['default'] = true;
            }

            $consulta = $lote->perfil;

            $usuario = trim($lote->usuario->nome . " " . $lote->usuario->sobrenome);

            $workflowStatus = $dossie->getWorkflowStatus();
            $displayCritery = $documentoHelper->formatCriterionForDisplay($dossie->criterio);
            $criterionAssociatedName = app(GetCriterionNameAction::class)
                ->execute($dossie->criterio, $dossie->id);
            $estadoDossie = self::getStatus($dossie);
            $novoEstadoDossie = self::getStatusNewByOld($dossie, $estadoDossie);

            // Dados da monitoria (se tiver)
            $monitoringData = $this->loteHasMonitoring($lote);

            $progress = $dossie->progress;
            if (!$dossie->progress
                || $dossie->progress == 0
                || $dossie->progress < 100
                || $novoEstadoDossie == DossieStatusEnum::PROCESSING->value
            ) {
                app(LogProcessDossieService::class)->getByDossierId($dossie->id, true);
                $progress = $dossie->progress;
            }
            $other = self::getBrother($dossie);
            $year = (int)$dossie->data_criacao?->format('Y');
            $dataDossie = [
                'id' => $dossie->id,
                'status_workflow' => $workflowStatus,
                'data_criacao' => $dossie->data_criacao?->format('d/m/Y H:i:s'),
                'data_estado' => $dossie->data_estado->format('d/m/Y H:i:s'),
                'criterio' => $displayCritery,
                'criterio_nome' => $criterionAssociatedName,
                'otherDossiersBacth' => $other,
                'status' => $novoEstadoDossie,
                'status_old' => $estadoDossie,
                'tipo' => $lote->tipo,
                'tipo_str' => ($lote->tipo == 2) ? 'PJ' : 'PF',
                'lote' => $lote->id,
                'sub_of' => $lote->sub_of,
                'monitoria' => $lote['monitoria'],
                'monitoringData' => $monitoringData,
                'perfil' => $consulta->nome,
                'usuario' => $usuario,
                'tag' => $lote->tag,
                'perfil_default' => $lote->perfil['default'] ?? false,
                'progresso_dossie' => $progress ?? 0,
                'block_migration' => $year < 2024,
                'dossie_convertido' => $dossie->dossie_convertido,
                'dossie_versao' => $dossie->dossie_versao,
                'company' => $session->company,
            ];

            // Não será utilizado na listagem, caso venha a ser só descomentar
            //$dataDossie['derivacao'] = self::getDerivados($dossie->derivados);

            $response[] = $dataDossie;
        }

        return $response;
    }

    public function loteHasMonitoring(LoteConsulta $lote): array|bool
    {
        $session = (object)session()->all();

        $monitoringData = $lote->getMonitoria->first() ?? false;
        if (!empty($monitoringData)) {
            $monitoringData = $monitoringData->toArray();
            $countPausedMonitoring = MonitoriaDossieAgendada::where([
                'client' => $session->company,
                'id_monitoria' => $monitoringData['id'],
                'monitoria_pausada' => true
            ])->count();

            $monitoringData['end'] = false;
            if ($monitoringData['data_final'] < date('Y-m-d') || $countPausedMonitoring > 0) {
                $monitoringData['end'] = true;
            }
        }
        return $monitoringData;
    }

    private static function getDerivados($lotesDerivados)
    {
        $derivados = [];
        $documentoHelper = new DocumentoHelper();
        $searchNameHelper = new SearchNameHelper();
        foreach ($lotesDerivados as $lote) {
            $consulta = $lote->getPesquisas->map(function ($item, $key) use (
                $lote, $documentoHelper, $searchNameHelper
            ) {
                $displayCritery = $documentoHelper->formatCriterionForDisplay($item->criterio);
                $criterionAssociatedName = app(GetCriterionNameAction::class)
                    ->execute($item->criterio, $item->id);

                $workflowStatus = $item->getWorkflowStatus();
                $estadoDossie = self::getStatus($item);
                $novoEstadoDossie = self::getStatusNewByOld($item, $estadoDossie);
                $other = self::getBrother($item);
                return [
                    'id' => $item->id,
                    'data_criacao' => $item->data_criacao->format('d/m/Y H:i:s'),
                    'data_estado' => $item->data_estado->format('d/m/Y H:i:s'),
                    'status_workflow' => $workflowStatus,
                    'otherDossiersBacth' => $other,
                    'status' => $novoEstadoDossie,
                    'status_old' => $estadoDossie,
                    'lote' => $lote->id,
                    'sub_of' => $lote->sub_of,
                    'usuario' => trim($lote->usuario->nome . " " . $lote->usuario->sobrenome),
                    'criterio' => $displayCritery,
                    'criterio_nome' => $criterionAssociatedName,
                    'perfil' => $lote->perfil->nome,
                    'dossie_versao' => $item->dossie_versao

                ];
            })->toArray();
            $derivados = array_merge($derivados, $consulta);
        }
        return $derivados;
    }

    private static function getStatus(PesquisaConsulta $dossiePrincipal): int|string
    {
        return StatusHelper::getStatus($dossiePrincipal);
    }

    private static function getStatusNewByOld(PesquisaConsulta $dossiePrincipal, int|string $status): string
    {
        return StatusHelper::getStatusNewByOld($dossiePrincipal, $status);
    }

    private static function getBrother(PesquisaConsulta $dossie)
    {
        $dossieId = $dossie->id;
        $other = $dossie->brother->filter(function ($item, $key) use ($dossieId) {
            return $dossieId != $item->id;
        });
        return $other?->pluck('id')?->toArray();
    }
}
