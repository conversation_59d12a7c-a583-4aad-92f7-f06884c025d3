<?php

namespace App\Domains\Dossie\Repositories;

use App\Domains\Dossie\Actions\Dossie\List\FilterListDossieAction;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Enums\DossieStatusEnum;
use App\Domains\Dossie\Helpers\CompaniesOnlyGetBatchesFromLastHoursOrMinutesHelper;
use App\Domains\Dossie\Repositories\Traits\PesquisaConsultaTrait;
use Carbon\Carbon;
use DossieSourcesModule\Sources\Entities\Seguranca\SummaryRef;
use Exception;
use Illuminate\Database\Eloquent\Collection;

class PesquisaConsultaRepository
{
    use PesquisaConsultaTrait;

    const PARAM_DOSSIE_LIST = 'dossies';

    private PesquisaConsulta $pesquisaConsulta;

    public function __construct(PesquisaConsulta $pesquisaConsulta)
    {
        $this->pesquisaConsulta = $pesquisaConsulta;
    }

    public function getByID(int $id): PesquisaConsulta
    {
        return $this->pesquisaConsulta->with(['workflowAnalise', 'lote'])->findOrFail($id);
    }

    public function getPaginatedByFilter(array $session, ?array $filters): array
    {
        $filters = (object) $filters;
        $session = (object) $session;

        $dossies = $this->pesquisaConsulta->select([
            'id',
            'id_pesquisa_consulta',
            'id_lote_consulta',
            'id_workflow',
            'captura',
            'estado',
            'status',
            'data_criacao',
            'data_estado',
            'criterio',
            'dossie_convertido',
            'dossie_versao',
            'progress'
        ])
        ->whereHas('lote', function ($query) use ($filters, $session) {

            // Filtrar por grupo quando não há filtro por usuario
            $query->when(
                empty($filters->usuario),
                function ($query) use ($session) {
                    return $query->where('id_grupo', $session->setup['grupo_usuario'])
                        ->orWhere(function ($query) use ($session) {
                            $query->whereNull('id_grupo')
                                ->whereHas('usuario', function ($query) use ($session) {
                                    $query->whereHas('getGrupo', function ($query) use ($session) {
                                        $query->where('id_grupo', $session->setup['grupo_usuario']);
                                    });
                                });
                        });
                }
            );

            // Filtra por Usuario
            $query->when(
                (isset($session->setup['somente_meus_lotes']) && $session->setup['somente_meus_lotes'] != false) ||
                (!$session->master && isset($session->setup_master['demo']) && $session->setup_master['demo']),
                function ($query) use ($session) {
                    return $query->where('id_usuario', $session->setup['id_usuario']);
                },
                function ($query) use ($filters) {
                    if (!empty($filters->usuario)) {
                        return $query->whereIn('id_usuario', $filters->usuario);
                    }
                    return $query;
                }
            );
        })->when(!empty($filters->derivacao) && $filters->derivacao, function($query) {
            $query->with('derivados', function ($query) {
                $query->with([
                    'perfil' => function ($query) {
                        $query->select('id', 'nome', 'versao');
                    },
                    'getPesquisas' => function ($query) {
                        $query->where('id_pesquisa_consulta', null)
                            ->select('id', 'criterio', 'status', 'estado', 'data_criacao',
                                'data_estado', 'id_lote_consulta','dossie_versao','dossie_convertido')
                            ->with([
                                'brother' => function ($query) {
                                    $query->select('id', 'id_lote_consulta');
                                },
                            ]);
                    }
                ]);
            });
        })->with([
            'brother' => function ($query) {
                $query->select('id', 'id_lote_consulta');
            },
            'fonte',
            'getChildrens' => function ($query) {
                $query->select('id_pesquisa_consulta', 'estado');
            },
            'lote' => function ($query) use ($session, $filters) {
                $query->with([
                    'perfil' => function ($query) {
                        $query->select('id', 'nome', 'versao')
                            ->with([
                                'getConsulta' => function ($query) {
                                    $query->select('id_consulta', 'saida')
                                        ->with([
                                            'getCaptura' => function ($query) {
                                                $query->select('id', 'metodo');
                                            }
                                        ])
                                        ->orderBy('id');
                                }
                            ]);
                    },
                    'usuario' => function ($query) {
                        $query->select('id', 'nome', 'sobrenome', 'login');
                    },
                    'getMonitoria' => function ($q) {
                        $q->select(
                            'id', 'id_lote', 'nome_monitoria', 'data_final','diferenca', 'status',
                            'periodicidade', 'ativo'
                        );
                    }, 'tag' => function ($q) {
                        $q->select('id', 'name', 'description', 'color', 'font_color');
                    }
                ]);
            },
            'workflow',
            'workflowAnalise',
            'workflowPesquisaDossie',
        ])
            ->where('id_pesquisa_consulta', null)
            ->where("estado", '<>', 0);
//            ->where(function($query) {
//                $query->where('dossie_versao', 2)
//                    ->orWhere('dossie_convertido', true);
//            });

        $dossies = app(FilterListDossieAction::class)->execute($dossies, $filters);

        //Validação se o cliente se aplica a regra de exibição de dossiês das últimas X horas e/ou Y minutos apenas
        $companies = CompaniesOnlyGetBatchesFromLastHoursOrMinutesHelper::getCompanies();
        if (array_key_exists($session->company, $companies)) {
            $dossies = $dossies->lastHoursOrMinutes(
                $companies[$session->company]['hours'] ?? 0,
                $companies[$session->company]['minutes'] ?? 0
            );
        }

        // Ordena
        if (!empty($filters->order)) {
            $oper = (substr($filters->order, 0, 1) == '-') ? 'desc' : 'asc';
            $campo = substr($filters->order, 1);

            $dossies = $dossies->orderBy($campo, $oper);
        } else {
            $dossies = $dossies->orderBy('id', 'desc');
        }

        $response = [];
        if (isset($filters->all) && !$filters->all) {
            $itemsPerPage = isset($filters->per_page) ? $filters->per_page : $session->setup['itens_pagina'];

            $dossies = $dossies->paginate($itemsPerPage);

            // Monta dados da resposta da paginação
            $response = [
                'total' => $dossies->total(),
                'per_page' => $dossies->perPage(),
                'last_page' => $dossies->lastPage(),
                'current_page' => $dossies->currentPage(),
                'dossies' => []
            ];

            $params[self::PARAM_DOSSIE_LIST] = $dossies;
            $params['filters'] = $filters;
            $response[self::PARAM_DOSSIE_LIST] = $this->sortDossieList($params);
        }

        if (isset($filters->all) && $filters->all) {
            $dossiesResult = collect();
            $dossies->chunk(1000, function ($result) use (&$dossiesResult) {
                $params[self::PARAM_DOSSIE_LIST] = $result;
                $dossiesResult->push($this->sortDossieList($params));
            });

            $response = [self::PARAM_DOSSIE_LIST => $dossiesResult->flatten(1)->toArray()];
        }
        return $response;
    }

    public function hasAppoveAutomatic(int $id): bool
    {
        return !$this->pesquisaConsulta->approve($id)
            ->get()
            ->isEmpty();
    }

    public function getDossieLotePerfilByID(int $id): PesquisaConsulta
    {
        return $this->pesquisaConsulta->where('id', $id)
            ->with([
                'lote' => function ($query) {
                    $query->with('perfil')->first();
                }
            ])
            ->first();
    }

    public function getDossieWithDerivados(int $id): PesquisaConsulta|Exception
    {
        return $this->pesquisaConsulta->select('id', 'criterio', 'id_lote_consulta', 'data_criacao', 'data_estado', 'dossie_versao', 'dossie_convertido')
            ->whereNull('id_pesquisa_consulta')
            ->with([
                'derivados' => function ($query) {
                    $query->select('sub_of', 'id', 'id_consulta', 'tag_id')
                        ->with([
                            'tag' => function ($query) {
                                $query->select('id', 'name', 'font_color', 'color');
                            },
                            'perfil' => function ($query) {
                                $query->select('id', 'nome', 'tipo_perfil');
                            },
                            'dossie' => function ($query) {
                                $query->select('id', 'id_lote_consulta', 'criterio', 'data_criacao', 'dossie_versao', 'dossie_convertido');
                            }
                        ]);
                }
            ])
            ->findOrFail($id);
    }

    public function getStatusSource(int $dossieId): Collection
    {
        return $this->pesquisaConsulta->select(
            'id',
            'id_pesquisa_consulta',
            'estado',
            'captura'
        )->with([
            'fonte' => function ($query) use ($dossieId) {
                $query->with([
                    'pjCategory' => function ($query) use ($dossieId) {
                        $query->with([
                            'tag' => function ($query) use ($dossieId) {
                                $query->where('dossie_id', $dossieId);
                            },
                            'reaction' => function ($query) use ($dossieId) {
                                $query->where('dossie_id', $dossieId);
                            },
                        ]);
                    },
                    'pfCategory' => function ($query) use ($dossieId) {
                        $query->with([
                            'tag' => function ($query) use ($dossieId) {
                                $query->where('dossie_id', $dossieId);
                            },
                            'reaction' => function ($query) use ($dossieId) {
                                $query->where('dossie_id', $dossieId);
                            },
                        ]);
                    },
                ]);
            },
            'tag' => function ($query) use ($dossieId) {
                $query->where('dossie_id', $dossieId);
            },
            'reaction' => function ($query) use ($dossieId) {
                $query->where('dossie_id', $dossieId);
            },
        ])
            ->where(function ($query) use ($dossieId) {
                $query->where('id', $dossieId)
                    ->orWhere('id_pesquisa_consulta', $dossieId);
            })
            ->withCount([
                'result' => function ($query) use ($dossieId) {
                    $query->where('app->details->dossier_id', $dossieId);
                }
            ])
            ->whereNot(
                'id',
                SummaryRef::SOURCE_CHAT_GPT_RESUMO
            )
            ->get();
    }

    public function dossieLoteNavigate(int $dossieId): array
    {
        $pesquisaConsulta = PesquisaConsulta::query()
            ->with(['lote.dossie'])
            ->where('id', $dossieId)
            ->firstOrFail();

        return $pesquisaConsulta->lote
            ->dossie
            ->pluck('id')
            ->sortBy(fn($item) => $item)
            ->values()
            ->toArray();
    }

    public function viewCapturaByDossie(array $viewCapturaByDossie)
    {
        return $this->pesquisaConsulta::query()
            ->where('id', $viewCapturaByDossie['id_dossie'])
            ->whereHas('captura', fn($query) => $query->where('metodo', $viewCapturaByDossie['captura']))
            ->with('result')
            ->first();
    }

    public function historicoDossie($dossieId)
    {
        return $this->pesquisaConsulta->with('lote')
            ->where('id', $dossieId)
            ->first();
    }

    public function getWithChildrensById(int $dossierId)
    {
        return $this->pesquisaConsulta
            ->with(['getChildrens', 'lote', 'fonte'])
            ->where('id', $dossierId)
            ->first();
    }

    public function update(int $id, array $data): bool
    {
        $consulta = $this->pesquisaConsulta->findOrFail($id);
        return $consulta->update($data);
    }

    public function updateByColumn(string $columnName, string $columnValue, array $data): bool
    {
        $consulta = $this->pesquisaConsulta
            ->where($columnName, $columnValue)
            ->firstOrFail();

        return $consulta->update($data);
    }
    public function getProfileIDCriteriaByIdsColumns($id, $sourceId, $columns)
    {
        return $this->pesquisaConsulta
            ->select($columns)
            ->where(function ($q) use ($id) {
                $q->where('id', $id)
                ->orWhere('id_pesquisa_consulta', $id);
            })
            ->where('captura', $sourceId)
            ->with(['lote' => function($q) {
                $q->select('id', 'id_consulta', 'id_consulta_perfil_default');
            }])
            ->first();
    }
    public function getDuplicidadeConsulta($criterios, $validateTimeInMinutes): array
    {
        $final = [];

        $comparisonDate = (string)Carbon::now()
            ->subMinutes($validateTimeInMinutes);
        foreach ($criterios as $criterio) {
            $dossiers = $this->pesquisaConsulta::select([
                'id',
                'id_pesquisa_consulta',
                'id_lote_consulta',
                'captura',
                'criterio',
                'data_criacao',
                'estado',
            ])->whereRaw(
                "(lower(regexp_replace(substr(trim(criterio), 0, position('|' in criterio)), '[^0-9a-zA-Z\s]*', '', 'g')) = '" . $criterio . "' or lower(regexp_replace(criterio, '[^0-9a-zA-Z\s]*', '', 'g')) ='" . $criterio . "')"
            )
                ->where('dossie', true)
                ->where('id_pesquisa_consulta', null)
                ->whereRaw("data_criacao > '{$comparisonDate}'")
                ->whereHas('lote.getGrupo', function ($query) {
                    $query->where('grupo.id', session('setup.grupo_usuario'));
                })->with([
                    'lote' => function ($query) {
                        $query->select('id', 'id_usuario', 'tipo', 'data_criacao', 'id_grupo', 'tag_id')
                            ->with([
                                'usuario' => function ($query) {
                                    $query->select('id', 'login', 'nome', 'sobrenome');
                                }
                            ])
                            ->with([
                                'getGrupo' => function ($query) {
                                    $query->select('id', 'nome');
                                }
                            ]);
                    },
                    'captura' => function ($query) {
                        $query->select('id', 'nome', 'tipo_pessoa');
                    },
                ])
                ->orderBy('data_estado', 'desc')
                ->limit(20)
                ->get();

            if ($dossiers->isNotEmpty()) {
                $dossiers->each(function ($dossie) use (&$final) {
                    $dossie['criterio'] = explode("|", $dossie['criterio']);
                    $final[] =  [
                        'tag_id' => $dossie->lote?->tag_id,
                        'tag' => $dossie?->lote?->tag,
                        'id_dossie' => $dossie?->id,
                        'criterio' => $dossie->criterio[0],
                        'data' => $dossie->data_criacao->format('d/m/Y'),
                        'estado' => $dossie->estado,
                        'loteType' => $dossie->lote->tipo,
                        'username' => "{$dossie->lote->usuario->nome} {$dossie->lote->usuario->sobrenome}",
                        'groupName' => $dossie->lote->getGrupo->nome,
                    ];
                });
            }
        }
        return $final;
    }

    public function create(array $params):PesquisaConsulta
    {
        return $this->pesquisaConsulta->create($params);
    }

    public function updateStatusDossiesToProcessByIdLote(int $loteId)
    {
        return $this->pesquisaConsulta->where('id_lote_consulta', $loteId)
            ->where('status', '!=', DossieStatusEnum::SUCCESS)
            ->where('estado', '!=', 4)
            ->update(
                [
                    'estado' => 1,
                    'status' => DossieStatusEnum::QUEUE
                ]
            );
    }

    public function getInAnalisysWorkflowByError(int $dossierId): Collection
    {
        return $this->pesquisaConsulta->select('id')
            ->ofDossie($dossierId)
            ->failed()
            ->hasAutomaticApprovals()
            ->get();
    }

    public function getWithErrorById(int $dossierId): Collection
    {
        return $this->pesquisaConsulta->select('id', 'id_pesquisa_consulta', 'captura', 'msg', 'criterio')
            ->where('id', $dossierId)
            ->orWhere('id_pesquisa_consulta', $dossierId)
            ->where('status', DossieStatusEnum::ERROR)
            ->where('estado', '5')
            ->get();
    }
}
