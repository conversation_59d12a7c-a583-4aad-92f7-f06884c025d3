<?php

namespace App\Domains\Dossie\Repositories;

use App\Domains\Dossie\Entities\Client\PdfRequestModel;

class PdfRequestRepository
{
    public function __construct(
        private readonly PdfRequestModel $pdfRequestModel
    )
    {
    }

    public function findById(int $id,?string $requestId)
    {
        return $this->pdfRequestModel
            ->query()
            ->select('status', 'id')
            ->when(!empty($requestId), fn($query) => $query->where('id', $requestId))
            ->whereJsonContains('data->id_dossie', $id)
            ->where('expires_at', '>=', date('Y-m-d'))
            ->with('pdf')
            ->first();
    }

    public function getSessionUserPdf(string $token)
    {
        $company = explode('_', $token)[0];

        return $this->pdfRequestModel
            ->query()
            ->from($company . '.pdf_requests')
            ->select('data')
            ->where('token', $token)
            ->firstOrFail()
            ?->data;
    }

}
