<?php

namespace App\Domains\Dossie\Repositories;

use App\Domains\Dossie\Entities\Client\Consulta;
use App\Domains\Dossie\Entities\Client\EstruturaConsultaModel;
use App\Domains\Dossie\Entities\Client\Grupo;
use App\Domains\Dossie\Entities\Dossie\ConsultaPerfilDefault;
use App\Domains\Dossie\Entities\Dossie\EstruturaConsultaPerfilDefault;
use App\Domains\Dossie\Entities\Seguranca\Captura;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class ProfileConsultationRepository
{
    private Model $profile;
    private Model $structProfile;
    private string $mode;
    private const GOOGLE_IDS = [166, 232];
    private const SOURCES_QSA = ['baseEmpresas', 'qsaBoaVista', 'receita_federal_comprovante_cnpj_qsa', 'SpcQsaCnpj', 'BoaVistaQsaScore'];
    public function __construct(
        private readonly EstruturaConsultaModel $estruturaConsultaModel,
        private readonly EstruturaConsultaPerfilDefault $estruturaConsultaModelDefaul,
        private readonly Consulta $consulta,
        private readonly ConsultaPerfilDefault $consultaPerfilDefault,
        private readonly Captura $captura,
        private readonly Grupo $grupo
    ) {
    }

    public function setMode(string $mode)
    {
        $this->mode = $mode;

        if ($this->mode == 'custom') {
            $this->profile = $this->consulta;
            $this->structProfile = $this->estruturaConsultaModel;
            return $this;
        }

        $this->profile = $this->consultaPerfilDefault;
        $this->structProfile = $this->estruturaConsultaModelDefaul;
        return $this;
    }

    public function getById(int $id, bool $basic = false, bool $inactive = false): null|Consulta|ConsultaPerfilDefault
    {
        $select = ['id', 'nome', 'versao', 'ativo', 'tipo_perfil', 'objetivo'];

        if ($this->mode == 'default') {
            $select[] = 'icon_path';
        }

        $profile = $this->profile->select($select);

        if ($basic) {
            return $profile->where('id', '=', $id)
                ->where('ativo', !$inactive)
                ->where('excluido', false)
                ->when($this->mode == 'custom', fn($query) => $query->where('f_novo_upminer', true))
                ->first();
        }

        return $profile->with(['getConsulta' => function ($q) {
            $q->select(
                'id', 'id_consulta', 'nivel', 'entrada',
                'saida', 'campo', 'perfil_saida_pf', 'perfil_saida_pj'
            )
                ->orderBy('nivel', 'asc')
                ->with(['getEntradaCaptura' => function ($q) {
                    $q->select(
                        'id',
                        'nome',
                        'metodo',
                        'tipo_pessoa',
                        'id_grupo_captura_pf',
                        'id_grupo_captura_pj',
                        'id_tipo_captura'
                    );
                }, 'getSaidaCaptura' => function ($q) {
                    $q->select(
                        'id',
                        'nome',
                        'metodo',
                        'tipo_pessoa',
                        'id_grupo_captura_pf',
                        'id_grupo_captura_pj',
                        'id_tipo_captura'
                    )->with(['getGrupoCapturaPf' => function ($q) {
                        $q->select('id', 'nome', 'alias')
                            ->where('tipo', '=', 1);
                    }, 'getGrupoCapturaPj' => function ($q) {
                        $q->select('id', 'nome', 'alias')
                            ->where('tipo', '=', 2);
                    }, 'getCamposEntrada' => function ($q) {
                        $q->select('captura', 'campo');
                    }]);
                }]);
        }], 'getGrupoConsulta', 'getGrupoConsulta.getGrupo', 'chatGptPermission')
            ->where('id', '=', $id)
            ->when($this->mode == 'custom', fn($query) => $query->where('f_novo_upminer', true))
            ->where('ativo', !$inactive)
            ->where('excluido', false)
            ->first();
    }

    public function getPaginatedByFilter(array $params)
    {
        // Checar se deve retornar os perfis padrões através do atributo $mode
        $profiles = $this->profile->select([
            'id', 'nome as name', 'versao as version', 'ativo as status',
            'data_criacao as date_created', 'tipo_perfil as type', 'objetivo as objective'
        ])->when(!empty($params['id_perfil']), function($query) use ($params) {
                $query->where('id', $params['id_perfil']);
            })->with(['permissionChat'=> function($query) {
                $query->select('id_perfil','id_action');
            },'getConsulta' => function ($query) use ($params) {
                $query->select('id', 'id_consulta', 'entrada', 'saida')
                    ->whereNotNull('saida')
                    ->with(['getSaidaCaptura' => function ($query) use ($params) {
                        $query->select('id','nome');
                    }]);
            }])->when($this->mode == 'custom', function ($query) use ($params) {
                $query->with(['getGrupoConsulta' => fn($query) => $query->select(
                    'id_consulta', 'id_grupo', 'id_workflow')
                ]);
            })->when(!empty($params['id_fonte']), function($query) use ($params) {
                $query->whereHas('getConsulta.getSaidaCaptura', function ($query) use ($params) {
                    $query->where('id', $params['id_fonte']);
                });
            })->when(!empty($params['id_grupo']) && $this->mode == 'custom', function($query) use ($params) {
                $query->whereHas('getGrupoConsulta', function ($query) use ($params) {
                    $query->whereIn('id_grupo', $params['id_grupo']);
                });
            })->when(!empty($params['nome']), function($query) use ($params) {
                $query->where('nome', 'iLIKE', '%' . $params['nome'] . '%');
            })
            ->where('excluido', false)
            ->where('ativo', $params['ativo'] ?? true)
            ->when($this->mode == 'custom', fn($query) => $query->where('f_novo_upminer', true));

        if (!empty($params['orderBy'])) {
            $profiles = $profiles->orderBy(
                $params['orderBy']['column'],
                $params['orderBy']['direction']
            );
;        } else {
            $profiles = $profiles->orderBy('nome')->orderBy('versao');
        }

        if (!empty($params['all']) && $params['all']) {
            $profiles = $profiles->get();
        } else {
            $profiles = $profiles->paginate($params['per_page'] ?? 10);
        }

        $profiles->each(function ($profile, $key) {
            $profile->idPerfil = $profile->id;
            $profile->foxyIa = $profile->foxyIa;

            if ($this->mode == 'custom') {
                $profile->groups = $profile->getGrupoConsulta->pluck('id_grupo');
                $profile->workflows = $profile->getGrupoConsulta->filter(function ($value, $key) {
                    return $value['id_workflow'];
                })->pluck('id_grupo');
            }

            $profile->sources = $profile->getConsulta->map(function ($item, $key) {
                $itemFonte = $item->getSaidaCaptura;

                if (!is_null($itemFonte)) {
                    return [
                        'idSource' => $itemFonte['id'],
                        'nameSource' => $itemFonte['nome']
                    ];
                }
            })->toArray();

            unset($profile['getConsulta'], $profile['id'], $profile['getGrupoConsulta'], $profile['permissionChat']);
        });

        return $profiles->toArray();
    }

    public function fetchStructure(array $data)
    {
        return $this->structProfile->query()
            ->select(
                'id_consulta',
                'entrada',
                'saida',
                'campo',
                'perfil_saida_pf',
                'perfil_saida_pj'
            )
            ->with([
                'getCaptura' => function ($query) {
                    $query->select('id', 'metodo', 'nome', 'tipo_pessoa')
                        ->with('getCamposEntrada');
                },
                'getPerfilPf',
                'getPerfilPj',
            ])
            ->whereIn('id_consulta', [$data['profile']])
            ->orderBy('id')
            ->get()
            ->toArray();
    }

    public function getConsultasByGrupo(array $type, $typeCriterio = null)
    {
        $consult = [];
        $consult['personalizada'] = $this->consulta::getConsultasByGrupo($type, true, session('setup.grupo_usuario'))
            ->toArray();
        $consult['default'] = $this->consultaPerfilDefault::getConsultasByGrupo($type, true)
            ->toArray();

        return $consult;
    }

    public function details(int $idPefil)
    {
        $select = ['id', 'nome as name', 'versao as version', 'objetivo as objective'];

        if ($this->mode == 'default') {
            $select[] = 'icon_path';
        }

        return $this->profile->select($select)
            ->withStructSourcesAndGroups(session('setup.grupo_usuario'))
            ->notDeleted()
            ->where('id', $idPefil)
            ->firstOrFail();
    }

    public function getStructureSource(int $profileId)
    {
        return $this->structProfile->where('id_consulta', $profileId)
        ->with(['getCaptura' => function ($q) {
            $q->select('id', 'metodo', 'nome', 'tipo_pessoa')
            ->with('fieldsEntry')->withoutGlobalScope('situacao');
        }])
        ->whereNotNull('saida')
        ->select('saida')
        ->orderBy('saida')
        ->get();
    }

    public function getStructureProfile(int $profileId)
    {
        return $this->structProfile->select('entrada', 'id_consulta', 'saida', 'perfil_saida_pf', 'perfil_saida_pj')
        ->with(
            [
                'getSaidaCaptura' => function ($q) {
                    $q->select(
                        'id',
                        'nome',
                        'tipo_pessoa',
                        'id_grupo_captura_pf',
                        'id_grupo_captura_pj',
                        'situacao',
                        'id_tipo_captura',
                        'metodo',
                        'tabela',
                        'detalhe_captura',
                        'captura_sem_referencia'
                    );
                },
                'getPerfilPf' => function ($q) {
                    $q->select('id')
                        ->with(['getConsulta' => function ($q) {
                            $q->select('id_consulta', 'saida')
                                ->with(['getSaidaCaptura' => function ($q) {
                                    $q->select(
                                        'id',
                                        'nome',
                                        'tipo_pessoa',
                                        'id_grupo_captura_pf',
                                        'id_grupo_captura_pj',
                                        'situacao',
                                        'id_tipo_captura',
                                        'metodo',
                                        'tabela',
                                        'detalhe_captura',
                                        'captura_sem_referencia'
                                    );
                                }]);
                        }]);
                },
                'getPerfilPj' => function ($q) {
                    $q->select()
                        ->with(['getConsulta' => function ($q) {
                            $q->select('id_consulta', 'saida')
                                ->with(['getSaidaCaptura' => function ($q) {
                                    $q->select(
                                        'id',
                                        'nome',
                                        'tipo_pessoa',
                                        'id_grupo_captura_pf',
                                        'id_grupo_captura_pj',
                                        'situacao',
                                        'id_tipo_captura',
                                        'metodo',
                                        'tabela',
                                        'detalhe_captura',
                                        'captura_sem_referencia'
                                    );
                                }]);
                        }]);
                }
            ]
        )
        ->where('id_consulta', $profileId)
        ->orderBy('saida', 'asc')
        ->orderBy('entrada', 'asc')
        ->get();
    }

    public function existInProfileSourcesQsa(int $profileId):bool
    {
        return $this->structProfile->where('id_consulta', $profileId)
        ->whereHas('getEntradaCaptura', fn($q) => $q->whereIn('metodo', ProfileConsultationRepository::SOURCES_QSA))
        ->where('entrada', '<>', 0)
        ->where(fn($q) => $q->whereNotNull('perfil_saida_pf')->orWhereNotNull('perfil_saida_pj'))
        ->exists();

    }

    public function createEstrutura(array $params): EstruturaConsultaModel|EstruturaConsultaPerfilDefault
    {
        return $this->structProfile->create($params);
    }

    public function getSources(): Collection
    {
        return $this->captura->select('id', 'nome as name', 'tipo_pessoa as type')->get();
    }

    public function getGroupUserProfile(): Collection
    {
        return $this->grupo->select("id", "nome as name")
            ->whereHas('getGrupoConsulta.getConsulta', function ($q) {
                $q->where('excluido', false)
                    ->where('ativo', true)
                    ->where('f_novo_upminer', true);
            })
            ->orderBy('nome')
            ->get();
    }

    public function getSourcesHomonyms(int $perfilId): Collection
    {
        return $this->captura->whereHas('getEstruturaConsulta.getConsulta', function ($q) use ($perfilId) {
            $q->where('id', $perfilId)
                ->where('tipo_perfil', 1);
        })->whereHas('getCamposEntrada', function ($q) {;
            $q->where('campo', 'nome_pf');
        })->get(['id'])->pluck('id');
    }

    public function getSourcesByPerfilId(int $perfilId): Collection
    {
        return $this->estruturaConsultaModel->select('nivel', 'saida')->with([
            'getCaptura' => function ($q) use ($perfilId) {
                $q->select('id', 'nome');
            }
        ])->where('id_consulta', $perfilId)->orderBy('nivel')->get();
    }

    public function getWorkflowByPerfilId(int $perfilId, array $params): ?Consulta
    {
        return $this->consulta->select('consulta.id',
            'consulta.id AS id_perfil', 'consulta.nome AS nome_perfil', 'grupo_consulta.*',
            'workflow.nome AS nome_workflow', 'workflow.qt_niveis', 'workflow.data_cadastro AS data_cadastro_workflow')
            ->join('grupo_consulta', function ($q) use ($params) {
                $q->where('grupo_consulta.id_grupo', '=', $params['id_grupo_consulta'])
                    ->on('grupo_consulta.id_consulta', '=', 'consulta.id');
            })
            ->with(['getConsulta' => fn ($q) => $q->select('id_consulta', 'saida')])
            ->join('workflow', function ($q) use ($params) {
                $q->on('workflow.id', '=', 'grupo_consulta.id_workflow')
                    ->on('workflow.id_versao', '=', 'grupo_consulta.id_versao_workflow');
            })
            ->where('grupo_consulta.id_grupo', '=', $params['id_grupo_consulta'])
            ->when(!empty($params['ativo']), fn($query) => $query->where('ativo', $params['ativo']))
            ->where('consulta.excluido', false)
            ->where('consulta.id', $perfilId)
            ->first();
    }

    public function getByTypeId(int $id, ?int $type): null|Consulta|ConsultaPerfilDefault
    {
        return $this->profile->where('id', $id)
            ->when($type, function($query) use ($type) {
                $query->where('tipo_perfil', $type);
            })->first();
    }

    public function create(array $params): Consulta|ConsultaPerfilDefault
    {
        return $this->profile->create($params);
    }

    public function setAsDeleted(int $perfilId, string $obs = null): bool
    {
        return $this->profile
            ->where('id', $perfilId)
            ->update([
                'ativo' => false,
                'excluido' => true,
                'obs_exclusao' => $obs,
                'data_exclusao' =>  date('Y-m-d h:i:s')
            ]
        );
    }

    public function getName($profileId)
    {
        return $this->profile->select('nome as name')
        ->where('id', $profileId)
        ->notDeleted()
        ->first();
    }

    public function getGooglePerfis(array $params, bool $paginated = true): array
    {
        $profiles = $this->profile->where('ativo', true)
            ->where('excluido', false)
            ->whereHas('getConsulta', function ($query) {
                $query->whereIn('saida', self::GOOGLE_IDS);
            });

        if ($this->mode == 'custom') {
            $profiles->where('f_novo_upminer', true);
        }

        $profiles = $profiles->with(['getGoogleGlobal' => function ($query) {
                $query->select('id', 'id_consulta', 'pais')
                    ->orderBy('pais', 'ASC');
            }])
            ->select('id', 'nome', 'versao')
            ->when(
                !empty($params['perfil']), fn($query) => $query->where('nome', 'iLIKE', '%' . $params['perfil'] . '%')
            )
            ->distinct()
            ->orderBy('nome', 'ASC')
            ->orderBy('versao', 'DESC');


        if ($paginated) {
            $profiles = $profiles->paginate($params['per_page'] ?? 12);

            return [
                'total' => $profiles->total(),
                'per_page' => $profiles->perPage(),
                'last_page' => $profiles->lastPage(),
                'current_page' => $profiles->currentPage(),
                'profiles' => $profiles->items()
            ];
        }

        return [
            'profiles' => $profiles->get()
        ];
    }

    public function getWithGoogleGlobalById(int $profileId): null|Consulta|ConsultaPerfilDefault
    {
        $profile = $this->profile->where('ativo', true)
            ->where('excluido', false)
            ->where('id', $profileId);

        if ($this->mode == 'custom') {
            $profile = $profile->where('f_novo_upminer', true);
        }

        return $profile->with(['getGoogleGlobal' => function ($query) {
                $query->select('id', 'id_consulta', 'pais as country', 'lista_exclusao', 'lista_inclusao', 'palavras')
                    ->orderBy('pais', 'ASC');
            }])
            ->select('id', 'nome')
            ->first();
    }

    public function getAllProfiles(array $params): Collection
    {
        if (!empty($params['nome'])) {
            return $this->profile
                ->where('ativo', true)
                ->where('nome', 'iLIKE', '%' . $params['nome'] . '%')->get();
        }

        return $this->profile->where('ativo', true)->get();
    }

    public function inactivateById(int $id): bool
    {
        return $this->profile->where('id', $id)
            ->update(['ativo' => false]);
    }
}
