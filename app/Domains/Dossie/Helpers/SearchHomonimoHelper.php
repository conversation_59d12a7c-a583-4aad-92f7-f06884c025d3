<?php

declare(strict_types=1);

namespace App\Domains\Dossie\Helpers;

use App\Actions\Spine\SpinePfClient;
use App\Actions\Spine\SpinePjClient;

class SearchHomonimoHelper
{
    private $modeMap = [
        'PF' => SpinePfClient::class,
        'PJ' => SpinePjClient::class,
    ];

    public function mount(string $name, string $type): string
    {
        if ($name === '-') {
            return $name;
        }

        if (isset($type) && array_key_exists($type, $this->modeMap)) {
            $modeClass = $this->modeMap[$type];
            $qnt = (new $modeClass())->getQntHomonimos($name);
            return $qnt > 1 ?
            trans("dossie.homonimos", ['qnt' => $qnt]) :
            trans("dossie.homonimos.{$qnt}");
        }
        
        return '-';
    }
}
