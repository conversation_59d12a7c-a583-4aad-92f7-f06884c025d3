<?php

declare(strict_types=1);

namespace App\Domains\Dossie\Helpers;

use App\Domains\Dossie\Repositories\LoteConsultaRepository;
use App\Domains\User\Entities\User;
use App\Domains\User\Repositories\UserRepository;
use App\Domains\Dossie\Helpers\DossierLimit\DossierLimitAccount;
use App\Domains\Dossie\Helpers\DossierLimit\DossierLimitApi;
use App\Domains\Dossie\Helpers\DossierLimit\DossierLimitUser;
use Exception;
use App\Exceptions\CustomException;

class DossierLimitHelper
{
    public function __construct(
        private readonly LoteConsultaRepository $loteRespository,
        private readonly DossierLimitAccount $dossierLimitAccount,
        private readonly DossierLimitApi $dossierLimitApi,
        private readonly DossierLimitUser $dossierLimitUser,
        private readonly UserRepository $userRespository
    ) {}

    public function mount(int $criteria): bool|Exception
    {
        $lastDay = date("Y-m-d", strtotime("+1 days", strtotime("last day of this month")));
        //primeiro dia do mes
        $fisrtDayMonth = date("Y-m-d", strtotime("first day of this month"));

        $limiteConta = $this->userRespository->getDossierLimitsMaster(session('setup.id_master'));

        if (($limiteConta->qtd_dossie_total === 0 ||
            session('setup.limite_dossies') === 0) ||
            ($limiteConta->qtd_dossie === 0 ||
                session('setup.limite_dossies_total') === 0
            )
        ) {
            throw new CustomException(trans('error.dossier-limit-zero'), 1);
        }

        $setupUser = User::select('qtd_dossie_total','qtd_dossie')
            ->where('id',session('setup.id_usuario'))
            ->firstOrFail();

        $limiteTotalUsuario = $setupUser->qtd_dossie_total;
        $limiteMensalUsuario =  $setupUser->qtd_dossie;
        $limiteMensalApi = session('setup.api_dossier_monthly_limiter', 0);

        if (!is_null($limiteTotalUsuario) || !is_null($limiteMensalUsuario)) {
            $limit = max([$limiteMensalUsuario, $limiteTotalUsuario]) ?? 0;
            $loteGenerated = $this->loteRespository->getLotesforLimit(
                [session('setup.id_usuario')],
                $limit
            );
            $this->dossierLimitUser->check(
                $criteria,
                $loteGenerated,
                $limiteMensalUsuario,
                $limiteTotalUsuario
            );
        }

        $usersId = $this->userRespository->getUserIdAccount(
            explode('_', session('setup.login'))[0]
        )->pluck('id')->toArray();

        if (session('api') && $limiteMensalApi > 0) {
            $limit = max([$limiteMensalApi]);

            $loteGenerated = $this->loteRespository->getLotesforLimit(
                $usersId,
                $limit,
                $lastDay,
                $fisrtDayMonth
            );
            $this->dossierLimitApi->setParams(
                $limiteConta->nome,
                $limiteMensalApi
            );
            $this->dossierLimitApi->check(
                $criteria,
                $loteGenerated,
                $limiteMensalApi
            );
        }

        if (!is_null($limiteConta->qtd_dossie_total) || !is_null($limiteConta->qtd_dossie)) {
            $limit = max([$limiteConta->qtd_dossie, $limiteConta->qtd_dossie_total]) ?? 0;

            $loteGenerated = $this->loteRespository->getLotesforLimit(
                $usersId,
                $limit
            );

            $this->dossierLimitAccount->check(
                $criteria,
                $loteGenerated,
                $limiteConta->qtd_dossie,
                $limiteConta->qtd_dossie_total
            );
        }

        return false;
    }
}
