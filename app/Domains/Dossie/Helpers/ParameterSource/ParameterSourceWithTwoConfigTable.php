<?php

namespace App\Domains\Dossie\Helpers\ParameterSource;

use App\Domains\Dossie\Helpers\ParameterSource\ParameterSourceHelper;
use Illuminate\Database\Eloquent\Collection;
use App\Domains\Dossie\Transforms\ParameterSourceTransform;
use App\Domains\Dossie\Interface\ParameterizableWithTwoTable;

abstract class ParameterSourceWithTwoConfigTable extends ParameterSourceHelper implements ParameterizableWithTwoTable
{

    public function mount(): array
    {
        $this->getData();
        $this->transform((new ParameterSourceTransform()));
        $data = $this->getData2();
        return $this->setData2($data);
    }
}
