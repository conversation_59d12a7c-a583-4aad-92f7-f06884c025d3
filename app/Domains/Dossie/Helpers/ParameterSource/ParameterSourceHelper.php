<?php

namespace App\Domains\Dossie\Helpers\ParameterSource;

use App\Domains\Dossie\Interface\Parameterizable;
use Illuminate\Database\Eloquent\Collection;
use App\Domains\Dossie\Entities\Seguranca\ParametroCapturaParametro;
use App\Domains\Dossie\Transforms\ParameterSourceTransform;
use App\Domains\Dossie\Interface\Transformable;

abstract class ParameterSourceHelper implements Parameterizable
{
    public Collection $parameter;
    public int $sourceId;
    public array $fileds;
    public array $data;

    public function mount(): array
    {
        $this->getData();
        $this->transform((new ParameterSourceTransform()));
        return $this->data;
    }
    public function getData(): void
    {
        $this->parameter = ParametroCapturaParametro::select('id', 'nome')->with([
            'details' => function ($q) {
                $q->select(
                    'id',
                    'id_captura_parametro',
                    'label',
                    'valor'
                );
            }
        ])
        ->where('id_captura', $this->sourceId)
        ->get();
    }

    public function transform(Transformable $transform):void
    {
        $this->data = $transform->handle(
            $this->parameter,
            $this->fileds
        );
    }
}
