<?php

namespace App\Domains\Dossie\Helpers\ParameterSource;

use Illuminate\Database\Eloquent\Collection;
use App\Domains\Dossie\Entities\UpSearch\Origins;
use App\Domains\Dossie\Transforms\ParameterSourceDiariosOficiaisTransform;
use App\Domains\Dossie\Interface\Transformable;
use App\Domains\Dossie\Helpers\ParameterSource\ParameterSourceHelper;

abstract class ParameterSourceDiariosOficiaisHelper extends ParameterSourceHelper
{
    public Collection $parameter;
    public int $sourceId;
    public array $fileds = ['uf', 'name', 'description'];
    public array $data;

    public function mount(): array
    {
        $this->getData();
        $this->transform((new ParameterSourceDiariosOficiaisTransform()));
        return $this->data;
    }
    public function getData(): void
    {
        $this->parameter = Origins::select(
            'state_alias as uf',
            'id as name',
            'name as description'
        )
        ->orderBy('state')
        ->orderBy('name')
        ->get();
    }
}
