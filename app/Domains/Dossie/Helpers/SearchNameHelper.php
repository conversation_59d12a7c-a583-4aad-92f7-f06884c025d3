<?php

declare(strict_types=1);

namespace App\Domains\Dossie\Helpers;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Helpers\GetCriterioNomeHelper;

class SearchNameHelper
{
    public function mount(int $dossieId): string
    {
        $dossie = PesquisaConsulta::select('id', 'captura')->where(function ($q) use ($dossieId) {
            $q->where('id', $dossieId)
            ->orWhere('id_pesquisa_consulta', $dossieId);
        })
        ->whereIn('captura', GetCriterioNomeHelper::ID_SOURCES_CLASS)
        ->where('estado', 4)
        ->whereHas('result', function ($q) use ($dossieId) {
            $q->whereJsonContains('app->details->dossier_id', $dossieId)
            ->whereIn('source_id', GetCriterioNomeHelper::ID_SOURCES_CLASS);
        })
        ->with([
            'result' => function ($q) use ($dossieId) {
                $q->select('result', 'source_id')
                ->whereJsonContains('app->details->dossier_id', $dossieId)
                ->whereIn('source_id', GetCriterioNomeHelper::ID_SOURCES_CLASS)
                ->with(['fonte' => function ($q) {
                    $q->select('metodo', 'id');
                }]);
            },
        ])
        ->orderBy('captura')
        ->get();
        foreach ($dossie as $key => $value) {
            $name = is_null($value?->result) ? false :$this->processName($value);
            if ($name) {
                return $name;
            }
        };
        return '-';
    }

    private function processName(PesquisaConsulta $dossie):string| bool
    {
        $data = json_decode($dossie->result->result, true)[0];
        $metodo = $dossie->result->fonte->metodo;

        return GetCriterioNomeHelper::$metodo($data);
    }
}
