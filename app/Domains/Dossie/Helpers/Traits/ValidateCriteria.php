<?php

namespace App\Domains\Dossie\Helpers\Traits;

trait ValidateCriteria
{
    public function validaCpf($cpf)
    {
        if (strpos($cpf, ' ') !== false) {
            return false;
        }

        $cpf = preg_replace("@\D+@i", "", $cpf);
        if (strlen($cpf) != 11) {
            return false;
        }

        if (preg_match('@([0-9])\1{10}@', $cpf)) {
            return false;
        }

        if (!preg_match("@[0-9]{11}@", $cpf)) {
            return false;
        }

        $soma1 = 0;
        $soma2 = 0;

        for ($i = 0; $i < 9; $i++) {
            $soma1 += (10 - $i) * $cpf[$i];
            $soma2 += (11 - $i) * $cpf[$i];
        }

        $dv1 = (($soma1 % 11) < 2) ? 0 : 11 - ($soma1 % 11);
        $soma2 += $dv1 * 2;
        $dv2 = (($soma2 % 11) < 2) ? 0 : 11 - ($soma2 % 11);


        return ($cpf[9] == $dv1 && $cpf[10] == $dv2);
    }

    public function validaCnpj($cnpj)
    {

        $cnpj = preg_replace("@\D+@i", "", $cnpj);
        if (strlen($cnpj) != 14) {
            return false;
        }
        if (preg_match('@([0-9])\1{13}@', $cnpj)) {
            return false;
        }
        if (!preg_match("@[0-9]{14}@", $cnpj)) {
            return false;
        }

        $k = 6;
        $soma1 = 0;
        $soma2 = 0;
        for ($i = 0; $i < 13; $i++) {
            $k = ($k == 1) ? 9 : $k;
            $soma2 += ($cnpj[$i] * $k);
            $k--;
            if ($i < 12) {
                if ($k == 1) {
                    $k = 9;
                    $soma1 += ($cnpj[$i] * $k);
                    $k = 1;
                } else {
                    $soma1 += ($cnpj[$i] * $k);
                }
            }
        }
        return ($cnpj[12] == (($soma1 % 11 < 2) ? 0 : 11 - $soma1 % 11) && $cnpj[13] == (($soma2 % 11 < 2) ? 0 : 11 - $soma2 % 11));
    }

    public function validaNome($nome)
    {
        return (preg_match_all("#[^\&\s\d\w\'.,/()-]#uis", $nome) === 0);
    }
}
