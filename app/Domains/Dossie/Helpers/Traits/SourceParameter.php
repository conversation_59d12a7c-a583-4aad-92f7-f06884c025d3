<?php

namespace App\Domains\Dossie\Helpers\Traits;

use App\Domains\Dossie\Helpers\ParametrizacaoOldDossie;
use App\Domains\Dossie\Entities\Client\LoteConsulta;

trait SourceParameter
{
    public function setParameter(array $sources, array $parameters, string $criteria, LoteConsulta $lote)
    {
        // montagem de parametrização das capturas
        foreach ($sources as $metodo => $captura) {
            if (
                $captura->id == 62 &&
                session('setup.id_master') == 1235 &&
                !isset($parameters[$metodo])
            ) {
                $parametrizacao = [
                    "parametros" => [
                        "exato" => "true"
                    ]
                ];
            } elseif (
                $captura->id == 15 &&
                in_array(session('setup.id_master'), [19426, 28875])
                &&
                !isset($parameters[$metodo])
            ) {
                $parametrizacao = [
                    "parametros" => [
                        "pep" => true
                    ]
                ];
            } else {
                $parametrizacao = isset($parameters[$metodo]) ?
                $parameters[$metodo] :
                ["parametros" => []];
            }
            
            ParametrizacaoOldDossie::$metodo(
                $lote,
                $captura,
                $parametrizacao,
                [$criteria]
            );
        }
    }
}
