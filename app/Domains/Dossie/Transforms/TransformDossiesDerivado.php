<?php

namespace App\Domains\Dossie\Transforms;

use Illuminate\Database\Eloquent\Collection;
use App\Domains\Dossie\Transforms\TransformDocumento;
use App\Domains\Dossie\Helpers\SearchNameHelper;
use App\Domains\Dossie\Helpers\SearchHomonimoHelper;



class TransformDossiesDerivado
{
    private TransformDocumento $transformDocumento;
    private SearchNameHelper $searchNameHelper;
    private SearchHomonimoHelper $searchHomonimoHelper;
    public function __construct(
        SearchNameHelper $searchNameHelper,
        SearchHomonimoHelper $searchHomonimoHelper,
        TransformDocumento $transformDocumento
    ) {
        $this->transformDocumento = $transformDocumento;
        $this->searchNameHelper = $searchNameHelper;
        $this->searchHomonimoHelper = $searchHomonimoHelper;
    }
    public function handle(Collection $data, int $dossieId): array
    {
        $dossies = [];
        foreach ($data as $key => $lote) {
            foreach ($lote->dossie as $key => $dossie) {
                $name = $this->searchNameHelper->mount($dossie->id);
                $homonimo = $this->searchHomonimoHelper->mount(
                    $name,
                    $lote?->perfil?->tipo_perfil == 1 ? 'PF': 'PJ'
                );

                $dossies[] = [
                    'dossieId' => $dossie->id,
                    'criterio' => $this->transformDocumento->handle(
                        explode(
                            '|',
                            $dossie->criterio
                        )[0]
                    ),
                    'loteId' => $dossie->id_lote_consulta,
                    'tag' => $lote->tag,
                    'createdAt' => $dossie->data_criacao->format('d/m/Y H:i:s'),
                    'perfil' => $lote?->perfil?->nome,
                    'tipoPerfil' => $lote?->perfil?->tipo_perfil == 1 ? 'PF': 'PJ',
                    'aberto' => $dossie->id == $dossieId,
                    'nome' => $name,
                    'homonimo' => $homonimo,
                    'dossie_versao' => $dossie->dossie_versao,
                    'dossie_convertido' => $dossie->dossie_convertido,
                ];
            }
        }
        return $dossies;
    }
}
