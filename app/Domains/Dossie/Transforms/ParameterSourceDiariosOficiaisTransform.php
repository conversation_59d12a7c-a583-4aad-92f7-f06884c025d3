<?php

namespace App\Domains\Dossie\Transforms;

use App\Domains\Dossie\Interface\Transformable;

class ParameterSourceDiariosOficiaisTransform implements Transformable
{
    public function handle($data, $fields):array
    {
        $result = [
            'parametro' => [
                'Diarios' => [],
            ],
        ];
        $data->each(function ($parametro) use (&$result, $fields) {
            $result['parametro']['Diarios'][] = $parametro->only($fields);
        });

        return $result;
    }
}
