<?php

namespace App\Domains\Dossie\Transforms;

use App\Domains\Dossie\Helpers\JuridicSources\CourtsHelper;
use Illuminate\Support\Arr;

abstract class AbstractSourceSummaryTypeJuridico
{
    const SUMMARY_INDEXES = [
        'processosComoAutor', 'processosComoReu', 'terceirosEnvolvidos', 'orgaosJulgadores',
        'statusDeProcessos', 'classesDeProcessos', 'principaisAssuntos', 'processosPorTribunais',
    ];

    const STATUS_KEYS = [
        'instance4' => ['statusPredictus.statusProcesso'],
        'instance5' => ['status']
    ];

    const COURT_KEYS = [
        'instance4' => ['tribunal'],
        'instance5' => ['tribunal_acronym']
    ];

    const COIN_KEYS = [
        'instance4' => ['valorCausa.moeda'],
        'instance5' => ['amount']
    ];
    const AMOUNT_KEYS = [
        'instance4' => ['valorCausa.valor'],
        'instance5' => ['amount']
    ];
    const VALUE_CAUSE_KEY = [
        'instance4' => ['valorCausa.moeda', 'valorCausa.valor'],
        'instance5' => ['amount.moeda', 'amount.valor'],
    ];

    const PARTIES_KEYS = [
        'instance4' => ['partes'],
        'instance5' => ['parties']
    ];
    const STATE_KEYS = [
        'instance4' => ['uf'],
        'instance5' => ['uf']
    ];
    const LAWSUIT_KEYS = [
        'instance4' => [
            'num' => 'numeroProcessoUnico',
            'participacao' => 'partes',
            'uf' => 'uf',
            'titulo' => 'assuntosCNJ',
            'valor' => 'valorCausa',
        ],
        'instance5' => [
            'uf' => 'uf',
            'num' => 'code',
            'valor' => 'amount',
            'participacao' => 'parties',
            'titulo' => 'subjects'
        ]
    ];

    /**
     * Para casos de procesos onde pode ter múltiplas ocorrências (assuntos, classes)...
     * Então nestes casos o cálculo da porcentagem precisa ser feito em cima do total
     * da parte do resumo passado por parâmetro ao invés do total de processos.
     *
     * @param array $summary
     * @param string $index
     * @param $instance
     * @param $filters
     * @return array
     */
    public static function processAndCountBySummary(array $summary, string $index, int $instance, array $filters): array
    {
        $summary['summaries'][$index]['summaryData']
            = self::treatData($summary['summaries'][$index]['summaryData'], $instance, $filters);

        $summary['summaries'][$index]['totalQtd'] = self::countTotalSummary(
            $summary['summaries'][$index]['summaryData']
        );

        $total = $summary['summaries'][$index]['totalQtd'];
        foreach ($summary['summaries'][$index]['summaryData'] as &$summaryData) {
            $summaryData['percentageOfTotal']
            = round(($summaryData['qtd'] / $total) * 100, 2);
        }
        return $summary;
    }

    /**
     * Calcula o total de itens de uma parte do resumo: Ex: principaisAssuntos
     *
     * @param array $summaryData
     * @return int
     */
    public static function countTotalSummary(array $summaryData): int
    {
        $totalSummary = 0;
        foreach ($summaryData as $key => $value) {
            if (!empty($summaryData[$key]['qtd'])) {
                $totalSummary += $summaryData[$key]['qtd'];
            }
        }
        return $totalSummary;
    }

    /**
     * Aplica os filtros
     *
     * @param array $summaryData
     * @param array $filters
     * @param int $instance
     * @return array
     */
    public static function applyFilters(array $summaryData, array $filters, int $instance): array
    {
        return collect($summaryData)->map(function ($summary) use ($filters, $instance) {
            $keptData = collect($summary['keptData']);
            $hasAllowed = $keptData->filter(function ($court) use ($filters, $instance) {
                if (self::filterLawSuits($filters, $court, $instance)) {
                    return true;
                }
            })
                ->filter()
                ->values()
                ->toArray();

            if (!empty($hasAllowed)) {
                $summary['qtd'] = count($hasAllowed);
                $summary['keptData'] = $hasAllowed;
                return $summary;
            }
        })
            ->filter()
            ->toArray();
    }

    protected static function parse(
        $formatedData,
        $instance,
        $summary,
        $type,
        $filters
    ): array {
        $totalLawSuits = $summary['summaries']['totalResults'] ?? 0;
        $indexes = self::SUMMARY_INDEXES;

        // Verificar os filtros
        if (!empty($filters)) {
            $filters['tribunais'] = !empty($filters['tribunais']) ? CourtsHelper::getCourts($filters['tribunais']) : [];
            $filters['status'] = !empty($filters['status']) ? CourtsHelper::getStatus($filters['status']) : [];
            $filters['participacao'] = !empty($filters['participacao']) ? CourtsHelper::getParties($filters['participacao']) : [];
            $filters['UF'] = !empty($filters['UF']) ? CourtsHelper::getState($filters['UF']) : [];

            // Retirar do array de index para não fazer de novo
            $key = array_search('processosPorTribunais', $indexes);
            unset($indexes[$key]);

            $summary['summaries']['processosPorTribunais']['summaryData'] = self::treatData(
                $summary['summaries']['processosPorTribunais']['summaryData'], $instance, $filters
            );

            // Para setar o total de processos no resumo processosPorTribunais com filtro.
            $totalLawSuits = $summary['summaries']['processosPorTribunais']['totalQtd'] = self::countTotalSummary(
                $summary['summaries']['processosPorTribunais']['summaryData']
            );

            // Total de processos calculados com os filtros
            $summary['summaries']['totalResults'] = $totalLawSuits;
        }

        // Somar o valor total dos processos
        $summary['summaries']['valorDosProcessos'] = self::sumLawSuits(
            $summary['summaries']['valorDosProcessos'], $instance, $filters
        );
        // Efetuar demais transforms com filtros e porcentagem
        foreach($indexes as $index) {
            // Substituir os status da pelo da Predictus
            if (in_array($index, ['statusDeProcessos'])) {
                $summary['summaries'][$index]['summaryData'] = self::replaceStatus(
                    $summary['summaries'][$index]['summaryData']
                );
            }

            if (in_array($index, ['processosComoAutor', 'processosComoReu', 'terceirosEnvolvidos'])) {
                $summary['summaries'][$index]['summaryData'] = self::treatData(
                    $summary['summaries'][$index]['summaryData'], $instance, $filters
                );

                $summary['summaries'][$index]['totalQtd'] = self::countTotalSummary(
                    $summary['summaries'][$index]['summaryData']
                );

                continue;
            }

            // Os processos podem ter múltiplos assuntos, então o cálculo da porcentagem precisa ser feito em cima
            // do total de assuntos ao invés do total de processos.
            if (in_array($index, ['principaisAssuntos'])) {
                $summary = self::processAndCountBySummary($summary, $index, $instance, $filters);
                continue;
            }

            // No caso da Judit (instance 5) os processos possuem múltiplas classes. Então deverá ser feito a
            // contabilização por essa parte do resumo ao invés do total de processos ($totalLawSuits).
            if (in_array($index, ['classesDeProcessos']) && $instance == 5) {
                $summary = self::processAndCountBySummary($summary, $index, $instance, $filters);
                continue;
            }

            $summary['summaries'][$index]['summaryData'] = self::treatData(
                $summary['summaries'][$index]['summaryData'], $instance, $filters, $totalLawSuits
            );

            $summary['summaries'][$index]['totalQtd'] = self::countTotalSummary(
                $summary['summaries'][$index]['summaryData']
            );
        }

        $summary['summaries']['detalhesProcessos']['summaryData'] = self::getLawSuitsIds(
            $summary['summaries']['processosPorTribunais']['keptData'],
            $instance,
            $filters,
            $summary['source_id']
        )[0];

        $summary['summaries']['detalhesProcessosComoAutor']['summaryData'] = self::getLawSuitsDetails(
            $summary['summaries']['processosComoAutor']['keptData'],
            $instance,
            $filters,
            $summary['source_id']
        );

        $summary['summaries']['detalhesProcessosTerceirosEnvolvidos']['summaryData'] = self::getLawSuitsDetails(
            $summary['summaries']['terceirosEnvolvidos']['keptData'],
            $instance,
            $filters,
            $summary['source_id']
        );
        $summary['summaries']['detalhesProcessosComoReu']['summaryData'] = self::getLawSuitsDetails(
            $summary['summaries']['processosComoReu']['keptData'],
            $instance,
            $filters,
            $summary['source_id']
        );
        $summary['summaries']['processosPorTribunais']['summaryData'] = self::replaceCourts(
            $summary['summaries']['processosPorTribunais']['summaryData'], $totalLawSuits
        );

        // Limpar o resumo de campos que não serão mais utilizados
        $summary = self::unsetData($summary);
        if (!isset($formatedData[$type][$instance])) {
            $formatedData[$type]['instance_' . $instance] = [
                'instance' => $summary['instancia'],
                'instanceName' => $summary['instanciaName'],
                'source_group' => $summary['source_group'],
                'summaries' => $summary['summaries'],
            ];
        }
        return $formatedData;
    }

    /**
     * Tratar os dados de cada parte do resumo da fonte. Aplicar os filtros e retornar.
     *
     * @param array $summaryData
     * @param array $filters
     * @param int|null $totalProcceses - total em valor
     * @return array
     */
    protected static function treatData(
        array $summaryData,
        int $instance,
        array $filters = [],
        int $total = null
    ): array
    {
        if (!empty($filters)) {
            $summaryData = self::applyFilters($summaryData, $filters, $instance);
        }

        foreach ($summaryData as $key => $value) {
            if ($key == 'nao_informado') {
                $summaryData[$key]['name'] = "NÃO INFORMADO";
            }

            if ($total) {
                $summaryData[$key]['percentageOfTotal']
                = round(($summaryData[$key]['qtd'] / $total) * 100, 2);
            }

            foreach($summaryData[$key]['keptData'] as &$keptData) {
                $statusKey = self::getStatusKeyByInstance($instance);
                $status = Arr::get($keptData, $statusKey);
                $status = CourtsHelper::mapStatusToPredictus($status);
                Arr::set($keptData, $statusKey, $status);
            }
        }

        return $summaryData;
    }

    /**
     * Filtrar os processos por tribunais e/ou status.
     * Para utilizar dentro de um filter
     *
     * @param array $filters
     * @param array $data
     * @return bool
     */
    protected static function filterLawSuits(array $filters, array $data, int $instance): bool
    {
        $court = Arr::get($data, self::getCourtKeyByInstance($instance));
        $status = Arr::get($data, self::getStatusKeyByInstance($instance));
        $parte = Arr::get($data, self::getParteKeyByInstance($instance));
        $state = Arr::get($data, self::getStateKeyByInstance($instance));
        $returnCourt= true;
        $returnStatus= true;
        $returnParties= true;
        $returnState =  true;
        if (!empty($filters['tribunais'])) {
            $returnCourt = in_array(CourtsHelper::clearCourt($court), $filters['tribunais']);
        }
        if (!empty($filters['status'])) {
            $returnStatus = in_array(
                CourtsHelper::mapStatusToPredictus($status),
                $filters['status']
            );
        }
        if (!empty($filters['participacao'])) {
            $returnParties = !empty(
                array_intersect(
                    CourtsHelper::mapParte($parte),
                    $filters['participacao']
                )
            );
        }

        if (!empty($filters['UF'])) {
            $returnState = in_array(
                $state,
                $filters['UF']
            );
        }

        return $returnCourt && $returnStatus && $returnParties
        && $returnState;
    }

    /**
     * Substitui os status possibilitar a filtragem padrão.
     *
     * @param array $summaryData
     * @return array
     */
    protected static function replaceStatus(array $summaryData): array
    {
        foreach($summaryData as $statusIndex => $status) {
            $newStatus = CourtsHelper::mapStatusToPredictus($status['name']);
            unset($summaryData[$statusIndex]);
            $status['name'] = $newStatus;
            $summaryData[$newStatus] = $status;
        }

        return $summaryData;
    }

    protected static function replaceCourts(array $summaryData, int $totalLawSuitses): array
    {
        $courtsMapped = [];
        foreach($summaryData as $summary) {
            $courtByType = CourtsHelper::findTypeOfCourtByName($summary['name']);
            $qtd = $summary['qtd'];
            if ($courtByType) {
                if (empty($courtsMapped[$courtByType]['qtd'])) {
                    $courtsMapped[$courtByType]['qtd'] = 0;
                }

                $courtsMapped[$courtByType]['name'] = $courtByType;
                $courtsMapped[$courtByType]['qtd'] += $qtd ;
            }
        }

        foreach($courtsMapped as &$court) {
            $court['percentageOfTotal'] = round(($court['qtd'] / $totalLawSuitses) * 100, 2);
        }

        return $courtsMapped;
    }

    /**
     * Limpar o total de processos
     *
     * @param array $summary
     * @return array
     */
    protected static function unsetData(array $summary): array
    {
        foreach (self::SUMMARY_INDEXES as $index) {
            unset($summary['summaries'][$index]['keepData']);
            unset($summary['summaries'][$index]['keptData']);
            unset($summary['summaries'][$index]['pathToData']);
            unset($summary['summaries'][$index]['indexToSummary']);
            unset($summary['summaries'][$index]['summaryByField']);
            unset($summary['summaries'][$index]['callBackFunction']);
            unset($summary['summaries'][$index]['summaryByFieldLabel']);
        }

        return $summary;
    }

    /**
     * Somar o total de processos
     *
     * @param array $summaryData
     * @param array $filters
     * @return array
     */
    protected static function sumLawSuits(array $summaryData, int $instance, array $filters = []): array
    {
        $values = [];
        $t = $summaryData['keptData'];
        if (!empty($filters)) {
            $keptData = collect($summaryData['keptData'])->map(function($summary) use ($filters, $instance) {
                if (self::filterLawSuits($filters, $summary, $instance)) {
                    return $summary;
                }
            })
                ->filter()
                ->values()
                ->toArray();

            $summaryData['keptData'] = $keptData;
        }

        foreach ($summaryData['keptData'] as $k => &$data) {
            $statusKey = self::getStatusKeyByInstance($instance);
            $coinKey = self::getCoinKeyByInstance($instance);
            $amountKey = self::getAmountKeyByInstance($instance);
            $status = Arr::get($data, self::getStatusKeyByInstance($instance));

            $status = CourtsHelper::mapStatusToPredictus($status);
            $coin = $data[self::getCoinKeyByInstance($instance)];
            $amount = $data[self::getAmountKeyByInstance($instance)];

            Arr::set($data, $statusKey, $status);
            Arr::set($data, $amountKey, $amount);
            Arr::set($data, $coinKey, $coin);
            $values[$k] = [
                'moeda_valor_causa' => $coin,
                'valor_causa' => self::sumValues($amount)
            ];
            // $values[]['valor_causa'] = self::sumValues($amount);
        }

        $summaryData['summaryData'] = [];
        $summaryData['summaryData']['totalValue'] = TransformSourceSummaryTypeJuridico::sumValues($values);
        if ($summaryData['summaryData']['totalValue'][0] === 'Não Informado') {
            $summaryData['summaryData']['totalValue'][0] = 'R$ 0';
        }
        // Para casos com moeda tipo Cruzeiro
        if ($summaryData['summaryData']['totalValue'][1] === 'Não Informado') {
            $summaryData['summaryData']['totalValue'][1] = '';
        }
        return $summaryData;
    }

    private static function getLawSuitsIds(array $summaryData, int $instance, array $filters, int $sourceId): array
    {
        $values = [];

        if (!empty($filters)) {
            $summaryData = collect($summaryData)->map(function($summary) use ($filters, $instance) {
                if (self::filterLawSuits($filters, $summary, $instance)) {
                    return $summary;
                }
            })
            ->filter()
            ->values()
            ->toArray();
        }

        return [collect($summaryData)->map(function($summary) use ($instance, &$values, $sourceId) {
            $lawSuitKey = self::getLawSuitKeyByInstance($instance);
            $lawSuitId['sourceId'] = $sourceId;
            $valueCauseKey = self::getValueCauseKeyByInstance($instance);
            foreach ($lawSuitKey as $key => $value) {
                if (Arr::get($summary, $value) == 'nao_informado' || !Arr::has($summary,[$value])) {
                    Arr::set($summary, $value, 'Não Informado');
                }
                
                if ($key == 'valor') {
                    $valorCause = Arr::get($summary, $valueCauseKey[1], 0);
                    $moedaValorCause = Arr::get($summary, $valueCauseKey[0], 0);
                    $valorCause = self::sumValues($valorCause);
                    $values[]=[
                        'moeda_valor_causa' => $moedaValorCause,
                        'valor_causa' => $valorCause
                    ];
                    $summary[$value] = $valorCause == '0,00' ? '-' : Arr::get($summary, $valueCauseKey[0], 'R$')." {$valorCause}";
                }

                if ($key == 'num') {
                    Arr::set(
                        $summary,
                        $value,
                        static::formatNumProcess(
                            Arr::get(
                                $summary,
                                $value
                            )
                        )
                    );
                }
                
                Arr::set(
                    $lawSuitId,
                    $key,
                    Arr::get($summary, $value)
                );
            }


            return $lawSuitId;
        })->toArray(), $values];
    }
    public static function formatNumProcess($num)
    {
        return $num;
    }

    public static function sumValues($values)
    {
        $num = (float)$values;
        return number_format($num, 2, ',', '.');
    }
    private static function getLawSuitsDetails(array $summaryData, int $instance, array $filter, int $sourceId): array
    {
        $data = self::getLawSuitsIds($summaryData, $instance, $filter, $sourceId);
        $amount  = TransformSourceSummaryTypeJuridico::sumValues($data[1]);
        if ($amount[0] === 'Não Informado') {
            $amount = 'R$ 0';
        }
        return [
            'amount' => $amount,
            'data' => $data[0]
        ];

    }

    private static function getCourtKeyByInstance(int $instance): string
    {
        return Arr::first(Arr::get(self::COURT_KEYS, 'instance' . $instance));
    }

    private static function getStatusKeyByInstance(int $instance): string
    {
        return Arr::first(Arr::get(self::STATUS_KEYS, 'instance' . $instance));
    }

    private static function getAmountKeyByInstance(int $instance): string
    {
        return Arr::first(Arr::get(self::AMOUNT_KEYS, 'instance' . $instance));
    }

    private static function getCoinKeyByInstance(int $instance): string
    {
        return Arr::first(Arr::get(self::COIN_KEYS, 'instance' . $instance));
    }

    private static function getLawSuitKeyByInstance(int $instance): array
    {
        return Arr::get(self::LAWSUIT_KEYS, 'instance' . $instance);
    }

    private static function getValueCauseKeyByInstance(int $instance): array
    {
        return Arr::get(self::VALUE_CAUSE_KEY, 'instance' . $instance);
    }
    private static function getParteKeyByInstance(int $instance): string
    {
        return Arr::first(Arr::get(self::PARTIES_KEYS, 'instance' . $instance));
    }

    private static function getStateKeyByInstance(int $instance): string
    {
        return Arr::first(Arr::get(self::STATE_KEYS, 'instance' . $instance));
    }
}
