<?php

namespace App\Domains\Dossie\Transforms;

use App\Domains\Dossie\Interface\Transformable;

class ParameterSourceTransform implements Transformable
{
    public function handle($data, $fields):array
    {
        $result = [];

        $data->each(function ($parametro) use (&$result, $fields) {
            $result['parametro'][$parametro->nome] = [];

            $parametro->details->each(function ($detalhe) use ($fields, &$result, $parametro) {
                $result['parametro'][$parametro->nome][] = $detalhe->only($fields);
            });
        });

        return $result;
    }
}
