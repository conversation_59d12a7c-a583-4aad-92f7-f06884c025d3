<?php

namespace App\Domains\Dossie\Transforms;

use App\Domains\Dossie\Actions\Dossie\List\GetCriterionNameAction;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use Illuminate\Database\Eloquent\Collection;
use App\Domains\Dossie\Transforms\TransformDocumento;
use App\Domains\Dossie\Transforms\TransformDossiesDerivado;
use App\Domains\Dossie\Entities\Client\LoteConsulta;
use App\Domains\Dossie\Helpers\SearchNameHelper;
use App\Domains\Dossie\Helpers\SearchHomonimoHelper;

class TransformCardInfo
{

    private TransformDocumento $transformDocumento;
    private TransformDossiesDerivado $transformOutrosDossies;
    private SearchNameHelper $searchNameHelper;
    private SearchHomonimoHelper $searchHomonimoHelper;

    public function __construct(
        SearchNameHelper         $searchNameHelper,
        SearchHomonimoHelper     $searchHomonimoHelper,
        TransformDocumento       $transformDocumento,
        TransformDossiesDerivado $transformOutrosDossies
    )
    {
        $this->transformDocumento = $transformDocumento;
        $this->searchNameHelper = $searchNameHelper;
        $this->searchHomonimoHelper = $searchHomonimoHelper;
        $this->transformOutrosDossies = $transformOutrosDossies;
    }

    public function cardInfoWithSources(
        PesquisaConsulta $dossie,
        LoteConsulta     $lote,
        array            $fontesComResultado,
        array            $fontesSemResultado,
        array            $fontesIndisponiveis,
        array            $masterLogo
    ): array
    {
        $data = $this->cardInfo(
            $dossie,
            $lote,
            $masterLogo
        );
        $data["fontesComResultado"] = [
            "amount" => count($fontesComResultado),
            "fontes" => $fontesComResultado
        ];
        $data["fontesSemResultado"] = [
            "amount" => count($fontesSemResultado),
            "fontes" => $fontesSemResultado
        ];
        $data["fontesIndisponiveis"] = [
            "amount" => count($fontesIndisponiveis),
            "fontes" => $fontesIndisponiveis
        ];
        return $data;
    }

    public function cardInfo(
        PesquisaConsulta $dossie,
        LoteConsulta     $lote,
        array            $masterLogo
    ): array
    {
        $name = app(GetCriterionNameAction::class)
            ->execute($dossie->criterio, $dossie->id);

        $homonimo = $this->searchHomonimoHelper->mount(
            $name,
            $lote?->perfil?->tipo_perfil == 1 ? 'PF' : 'PJ'
        );
        $derivados = empty($dossie->derivados) ?
            [] :
            $this->transformOutrosDossies->handle($dossie->derivados, $dossie->id);
        return [
            "loteID" => $lote->id,
            "tag" => $lote->tag,
            "dossieID" => $dossie->id,
            "apelido" => $dossie?->apelido ?? '',
            "perfil" => !empty($lote?->perfil?->nome) ? $lote?->perfil?->nome : $lote?->perfilDefault?->nome,
            'nome' => $name,
            'homonimo' => $homonimo,
            'dossie_versao' => $dossie->dossie_versao,
            'dossie_convertido' => $dossie->dossie_convertido,
            "criterio" => $this->transformDocumento->handle(
                explode(
                    '|',
                    $dossie->criterio
                )[0]
            ),
            "responsavel" => "{$lote->usuario->nome} {$lote->usuario->sobrenome}",
            "createdAt" => $dossie->data_criacao->format('d/m/Y H:i:s'),
            "updatedAt" => $dossie->data_estado->format('d/m/Y H:i:s'),
            "logo" => $masterLogo['logo'],
            "dossiePaiID" => empty($lote->sub_of) ? '' : $lote->sub_of,
            "dossiesIrmaos" => empty($lote->dossieIrmaos) ? [] : $this->transformOutrosDossies->handle($lote->dossieIrmaos, $dossie->id),
            "amountDerivados" => count($derivados),
            "derivados" => $derivados
            ,
            'visible' => true,
        ];
    }
}
