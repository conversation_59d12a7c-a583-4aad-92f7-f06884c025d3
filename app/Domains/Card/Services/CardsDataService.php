<?php

namespace App\Domains\Card\Services;

use App\Domains\Aplicativo\Services\AplicativoService;
use App\Domains\Dossie\Services\DossieService;
use App\Domains\Dossie\Services\PdfRequestService;
use App\Domains\Dossie\Services\PesquisaConsultaService;
use App\Domains\SummaryCompany\Services\SummaryCompanyService;
use App\Domains\Reaction\Services\ReactionsService;
use DossieSourcesModule\Sources\Services\Cliente\ResultServicesService;
use App\Domains\Tag\Services\TagDossieService;
use App\Domains\UpFlag\Services\UpFlagService;
use App\Domains\User\Services\UserGroupService;
use App\Mocks\ResultControllerMockedData;

class CardsDataService
{
    private DossieService $dossieService;
    private SummaryCompanyService $summaryCompanyService;
    private AplicativoService $aplicativoService;
    private ResultServicesService $resultServicesService;
    private TagDossieService $tagService;
    private ReactionsService $reactionsService;
    private UpFlagService $upFlagService;
    private UserGroupService $userGroupService;
    private PdfRequestService $pdfRequestService;

    public function __construct(
        private readonly PesquisaConsultaService $pesquisaConsultaService,
        DossieService         $dossieService,
        SummaryCompanyService $summaryCompanyService,
        AplicativoService     $aplicativoService,
        ResultServicesService $resultServicesService,
        TagDossieService            $tagService,
        ReactionsService      $reactionsService,
        UpFlagService         $upFlagService,
        UserGroupService      $userGroupService,
        PdfRequestService    $pdfRequestService
    )
    {
        $this->dossieService = $dossieService;
        $this->summaryCompanyService = $summaryCompanyService;
        $this->aplicativoService = $aplicativoService;
        $this->resultServicesService = $resultServicesService;
        $this->tagService = $tagService;
        $this->userGroupService = $userGroupService;
        $this->reactionsService = $reactionsService;
        $this->upFlagService = $upFlagService;
        $this->pdfRequestService = $pdfRequestService;

    }

    public function getCardsData(int $dossieID): array
    {
        $cardInfo = $this->dossieService->getCardInfo($dossieID);
        $otherApps = $this->aplicativoService->getOtherApps($dossieID);

        $fontes = $this->pesquisaConsultaService->grupoFontes($dossieID);

        $cardTags = $this->tagService->getCardTags($dossieID);
        $reactions = $this->reactionsService->getReactions();
        $summary = $this->summaryCompanyService->getSummaryCompany($dossieID);
        $upFlag = $this->upFlagService->getByID($dossieID);
        $users = $this->userGroupService->getUserAndOthersUsersGroup();
        $data = ResultControllerMockedData::getDossieCardsData();
        $statusPdf = $this->pdfRequestService->findById(['dossie_id'=>$dossieID]);
        $data['status_pdf'] = $statusPdf['status_pdf'];
        $data['info'] = $cardInfo;
        $data['otherApps'] = $otherApps;
        $data['grupoFontes'] = $fontes;
        $data['dossieTags'] = $cardTags;
        $data['companySummary'] = $summary;
        $data['reactions'] = $reactions;
        $data['upflag'] = $upFlag;
        $data['tags'] = $this->tagService->getTags($dossieID);
        $data['comentarios']['users'] = $users;
        return $data;
    }
}
