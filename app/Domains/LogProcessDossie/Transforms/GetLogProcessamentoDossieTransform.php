<?php

namespace App\Domains\LogProcessDossie\Transforms;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Dossie\Enums\DossieStatusEnum;
use App\Domains\Dossie\Helpers\StatusHelper;
use App\Domains\LogProcessDossie\Enums\StatusEnum;

class GetLogProcessamentoDossieTransform
{
    private const PDF_SOURCE_URL = '/api/dossie/pdf/source/{id}?id_dossie={idDossier}&company={company}';

    /**
     * @param PesquisaConsulta $dossie
     * @param array $logs
     * @param array $sourcesNames
     * @param bool $lastProcess - se true, retorna apenas o último com todas as fontes
     * @param bool $onlyProgress - retorna apenas o progresso
     * @return int|array
     */
    public function handle(
        PesquisaConsulta $dossie, array $logs, array $sourcesNames, bool $lastProcess, bool $onlyProgress = false
    ): int|array
    {
        if (array_reduce($logs, fn($carry, $item) => $carry && empty($item['detalhes']), true)) {
            return $this->checkBatchIsProcessing($dossie);
        }

        $childrens = $dossie->getChildrens->toArray();
        $childrens[] = $dossie->toArray();

        // Contar fontes a partir da tabela pesquisa_consulta pois pode ter fontes dependentes
        $sourcesIds = $dossie->getChildrens->pluck('captura')->toArray();

        $sourcesIds[] = $dossie->captura;
        $sourcesCount = count($sourcesIds);

        if ($lastProcess) {
            return $this->onlyLastProcessWithAllSources(
                $dossie,
                $logs,
                $childrens,
                $sourcesNames,
                $sourcesCount,
                $onlyProgress
            );
        }

        $session = (object)session()->all();

        $process = 0;
        $finalLog = [];

        // Iterar sobre o log recuperado do elasticsearch e filtrado para o dossiê e seus filhos
        foreach ($logs as $log) {
            $process++;
            $response = [
                'batch_id' => $dossie->id_lote_consulta,
                'dossier_id' => $dossie->id,
                'process' => $process,
                'progress' => 0,
                'sources' => []
            ];

            $detailsCount = 0;

            // Recuperar detalhes das fontes processadas e calcular o progresso
            foreach ($log['detalhes'] as $detalhe) {
                try {
                    $fonte = $sourcesNames[$detalhe['captura']]['nome'];
                    $status = StatusEnum::from($detalhe['status'])->name;
                    $link = null;

                    if ($status != StatusEnum::processing->name) {
                        $detailsCount++;
                    }

                    if (!$this->isErrorOrAlert($status)) {
                        $link = $this->mountPdfSourceLink($detalhe['captura'], $response['dossier_id'], $session->company);
                    }

                    $response['sources'][] = [
                        'id' => $detalhe['captura'],
                        'source' => $fonte,
                        'criterion' => $detalhe['criterio'],
                        'status' => $status,
                        'message' => $detalhe['msg'] ?? '',
                        'link' => $link
                    ];
                } catch (\Throwable $e) {
                    return [];
                }
            }

            // Tratativas para registros que podem não ter detalhes no elastic
            if (empty($log['detalhes']) && $detailsCount == 0) {
                $detailsCount = $sourcesCount;
            }

            $response['progress'] = floor(($detailsCount / $sourcesCount) * 100);
            $finalLog[] = $response;
        }

        $finalLog = array_reverse($finalLog);

        // Retornar progresso do último processamento
        if ($onlyProgress) {
            return $finalLog[0]['progress'] ?? 0;
        }

        return $finalLog;
    }

    private function mountPdfSourceLink(int $idSource, int $idDossier, string $company): string
    {
        $url = str_replace('{id}', $idSource, self::PDF_SOURCE_URL);
        $url = str_replace('{idDossier}', $idDossier, $url);
        $url = str_replace('{company}', $company, $url);

        return env('EXPORTACAO_PDF_URL') . $url;
    }

    private function isErrorOrAlert(string $status): bool
    {
        return $status == StatusEnum::error->name || $status == StatusEnum::warning->name;
    }

    /**
     * Retorna apenas o processamento atual com todas as fontes que já podem ter retornado com sucesso
     * Em processamentos anteriores.
     *
     * @param PesquisaConsulta $dossie
     * @param array $logs
     * @param array $childrens
     * @param array $sourcesNames
     * @param int $sourcesCount
     * @param bool $onlyProgress - Retornas apenas o progresso
     * @return int|array
     */
    private function onlyLastProcessWithAllSources(
        PesquisaConsulta $dossie,
        array $logs,
        array $childrens,
        array $sourcesNames,
        int $sourcesCount,
        bool $onlyProgress = false

    ): int|array
    {
        $logChildrens = [];
        foreach ($logs as $log) {
            foreach($log['detalhes'] as $detalhe) {
                $logChildrens[$detalhe['dossie']] = [
                    'dossie' => $detalhe['dossie'],
                    'cliente' => $detalhe['cliente'],
                    'fonte' => $detalhe['captura'],
                    'criterio' => $detalhe['criterio'],
                    'descricao' => $detalhe['descricao'],
                    'status' => $detalhe['status'],
                    'msg' => $detalhe['msg'] ?? null
                ];
            }
        }

        $sourcesLog = [];
        $detailsCount = 0;
        foreach($childrens as $children) {
            if (!empty($logChildrens[$children['id']])) {
                $logChildren = $logChildrens[$children['id']];
                $status = $this->checkStatusByDossieAndElastic($children, $logChildren);

                if (!$this->isErrorOrAlert($status)) {
                    $link = $this->mountPdfSourceLink(
                        $logChildren['fonte'],
                        $dossie->id,
                        $logChildren['cliente']
                    );
                }

                if ($status != StatusEnum::processing->name) {
                    $detailsCount++;
                }

                $sourcesLog[] = [
                    'id' => $children['id'],
                    'source' => $logChildren['fonte'],
                    'source_name' => $sourcesNames[$logChildren['fonte']]['nome'],
                    'criterion' => $logChildren['criterio'],
                    'status' => $status,
                    'message' => $logChildren['msg'] ?? '',
                    'description' => $logChildren['descricao'],
                    'metadata' => $this->mountMetaData($logChildren),
                    'link' => $link ?? null
                ];
            }
        }

        // Garantir que logs diferente de "success" estejam sempre no topo.
        usort(
            $sourcesLog,
            fn($a, $b) => $a['status'] === StatusEnum::success->name && $b['status'] !== StatusEnum::success->name
        );

        // Recupera o status antigo do dossiê
        $estadoDossie = StatusHelper::getStatus($dossie);
        $status = StatusHelper::getStatusNewByOld($dossie, $estadoDossie);
        $progress = $status == 'queue' ? 0 : floor(($detailsCount / $sourcesCount) * 100);
        $sources = $status == 'queue' ? [] : $sourcesLog;

        $logs = [
            'batch_id' => $dossie->id_lote_consulta,
            'dossier_id' => $dossie->id,
            'status' => $status,
            'process' => count($logs),
            'progress' => $progress,
            'sources' => $sources
        ];

        if ($onlyProgress) {
            return [
                'progress' => $progress,
                'status' => $status
            ];
        }

        return $logs;
    }

    private function checkStatusByDossieAndElastic(array $dossie, array $log): string
    {
        if ($dossie['estado'] == 4) {
            return StatusEnum::success->name;
        }

        return StatusEnum::from($log['status'])->name;
    }

    private function checkBatchIsProcessing(PesquisaConsulta $dossie): array
    {
        if ($dossie->lote->estado == 2) {
            return ['status' => StatusEnum::processing->name];
        }

        if ($dossie->estado == 1 && $dossie->status == DossieStatusEnum::QUEUE->value) {
            return ['status' => DossieStatusEnum::QUEUE->value];
        }

        $status = $dossie->status ?? StatusHelper::getStatusNewByOld($dossie, $dossie->estado);
        return ['status' => $status];
    }

    private function mountMetadata(array $logs): array
    {
        $descricao = $logs['descricao'] ?? null;

        if (!$descricao) {
            return [];
        }

        $patterns = [
            'details' => '/^[^\[]*\[[^:]*\:[^\]]*\]\s*[^-]*-\s*(.*?)$/i',
            'more' => '/\[([^\]]*)\]/i'
        ];

        $results = [];
        if (preg_match($patterns['details'], $logs['descricao'], $matches)) {
            $results['details'] = $matches[1];
        }

        if (preg_match_all($patterns['more'], $logs['descricao'], $matches)) {
            if (count($matches) > 1) {
                unset($matches[0]);
                sort($matches);
            }

            $more = [];
            $matches = $matches[0];
            foreach($matches as $match) {
                if (str_contains($match, 'Critério')) {
                    continue;
                }
                $match = str_replace('[', '', $match);
                $match = str_replace(']', '', $match);
                $more[] = $match;
            }

            $results['more'] = $more;
        }

        return $results;
    }
}
