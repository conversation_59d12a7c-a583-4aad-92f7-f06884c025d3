<?php

namespace App\Domains\UpFlag\Services;

use App\Domains\UpFlag\Helpers\MapUpFlagHelper;
use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;
use App\Domains\UpFlag\Repositories\UpFlagDossiesRepository;
use App\Domains\UpFlag\Transforms\TransformUpFlagDossie;

class UpFlagService
{
    private PesquisaConsultaRepository $pesquisaConsultaRepository;
    private UpFlagDossiesRepository $upFlagDossiesRepository;
    private MapUpFlagHelper $mapUpFlagHelper;
    private TransformUpFlagDossie $transformUpFlagDossie;

    public function __construct(
        PesquisaConsultaRepository $pesquisaConsultaRepository,
        UpFlagDossiesRepository $upFlagDossiesRepository,
        TransformUpFlagDossie $transformUpFlagDossie,
        MapUpFlagHelper $mapUpFlagHelper
    ) {
        $this->pesquisaConsultaRepository = $pesquisaConsultaRepository;
        $this->upFlagDossiesRepository = $upFlagDossiesRepository;
        $this->mapUpFlagHelper = $mapUpFlagHelper;
        $this->transformUpFlagDossie = $transformUpFlagDossie;
    }

    public function getByID(int $dossieID): array
    {
        $dossie =$this->pesquisaConsultaRepository->getByID($dossieID);
        $criterio = $this->mapUpFlagHelper->getCriterio($dossie->criterio);

        if (empty($criterio)) {
            return $this->transformUpFlagDossie->handle([]);
        }

        $upFlag = $this->upFlagDossiesRepository->getNameReasonByIds(
            session('setup.id_master'),
            $dossie->id_lote_consulta,
            $criterio
        )?->toArray();

        return $this->transformUpFlagDossie->handle($upFlag);
    }
}
