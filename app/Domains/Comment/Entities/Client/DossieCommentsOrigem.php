<?php

declare(strict_types=1);

namespace App\Domains\Comment\Entities\Client;

use Illuminate\Database\Eloquent\Model;
use App\Domains\Comment\Entities\Client\DossieComments;

class DossieCommentsOrigem extends Model
{
    protected $table = 'dossie_comments_origem';

    protected $fillable = [
        'captura_id',
        'dossie_comments_id',
        'dossie_id',
        'workflow_id',
        'version_workflow_id',
        'updated_at'
    ];

    public function origem()
    {
        return $this->hasMany(
            DossieComments::class,
            'dossie_comments_id',
            'id'
        );
    }
}
