<?php

declare(strict_types=1);

namespace App\Domains\Comment\Entities\Client;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Domains\User\Entities\User;

class DossieCommentReactions extends Model
{
    protected $table = 'dossie_comment_reactions';

    protected $fillable = [
        'user_id',
        'dossie_comments_id',
        'reaction_id'
    ];
    protected $with = ['user'];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->select(
            'id',
            'nome',
            'email',
            'sobrenome',
            'imagem_perfil'
        );
    }
}
