<?php

declare(strict_types=1);

namespace App\Domains\Comment\Entities\Client;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use App\Domains\User\Entities\User;
use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use App\Domains\Comment\Entities\Client\DossieCommentReactions;
use App\Domains\Comment\Entities\Client\DossieCommentEditingHistory;
use App\Domains\Comment\Entities\Client\DossieCommentMarkedUsers;
use DateTimeInterface;

class DossieComments extends Model
{
    const AUTO_USER = 1;
    protected $table = 'dossie_comments';

    protected $fillable = [
        'user_id',
        'dossie_id',
        'comment',
        'dossie_comments_id',
        'dossie_comments_edit',
        'comments_edited_log_id',
        'is_relevante',
        'updated_at',
        'created_at',
    ];
    protected $attributes = [
        'dossie_comments_edit' => false,
    ];

    protected function serializeDate(DateTimeInterface $date): string
    {
        return $date->format('d/m/Y H:i:s');
    }
    protected $with = ['commentOfComments', 'user', 'reactions'];

    public function marked()
    {
        return $this->hasMany(
            DossieCommentMarkedUsers::class,
            'dossie_comments_id',
            'id'
        );
    }

    public function commentOfComments()
    {
        return $this->hasMany(
            DossieComments::class,
            'dossie_comments_id',
            'id'
        )
            ->withCount('commentToLog')
            ->orderBy('created_at', 'desc');
    }

    public function commentToLog()
    {
        return $this->hasMany(
            DossieComments::class,
            'comments_edited_log_id',
            'id'
        );
    }

    public function commentEdited()
    {
        return $this->hasMany(
            DossieComments::class,
            'comments_edited__log_id',
            'id'
        )->orderBy('id', 'desc');
    }

    public function relevante()
    {
        return $this->hasMany(
            DossieCommentsSettings::class,
            'user_id',
            'user_id'
        );
    }

    public function scopeIsRelevante(Builder $query, bool $relevance): void
    {
        $query->when($relevance, function ($q) use ($relevance) {
            $q->where('is_relevante', $relevance);
        });
    }

    public function dossie(): BelongsTo
    {
        return $this->belongsTo(PesquisaConsulta::class, 'dossie_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id', 'id')
            ->select(
                'id',
                'nome',
                'email',
                'sobrenome',
                'imagem_perfil'
            );
    }

    public function reactions(): HasMany
    {
        return $this->hasMany(DossieCommentReactions::class, 'dossie_comments_id', 'id');
    }

    public function commentsDossie()
    {
        return $this->hasMany(
            DossieCommentsOrigem::class,
            'dossie_comments_id',
            'id'
        );
    }

    public function scopeDossier(Builder $query, int $dossieID): void
    {
        $query->where('dossie_id', $dossieID)
            ->whereHas('commentsDossie', function ($q) use ($dossieID) {
                $q->whereNull('captura_id')
                    ->whereNull('workflow_id')
                    ->whereNull('version_workflow_id')
                    ->where('dossie_id', $dossieID);
            });
    }

    public function scopeWorkflow(Builder $query, int $dossieID, int $workflowID, int $version): void
    {
        $query->where('dossie_id', $dossieID)
            ->whereHas('commentsDossie', function ($q) use ($workflowID, $version) {
                $q->whereNull('captura_id')
                    ->where('workflow_id', $workflowID)
                    ->where('version_workflow_id', $version)
                    ->whereNull('dossie_id');
            });
    }

    public function scopeSource(Builder $query, int $dossieID, int $sourceID): void
    {
        $query->where('dossie_id', $dossieID)
            ->whereHas('commentsDossie', function ($q) use ($sourceID) {
                $q->where('captura_id', $sourceID)
                    ->whereNull('workflow_id')
                    ->whereNull('version_workflow_id')
                    ->whereNull('dossie_id');
            });
    }
}
