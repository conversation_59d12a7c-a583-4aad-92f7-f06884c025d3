<?php

namespace App\Domains\Comment\Transforms;

use App\Domains\Comment\Helpers\EmailInAliasHelper;
use Illuminate\Support\Arr;
use App\Domains\Comment\Interfaces\TransformCommentsInterface;
use Illuminate\Support\Facades\Auth;

abstract class AbstractTransformComments implements TransformCommentsInterface
{
    public string $nameAliasUser;

    public function handle(array $data, bool $relevance = false): array
    {
        $result = [];

        foreach ($data as $value) {
            $data = $this->mountComentario($value, $relevance);
            if ($data) {
                $result[] = $data;
            }
        }
        return $result;
    }

    public function mountAlias(string $email): string
    {
        return EmailInAliasHelper::getAlias($email);
    }

    public function reactions(array $reactions): array
    {
        $mapReactions = [
            'my' => [],
            'reactions' => []
        ];
        foreach ($reactions as $value) {
            $mapReactions['reactions'][$value['reaction_id']][] = $this->mountAlias(
                $value['user']['email']
            );

            if ($value['user_id'] == session('setup.id_usuario')) {
                $mapReactions['my'][] = $value['reaction_id'];
            }
        }
        return $mapReactions;
    }

    public function mountCommentsReplay(array $comments, bool $relevance = false): array
    {
        $map = [];
        foreach ($comments as $value) {
            $data = $this->mountComentario($value, $relevance);
            if ($data) {
                $map [] = $data;
            }
        }
        return $map;
    }

    public function mountComentario(array $value, bool $relevance = false): ?array
    {
        if ($relevance === true && $value['is_relevante'] !== $relevance) {
            return null;
        }
        $reactions = $this->reactions($value['reactions']);
        //aqui não é
        $commentToEdit = Arr::get($value, 'comment_to_log_count', $value['dossie_comments_edit'] ?? []);
        return [
            $this->nameAliasUser => $this->mountAlias($value['user']['email']),
            'comment_id' => $value['id'],
            'my_comment' => $value['user_id'] == session('setup.id_usuario'),
            'creator' => $value['user']['nome'] . ' ' . $value['user']['sobrenome'],
            'is_relevante' => $value['is_relevante'],
            'comentario' => $value['comment'],
            'edited' => (bool)$commentToEdit,
            'data' => $value['created_at'],
            'myReactions' => $reactions['my'],
            'reactions' => $reactions['reactions'],
            'commentsReplay' => $this->mountCommentsReplay($value['comment_of_comments'], $relevance)
        ];
    }
}
