<?php

namespace App\Domains\Aplicativo\Services;

class PermissionService
{
    public function getAppsPermission(): array
    {
        $session = (object) session()->all();

        $utilitarios = (array) $session->contents['aplicativos']['utilitarios'];
        $utilitarios_master = [];
        if (!empty($session->contents['aplicativos']['utilitarios_master'])) {
            $utilitarios_master = (array) $session->contents['aplicativos']['utilitarios_master'];
        }
        $userData['utilitarios'] = array_values($utilitarios);
        $userData['utilitarios_master'] = array_values($utilitarios_master);

        return $userData;
    }

    public function getMonitoriaPermission(): array
    {
        $apps =  $this->getAppsPermission();

        $usuarioPossuiPermissao = in_array('Monitoria de Dossie', $apps['utilitarios']);
        $masterPossuiPermissao = in_array('Monitoria de Dossie', $apps['utilitarios_master']);

        return [
            'userHasPermission' => $usuarioPossuiPermissao,
            'masterHasPermission' => $masterPossuiPermissao
        ];
    }
}
