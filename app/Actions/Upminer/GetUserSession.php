<?php

namespace App\Actions\Upminer;

use App\Actions\Clients\Upminer\SessionClient;
use App\DTOs\Upminer\Session;

class GetUserSession
{
    public function execute(string $token): Session
    {
        // this can be used to test the APIs Locally
        if (env('APP_ENV') === 'testlocal') {
            $data = [
                'data' => json_decode(
                    file_get_contents(storage_path('/app/private/session-master-uplexis.json')),
                    true
                )
            ];
            return Session::fromResponse( $data );
        } else {
            $client = app(SessionClient::class, compact('token'));

            return $client->getSession();
        }
    }
}
