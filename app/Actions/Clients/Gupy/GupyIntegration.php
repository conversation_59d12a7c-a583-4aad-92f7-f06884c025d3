<?php

namespace App\Actions\Clients\Gupy;

use App\Domains\Dossie\Repositories\PesquisaConsultaRepository;
use App\Domains\Workflow\Repositories\DossieGupyRepository;
use Exception;
use App\Exceptions\GupyException;
use GuzzleHttp\Client;
use Illuminate\Session\SessionManager;

class GupyIntegration
{
    private Client $client;
    private PesquisaConsultaRepository $pesquisaConsultaRepository;
    private DossieGupyRepository $dossieGupyRepository;
    protected $session;
    private static $gupyIntegratedTenants = [
        'cba5247a',
    ];


    public function __construct(
        PesquisaConsultaRepository $pesquisaConsultaRepository,
        DossieGupyRepository $dossieGupyRepository,
        SessionManager $session
    ) {

        $this->session = $session;
        $this->client = new Client([
            'base_uri' => env('GUPY_INTEGRATION'),
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ]
        ]);
        $this->pesquisaConsultaRepository = $pesquisaConsultaRepository;
        $this->dossieGupyRepository = $dossieGupyRepository;
    }

    public function sendWorkflow(
        int $dossieID,
        string $status,
        int $clientID,
        string $comentario
    ) {

        if($this->haveGupyIntegration()){
            $dossieLotePerfil = $this->pesquisaConsultaRepository->getDossieLotePerfilByID($dossieID);

            $dossie = $this->dossieGupyRepository->getByClientIDLoteID(
                //$clientID,
                $dossieLotePerfil->id_lote_consulta
            );

            if (!empty($dossie)) {
                $loteID = $dossieLotePerfil->id_lote_consulta;
                $tag = $this->getTag($status);
                $sendResponse = $this->send($loteID, $status, $clientID, $comentario, $tag);

                if ($sendResponse['status'] === 'success') {
                    return true;
                }

                return false;
            }

            return false;
        }
    }

    public function haveGupyIntegration()
    {
        $tenant = $this->session->get('company');
        return in_array($tenant, self::$gupyIntegratedTenants);
    }

    public function register(int $clientId)
    {
        try {
            $response = $this->client->post("/api/webhook/$clientId/register");

            return json_decode($response->getBody()->getContents(), JSON_OBJECT_AS_ARRAY);
        } catch (Exception $e) {
            throw new GupyException(trans('gupy.error'));
        }
    }

    private function send(
        int $loteID,
        string $status,
        int $clientID,
        string $comment,
        string $tag
    ) {
        try {
            $response = $this->client->post('/api/workflow', [
                'body' => json_encode([
                    'lote_id' => $loteID,
                    'client_id' => $clientID,
                    'tag' => $tag,
                    'comment' => $comment,
                    'status' => $status
                ])
            ]);

            $response = json_decode($response->getBody()->getContents(), JSON_OBJECT_AS_ARRAY);
            return $response;
        } catch (Exception $e) {
            throw new GupyException(trans('gupy.error'));
        }
    }

    private function getTag($status)
    {
        return $status == 'reprovado' ? 'NÃO RECOMENDADO' : 'RECOMENDADO';
    }
}
