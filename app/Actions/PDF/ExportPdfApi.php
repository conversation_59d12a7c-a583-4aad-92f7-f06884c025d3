<?php

namespace App\Actions\PDF;

use Exception;
use GuzzleHttp\Client;
use App\Actions\PDF\Helpers\SessionHelper;
use App\Actions\PDF\Transforms\TransformBodyPdf;
use App\Exceptions\ErrorRequestGeneretePdf;

class ExportPdfApi
{
    private Client $client;
    private SessionHelper $sessionHelper;
    private TransformBodyPdf $transformBodyPdf;

    public function __construct(
        SessionHelper $sessionHelper,
        TransformBodyPdf $transformBodyPdf
    ) {
        $this->client = new Client([
            'base_uri' => env('EXPORTACAO_PDF_URL'),
        ]);

        $this->sessionHelper = $sessionHelper;
        $this->transformBodyPdf = $transformBodyPdf;
    }

    public function sendRequestExport(array $params, bool $details)
    {
        $session = session()->all();
        $session = $this->sessionHelper->mount($session, $params['id_dossie']);
        $data = $this->transformBodyPdf->handle(
            $params,
            $session,
            $details
        );

        return $this->send($data);
    }

    private function send(array $data)
    {
        try {
            $token = session('id');

            $response = $this->client->post('/api/dossie/pdf/pdf-request', [
                'headers' => [
                    'Authorization' => "{$token}",
                    'Origin' => env('APP_URL'),
                    'Content-Type' => 'application/json'
                ],
                'json' => $data
            ]);

            $response = json_decode($response->getBody()->getContents(), JSON_OBJECT_AS_ARRAY);
            return $response;
        } catch (Exception $e) {
            throw new ErrorRequestGeneretePdf('Error request pdf', 0);
        }
    }

    public function sendRequestLawsuits(array $params, bool $details)
    {
        $session = session()->all();
        $session = $this->sessionHelper->mount($session, $params['id_dossie']);
        $data = $this->transformBodyPdf->handle(
            $params,
            $session,
            $details
        );

        return $this->sendLawSuits($data);
    }

    private function sendLawSuits(array $data)
    {
        try {
            $token = session('id');

            $response = $this->client->post('/api/dossie/pdf/lawsuits-request', [
                'headers' => [
                    'Authorization' => "{$token}",
                    'Origin' => env('APP_URL'),
                    'Content-Type' => 'application/json'
                ],
                'json' => $data
            ]);

            $response = json_decode($response->getBody()->getContents(), JSON_OBJECT_AS_ARRAY);
            return $response;
        } catch (Exception $e) {
            throw $e;
            throw new ErrorRequestGeneretePdf('Error request pdf', 0);
        }
    }
}
