<?php

namespace App\Logging;

use Monolog\Logger;
use Monolog\Handler\StreamHandler;
use GuzzleHttp\Client;

class SigNozLogger
{
    public function __invoke(array $config)
    {

        if (
            // esses ambiantes ainda n tem acesso a url do signoz
            !in_array(
                env('APP_ENV'),
                ['qa', 'hml', 'prod']
            )
        ) {
            // Cria o logger
            $logger = new Logger('signoz');

            // Adiciona um handler local opcional
            $localHandler = new StreamHandler(storage_path('logs/signoz.log'), Logger::DEBUG);
            $logger->pushHandler($localHandler);

            // Cliente HTTP para enviar logs ao SigNoz
            $client = new Client();

            // Enviar os logs usando Guzzle (equivalente ao seu curl)
            $logger->pushProcessor(function ($record) use ($client) {
                try {
                    // Gera o payload no formato simples igual ao do seu curl
                    $payload = [
                        "resourceLogs" => [
                            [
                                "scopeLogs" => [
                                    [
                                        "logRecords" => [
                                            [
                                                "timeUnixNano" => (int)(microtime(true) * 1e9),
                                                "severityText" => $record['level_name'],
                                                "body" => [
                                                    "stringValue" => $record['message']
                                                ],
                                                "trace_id" => isset($record['context']['trace_id']) ? $record['context']['trace_id'] : 'no-trace-id',
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ];

                    // Envia o log para o SigNoz
                    $response = $client->post(env('OTEL_EXPORTER_OTLP_ENDPOINT') . '/v1/logs', [
                        'json' => $payload,
                        'headers' => [
                            'Content-Type' => 'application/json'
                        ]
                    ]);

                } catch (\Exception $e) {
                    error_log('Erro ao enviar log para o SigNoz: ' . $e->getMessage());
                }

                return $record;
            });

            return $logger;
        }
    }
}
