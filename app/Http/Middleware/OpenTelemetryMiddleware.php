<?php

namespace App\Http\Middleware;

use Closure;
use OpenTelemetry\SDK\Trace\TracerProvider;
use OpenTelemetry\SDK\Trace\SpanProcessor\BatchSpanProcessor;
use OpenTelemetry\Contrib\Otlp\SpanExporter;
use OpenTelemetry\SDK\Common\Export\Http\PsrTransportFactory;
use OpenTelemetry\SDK\Common\Time\SystemClock;
use Illuminate\Support\Facades\Log;
use Exception;

class OpenTelemetryMiddleware
{
    protected $tracer;
    protected $spanProcessor;
    protected $span;

    public function __construct()
    {
        $transportFactory = PsrTransportFactory::discover();
        $transport = $transportFactory->create(
            env('OTEL_EXPORTER_OTLP_ENDPOINT') . '/v1/traces',
            'application/x-protobuf'
        );

        $exporter = new SpanExporter($transport);
        $clock = new SystemClock();

        $this->spanProcessor = new BatchSpanProcessor($exporter, $clock);

        $tracerProvider = new TracerProvider($this->spanProcessor);

        $this->tracer = $tracerProvider->getTracer(env('OTEL_SERVICE_NAME'));
    }

    public function handle($request, Closure $next)
    {
        if (
            in_array(
                env('APP_ENV'),
                ['qa', 'hml', 'prod']
            )
        ) {
            // o ambiente de qa e hml ainda n tem liberado o acesso a maquina do sonar/signoz
            return $next($request);
        }
        // Inicia um span para a requisição
        $this->span = $this->tracer->spanBuilder('http_request')->startSpan();
        $scope = $this->span->activate();

        $session = (object) session()->all();

        $client = isset($session->company) ? $session->company : 'not informed';
        $userID = isset($session->setup['id_usuario']) ? $session->setup['id_usuario'] : 'not informed';
        // Adiciona atributos ao span
        $this->span->setAttribute('http.url', $request->fullUrl());
        $this->span->setAttribute('http.method', $request->method());
        $this->span->setAttribute('client.schema', $client);
        $this->span->setAttribute('user.id', $userID);

        // Captura parâmetros da URL ou do corpo da requisição
        if ($request->isMethod('get')) {
            $this->span->setAttribute('http.query_params', json_encode($request->query()));
        } else {
            $this->span->setAttribute('http.body_params', json_encode($request->all()));
        }

        try {
            $response = $next($request);
            $responseContent = $this->extractResponseContent($response);
            $this->span->setAttribute('http.status_code', $response->getStatusCode());
            $this->span->setAttribute('response.content', $responseContent);
        } catch (Exception $e) {
            $this->addSpanErrorData($e);
            throw $e;
        } finally {
            $scope->detach();
            $this->span->end();
            $this->spanProcessor->forceFlush();
        }

        return $response;
    }

    private function extractResponseContent($response) {
        try {
            if (!empty($response->exception) && (!$response->isOk() || !$response->isSuccessful())) {
                $this->addSpanErrorData($response->exception);
                return json_encode([
                    'error' => $response->exception->getMessage(),
                    'file' => $response->exception->getFile(),
                    'line' => $response->exception->getLine(),
                    'trace' => $response->exception->getTrace()
                ]);
            }
            return $response->getContent();
        } catch (Exception $e) {
            return $response;
        }
    }

    private function addSpanErrorData($e) {
        $errorMessage = $e->getMessage() . ' on file:' . $e->getFile() . ' line: ' . $e->getline();
        $this->span->setStatus(\OpenTelemetry\API\Trace\StatusCode::STATUS_ERROR);
        $this->span->setAttribute('error', true);
        $this->span->setAttribute('exception.errorMessage', $errorMessage);
        $this->span->setAttribute('exception.message', $e->getMessage());
        $this->span->setAttribute('exception.stacktrace', $e->getTraceAsString());

        Log::error('Application error: ' . $e->getMessage(), [
            'trace_id' => $this->span->getContext()->getTraceId(),
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'stack' => $e->getTraceAsString(),
        ]);
    }
}
