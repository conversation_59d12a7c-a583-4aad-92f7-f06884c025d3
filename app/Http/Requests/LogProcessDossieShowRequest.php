<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LogProcessDossieShowRequest extends FormRequest
{

    protected function prepareForValidation()
    {
        $this->merge([
            'dossieId' => $this->route('dossieId')
        ]);
    }

    public function rules()
    {
        return [
            'dossieId' => 'required|integer|min:1',
            'last' => 'nullable|in:true,false,0,1',
        ];
    }

    public function messages()
    {
        return [
            'dossieId.required' => 'O ID do dossiê é obrigatório',
            'dossieId.integer' => 'O ID do dossiê deve ser um número inteiro',
            'dossieId.min' => 'O ID do dossiê deve ser maior que zero'
        ];
    }
} 