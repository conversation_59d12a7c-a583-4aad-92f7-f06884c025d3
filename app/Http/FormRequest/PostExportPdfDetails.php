<?php

namespace App\Http\FormRequest;

use Illuminate\Foundation\Http\FormRequest;

class PostExportPdfDetails extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            "id_lote" => ['required', 'integer', 'exists:lote_consulta,id'],
            "id_dossie" => ['required', 'integer', 'exists:pesquisa_consulta,id'],
            "download"=> ['required', 'boolean'],
            "graficos_mapas"=> ['required', 'boolean'],
            "apenas_tags"=> ['sometimes', 'boolean'],
            "analyze" => [
                'required',
                'array:relevantes',
            ],
            "analyze.relevantes" => [
                'required',
                'boolean'
            ],
            "comentarios" => [
                'required',
                'array:mais_recentes,relevantes,workflow,fontes',
            ],
            "comentarios.mais_recentes" => ['required', 'boolean'],
            "comentarios.relevantes" => ['required', 'boolean'],
            "comentarios.workflow" => ['required', 'boolean'],
            "comentarios.fontes" => ['required', 'boolean'],
            "foxy_ia"=> ['required', 'boolean'],
            "categorias" => ['sometimes', 'array'],
            "categorias.*" => ['sometimes', 'string'],
            "anexos"=> ['required', 'boolean'],
            "titulo_pdf"=> ['sometimes', 'string', 'min:1'],
        ];
    }
}
