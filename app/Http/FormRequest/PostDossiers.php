<?php

namespace App\Http\FormRequest;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Rules\ValidateIdProfileExists;

class PostDossiers extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'file' => ['nullable', 'array:name',
            ],
            "file.name" => ['string'],
            'mode' => [
                'required',
                'string',
                Rule::in(['custom', 'default'])
            ],
            "ignoreDuplicityBlock" => ['boolean'],
            "tipo" => ['required', 'in:1,2'],
            "perfil" => [
                'required',
                'int',
                new ValidateIdProfileExists($this->input('mode')),
            ],
            "criterios" => ['required', 'array'],
            "parametros" => ['array'],
            "tag" => ['nullable','int', 'min:1', 'exists:tags,id'],
            "process" => ['nullable','boolean'],
        ];
    }
}
