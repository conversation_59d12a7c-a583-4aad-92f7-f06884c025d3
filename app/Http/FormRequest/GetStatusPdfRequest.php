<?php

namespace App\Http\FormRequest;

use App\Domains\Dossie\Entities\Client\PesquisaConsulta;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Domains\Dossie\Entities\Client\PdfRequestModel;

class GetStatusPdfRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'request_id' => [
                'integer',
                Rule::exists(PdfRequestModel::class, 'id')
            ],
            'dossie_id' => [
                'required',
                'integer',
                Rule::exists(PesquisaConsulta::class, 'id')
            ],
        ];
    }
}
