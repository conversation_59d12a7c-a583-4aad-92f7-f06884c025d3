<?php

namespace App\Http\FormRequest;

use App\Domains\User\Entities\Group;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetFilterUsersByGroup extends FormRequest
{

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            "name_or_email" => ['nullable', 'string', 'min:2', 'max: 50']
        ];
    }

    public function messages(): array
    {
        return [
            'name_or_email_min' => trans('userGroup.name_or_email.min'),
            'name_or_email_max' => trans('userGroup.name_or_email.max'),
        ];
    }
}
