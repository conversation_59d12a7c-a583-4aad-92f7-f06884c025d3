<?php

namespace App\Http\Controllers;

use App\Domains\Aplicativo\Services\PermissionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PermissionController extends Controller
{
    public function __construct(
        private PermissionService $permissionService
    ) {}

    public function getAppsPermission(): JsonResponse
    {
        $result = [
            'status' => 200,
            'data' => $this->permissionService->getAppsPermission()
        ];

        return response()->json($result, $result['status']);
    }

    public function getMonitoriaPermission(): JsonResponse
    {
        $result = [
            'status' => 200,
            'data' => $this->permissionService->getMonitoriaPermission()
        ];

        return response()->json($result, $result['status']);
    }
}
