<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use DossieSourcesModule\Sources\Services\Cliente\ResultServicesService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Requests\FromRequestSourceFactory;

class SourceController extends Controller
{
    private ResultServicesService $resultServicesService;

    public function __construct(ResultServicesService $resultServicesService)
    {
        $this->resultServicesService = $resultServicesService;
    }

    public function getSouceResultTest($sourceID): JsonResponse
    {
        $result = [
            'status' => 200,
            'data' => json_decode(
                file_get_contents(
                    storage_path() . '/app/private/mockedSourceResults/source' . $sourceID . '.json', true
                )
            ),
        ];

        return response()->json($result, $result['status']);
    }

    public function getSouceResult(int $dossieID, int $sourceID, Request $request): JsonResponse
    {
        $filters = FromRequestSourceFactory::make(
            $sourceID,
            $request
        );

        $result = [
            'status' => 200,
            'data' => $this->resultServicesService->getSourceResult([
                'sourceID' => $sourceID,
                'dossieID' => $dossieID,
                'filters' => $filters->validated(),
            ]),
        ];

        return response()->json($result, $result['status'], [], JSON_INVALID_UTF8_IGNORE);
    }
}
