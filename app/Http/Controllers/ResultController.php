<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Domains\Card\Services\CardsDataService;

use DossieSourcesModule\Sources\Services\Cliente\ReactionsService;
use App\Mocks\ResultControllerMockedData;
use Illuminate\Http\Response;
use App\Domains\Dossie\Services\DossieSourcesSummaryService;
use App\Domains\Dossie\Services\DossieService;
use App\Domains\Dossie\Services\PesquisaConsultaService;
use App\Domains\Aplicativo\Services\AplicativoService;

class ResultController extends Controller
{
    public function __construct(
        ReactionsService $reactionsService,
        CardsDataService $cardsDataService,
        DossieSourcesSummaryService $dossieSourcesSummaryService,
    )
    {
        $this->reactionsService = $reactionsService;
        $this->cardsDataService = $cardsDataService;
        $this->dossieSourcesSummaryService = $dossieSourcesSummaryService;
    }

    public function getDossieCardsData(int $dossieID): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->cardsDataService->getCardsData($dossieID)
        ];

        return response()->json($result, $result['status']);
    }

    public function getDossieCardsInfo(int $dossieId):JsonResponse
    {
        $data = app(DossieService::class)->getInfo($dossieId);
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $data
        ];

        return response()->json($result, $result['status']);
    }

    public function getSourceGroup(int $dossieId):JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => app(PesquisaConsultaService::class)->grupoFontes($dossieId)
        ];

        return response()->json($result, $result['status']);
    }

    public function getSourcesGroupResume(int $dossieID, string $grupoAlias):JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            // os dados estao mockados n precisa passar o dossieID agora
            'data' => $this->dossieSourcesSummaryService->getSourceGroupSummary($dossieID, $grupoAlias)
        ];

        return response()->json($result, $result['status']);
    }
    public function getDossieCardsWithoutResultSources(int $dossieId):JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => app(DossieService::class)->getCardSourceWithoutResult($dossieId)
        ];
    
        return response()->json($result, $result['status']);
    }
    public function getDossieCardsWithResultSources(int $dossieId):JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => app(DossieService::class)->getCardSourceWithResult($dossieId)
        ];

        return response()->json($result, $result['status']);
    }
    public function getAppsDisponiveis(int $dossieId):JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => app(AplicativoService::class)->getOtherApps($dossieId)
        ];
    
        return response()->json($result, $result['status']);
    }

    public function getReactions():JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->reactionsService->getReactions()
        ];

        return response()->json($result, $result['status']);
    }
}
