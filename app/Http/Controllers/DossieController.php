<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\Clients\Upminer\LoteProcessRow;
use App\Actions\PDF\ExportPdfApi;
use App\Domains\Dossie\Services\DossieService;
use App\Domains\Dossie\Services\PdfRequestService;
use App\Domains\Dossie\Services\PesquisaConsultaService;
use App\Http\FormRequest\GetStatusPdfRequest;
use App\Http\FormRequest\LoteRequest;
use App\Http\FormRequest\PostExportPdfDetails;
use App\Http\FormRequest\PostExtraDossiersGenerated;
use App\Http\FormRequest\PostExportPdfLawsuits;
use App\Http\FormRequest\PostExportPdfResume;
use App\Http\FormRequest\ViewCapturaByDossieRequest;
use App\Http\Requests\CheckDossiersErrorsRequest;
use App\Http\Requests\FixDossiersErrorsRequest;
use App\Http\Requests\HistoricoLoteRequest;
use App\Http\Requests\LimitDossieRequest;
use App\Http\Requests\ListDossieRequest;
use App\Http\Requests\LoteDuplicarRequest;
use App\Http\Requests\ReprocessDossieRequest;
use App\Domains\Dossie\Services\LoteConsultaService;
use App\Http\FormRequest\PostDossiers;
use App\Http\Requests\StoreLimitResultSourceRequest;
use App\Http\Requests\HomonymsRequest;
use App\Http\Resources\GetUserByCompanyResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Throwable;
use Exception;
use App\Domains\Dossie\Services\ProfileConsultationService;

class DossieController extends Controller
{
    private DossieService $dossieService;
    private ExportPdfApi $exportPdfApi;
    private PdfRequestService $pdfRequestService;

    public function __construct(
        DossieService $dossieService,
        ExportPdfApi $exportPdfApi,
        PdfRequestService $pdfRequestService,
        private readonly PesquisaConsultaService $pesquisaConsultaService,
        private readonly LoteConsultaService $loteConsultaService,
        private readonly ProfileConsultationService $profileConsultationService
    ) {
        $this->dossieService = $dossieService;
        $this->pdfRequestService = $pdfRequestService;
        $this->exportPdfApi = $exportPdfApi;
    }

    public function index(ListDossieRequest $request): JsonResponse
    {
        $params = $request->validated();
        $params['all'] = $params['all'] ?? false;

        $result = [
            'status' => 200,
            'data' => $this->dossieService->getListDossie($params)
        ];

        return response()->json($result, $result['status']);
    }

    public function getStatusPdfRequest(GetStatusPdfRequest $getStatusPdfRequest): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->pdfRequestService
                ->findById($getStatusPdfRequest->validated())

        ];

        return response()->json($result, $result['status']);
    }

    public function show(int $dossieId): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->dossieService->dossiLoteNavigate($dossieId)
        ];

        return response()->json($result, $result['status']);
    }


    public function addLoteFila(LoteRequest $loteRequest)
    {
        $token = session('id');
        return (new LoteProcessRow($token))->process($loteRequest->validated());
    }

    public function historicoLote(HistoricoLoteRequest $historicoLoteRequest)
    {
        $response = $this->pesquisaConsultaService->historicoDossie($historicoLoteRequest->validated());
        return response()->json([
            'data' => $response,
            'status' => Response::HTTP_OK
        ]);
    }

    public function loteDuplicado(LoteDuplicarRequest $loteDuplicarRequest)
    {
        $response = $this->pesquisaConsultaService->loteDuplicado($loteDuplicarRequest->validated());
        return response()->json([
            'data' => $response,
            'status' => Response::HTTP_OK
        ]);
    }

    public function filters()
    {
        $response = $this->pesquisaConsultaService->filters();
        return response()->json([
            'data' => $response,
            'status' => Response::HTTP_OK
        ]);
    }

    public function sendResume(PostExportPdfResume $postExportPdf)
    {
        return $this->exportPdfApi->sendRequestExport(
            $postExportPdf->validated(),
            false
        );
    }

    public function sendDetails(PostExportPdfDetails $postExportPdf)
    {
        return $this->exportPdfApi->sendRequestExport(
            $postExportPdf->validated(),
            true
        );
    }

    public function sendLawSuits(PostExportPdfLawsuits $postExportPdf)
    {
        return $this->exportPdfApi->sendRequestLawsuits(
            $postExportPdf->validated(),
            true
        );
    }

    public function viewCapturaByDossie(ViewCapturaByDossieRequest $viewCapturaByDossieRequest): JsonResponse
    {
        $viewCapturaByDossie = $viewCapturaByDossieRequest->validated();

        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->dossieService->viewCapturaByDossie($viewCapturaByDossie)
        ];

        return response()->json($result, $result['status']);
    }

    public function store(PostDossiers $request): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->loteConsultaService->create($request->validated())
        ];

        return response()->json($result, $result['status']);
    }

    public function reprocessDossies(ReprocessDossieRequest $request): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->dossieService->reprocessDossies($request->validated())
        ];

        return response()->json($result, $result['status']);
    }

    public function storeLimitDossie(LimitDossieRequest $limitDossieRequest): JsonResponse
    {
        $usuario = $this->dossieService->storeLimitDossie($limitDossieRequest->validated());
        $result = [
            'status' => Response::HTTP_OK,
            'data' => "Limite de dossie atualizado com sucesso para o usuario : {$usuario->nome}"
        ];

        return response()->json($result, $result['status']);
    }

    public function getLimitDossie(Request $request): JsonResponse
    {
        return GetUserByCompanyResource::collection(
            $this->dossieService->getLimitDossie($request->all())
        )->response();
    }

    public function storeLimitResultSource(StoreLimitResultSourceRequest $storeLimitResultSourceRequest): JsonResponse
    {
        $validatedData = $storeLimitResultSourceRequest->validated();
        $data = $this->dossieService->storeLimitResultSource($validatedData);

        $result = [
            'status' => Response::HTTP_OK,
            'data' => $data
        ];

        return response()->json($result, $result['status']);
    }

    public function getLimitResultSource(): JsonResponse
    {
        $data = $this->dossieService->getLimitResultSource();

        $result = [
            'status' => Response::HTTP_OK,
            'data' => $data
        ];

        return response()->json($result, $result['status']);
    }

    public function extraDossiersGenerated(PostExtraDossiersGenerated $request): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->profileConsultationService->extraDossiersGenerated($request->validated())
        ];

        return response()->json($result, $result['status']);
    }

    public function searchHomonyms(HomonymsRequest $homonymsRequest): JsonResponse
    {
        $validatedData = $homonymsRequest->validated();
        $response = $this->dossieService->searchHomonyms($validatedData);
        $result = [
            'status' => Response::HTTP_OK,
            'quantity' => count($response),
            'data' => $response
        ];

        return response()->json($result, $result['status']);
    }

    public function checkErrors(CheckDossiersErrorsRequest $request): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->dossieService->checkErrors($request->validated()),
        ];

        return response()->json($result, $result['status']);
    }

    public function fixErrors(FixDossiersErrorsRequest $request): JsonResponse
    {
        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->dossieService->fixErrors($request->validated()),
        ];

        return response()->json($result, $result['status']);
    }
}
