<?php

namespace App\Http\Controllers;

use App\Domains\LogProcessDossie\Services\LogProcessDossieService;
use App\Http\Requests\GetProgressRequest;
use App\Http\Requests\LogProcessDossieShowRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class LogProcessDossieController extends Controller
{
    public function __construct(
        private LogProcessDossieService $logProcessDossieService
    )
    {}

    public function show(LogProcessDossieShowRequest $request)
    {
        $dossierId = $request->route('dossieId');
        $lastProgress = $request->boolean('last');

        $result = [
            'status' => Response::HTTP_OK,
            'data' => $this->logProcessDossieService->getByDossierId($dossierId, $lastProgress),
        ];
    
        return response()->json($result, $result['status']);
    }

    public function getProgress(GetProgressRequest $request)
    {
        $response = [];
        $dossies = $request->validated()['dossie'];

        foreach ($dossies as $dossie) {
            $response[$dossie] = $this->logProcessDossieService->getDossierProgressById($dossie);
        }

        $result['status'] = Response::HTTP_OK;
        $result['data'] = $response;
    
        return response()->json($result, $result['status']);
    }
}
