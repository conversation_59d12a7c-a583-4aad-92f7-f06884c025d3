<template>
	<div
		class="flex flex-col gap-3 items-center"
		style="padding-top: 140px"
		v-if="!hasDossiers">
		<span
			style="font-size: 48px; color: #7922b9"
			class="material-symbols-outlined">
			inbox
		</span>

		<p class="text-xxs text-neutral-dark-1">
			{{
				props.amITheOwnerOfDossiers && !props.hasOthersFiltersBesideResponsible
					? $t('listDossiers.noDossiersCreated')
					: $t('listDossiers.noDossiersFound')
			}}
		</p>

		<RouterLink
			class="page__link text-brand-secondary-1 font-semibolder"
			to="/dossiers/create"
			v-if="
				props.amITheOwnerOfDossiers && !props.hasOthersFiltersBesideResponsible
			">
			{{ $t('listDossiers.createNewDossier') }}
		</RouterLink>
	</div>

	<template v-if="hasDossiers">
		<ul style="display: flex; gap: 10px" v-if="selectedDossiers.length > 0">
			<li>
				<up-button
					:disabled="shouldDisableReprocessSelectedDossiers.value"
					prependIcon="refresh"
					@click="reprocessSelectedDossiers()"
					type="secondary"
					size="sm"
					style="border-style: solid">
					<up-tooltip
						:tooltip-text="shouldDisableReprocessSelectedDossiers.text">
						<template #trigger>
							<span>{{ $t('common.reprocess') }}</span>
						</template>
					</up-tooltip>
				</up-button>
			</li>
			<li>
				<up-button
					:disabled="shouldDisableExportSelectedDossiers.value"
					@click="exportSelectedDossiers()"
					prependIcon="picture-as-pdf-outline"
					size="sm"
					type="secondary"
					style="border-style: solid">
					<up-tooltip :tooltip-text="shouldDisableExportSelectedDossiers.text">
						<template #trigger>
							<span>{{ $t('common.export') }}</span>
						</template>
					</up-tooltip>
				</up-button>
			</li>
		</ul>

		<table class="dossiers-table" aria-label="dossier-list-table">
			<thead>
				<tr>
					<th class="dossiers-table__heading">
						<up-checkbox
							v-model:checked="allSelected"
							:disabled="shouldDisableSelectAll"></up-checkbox>
					</th>
					<th
						class="dossiers-table__heading"
						v-for="{ text, id } in headers"
						:key="id">
						<span v-if="id !== 'criteria'"> {{ text }} </span>

						<button
							style="display: flex; align-items: center; width: 100%"
							@click="toggleDetailsForAllDossiers"
							v-else>
							{{ text }}

							<span
								style="font-size: 16px; color: purple; margin-left: auto"
								class="material-symbols-outlined">
								{{
									`expand_${
										dossiersToShowDetails.length === props.dossiers.length
											? 'less'
											: 'more'
									}`
								}}
							</span>
						</button>
					</th>
				</tr>
			</thead>
			<tbody>
				<template v-for="dossier in dossiers" :key="dossier.id">
					<tr
						class="dossiers-table__row"
						:class="[
							dossier.status === 'created' &&
								'dossiers-table__row--not-initialized',
						]">
						<td style="width: 39px" class="dossiers-table__data">
							<up-checkbox
								v-model:checked="selectedDossiers"
								name="reprocess"
								:value="dossier.id" />
						</td>

						<td style="width: 72px" class="dossiers-table__data">
							<button @click="navigateToDossier(dossier)">
								{{ formatNumberDossier(dossier.id) }}
							</button>
						</td>
						<td style="width: 88px" class="dossiers-table__data">
							{{ dossier.data_criacao && dossier.data_criacao.split(' ')[0] }}
						</td>
						<td class="dossiers-table__data" width="15%">
							<CustomSelect
								arrow-color="#7922b9"
								:modelValue="dossier.tag"
								@update:modelValue="(tag) => updateDossierTag({ dossier, tag })"
								:withoutBorder="true"
								minWidth="200px"
								:options="tags">
								<template #selected-option="option">
									<up-badge size="sm" :style="getTagStyle(option)">{{
										option.name
									}}</up-badge>
								</template>

								<template #option="{ name, color, font_color }">
									<div
										style="
											display: flex;
											align-items: center;
											justify-content: space-between;
											border-bottom: 1px solid #ccc;
											padding: 8px;
											color: rgb(102, 102, 102);
											font-size: 14px;
											text-align: left;
										">
										<up-badge
											size="sm"
											:style="{
												'background-color': color,
												color: font_color,
											}"
											>{{ name }}</up-badge
										>
										<span
											style="font-size: 16px; color: #7922b9"
											class="material-symbols-outlined">
											add
										</span>
									</div>
								</template>
							</CustomSelect>
						</td>
						<td class="dossiers-table__data" style="max-width: 0">
							<button @click="toggleDetailsFor(dossier)">
								<span class="material-symbols-outlined">
									{{ getRowCriteriaIcon(dossier.tipo_str) }}
								</span>
								<span class="truncate">
									{{ dossier.criterio_nome }}
								</span>

								<span
									style="font-size: 16px; color: purple; margin-left: auto"
									class="material-symbols-outlined">
									{{
										`expand_${
											checkIfDossierIsBeingShown(dossier) ? 'less' : 'more'
										}`
									}}
								</span>
							</button>
						</td>
						<td class="dossiers-table__data" style="width: 75px">
							<button
								:disabled="dossier.status === 'created'"
								class="dossiers-table__progress"
								@click="openDossierProcessingWindow(dossier)">
								{{ dossier.progresso_dossie || 0 }}%

								<span class="material-symbols-outlined"> help </span>
							</button>
						</td>
						<td class="dossiers-table__data truncate" style="width: 116px">
							<button
								:disabled="dossiersBeingReprocessed.includes(dossier.id)"
								v-if="dossier.status === 'created'"
								@click="reprocessDossier(dossier.id)">
								<up-badge
									:variant="getDossierStatusDetails(dossier).variant"
									size="sm">
									<strong>
										{{ getDossierStatusDetails(dossier).text }}
									</strong>
									<span
										style="font-size: 16px; color: #7922b9"
										class="material-symbols-outlined">
										slideshow
									</span>
								</up-badge>
							</button>
							<up-badge
								v-else
								:variant="getDossierStatusDetails(dossier).variant"
								size="sm">
								<strong>
									{{ getDossierStatusDetails(dossier).text }}
								</strong>
								<span
									style="font-size: 16px; color: #7922b9"
									v-if="dossier.status === 'created'"
									class="material-symbols-outlined">
									slideshow
								</span>
							</up-badge>
						</td>
						<td class="dossiers-table__data" style="width: 74px">
							<button
								class="flex items-center gap-[10px]"
								:style="
									dossier.perfil_default ? 'opacity: 0.5; cursor: default' : ''
								"
								:disabled="dossier.perfil_default"
								@click="
									!dossier.perfil_default && openMonitoringWindow(dossier)
								">
								<span
									style="color: #666"
									class="text-xxs material-symbols-outlined">
									open_in_new
								</span>
								{{ getMonitoringState(dossier.monitoringData) }}
							</button>
						</td>
						<td class="dossiers-table__data truncate" style="width: 93px">
							<up-badge
								v-if="dossier.status_workflow"
								size="sm"
								:variant="getWorkflowDetails(dossier.status_workflow).variant">
								<strong>
									{{ getWorkflowDetails(dossier.status_workflow).text }}
								</strong>
							</up-badge>
						</td>
						<td class="dossiers-table__data" style="width: 60px">
							<div
								v-if="dossier.score"
								:class="[
									'dossiers-table__score',
									`dossiers-table__score--${dossier.score.last_result_risk}`,
								]">
								{{ getScoreDetails(dossier.score.last_result_risk).text }}
								<span class="material-symbols-outlined">
									{{ getScoreDetails(dossier.score.last_result_risk).icon }}
								</span>
							</div>
						</td>
						<td class="dossiers-table__data" style="width: 104px">
							<ul class="dossiers-table__actions">
								<li
									class="dossiers-table__reprocess"
									v-if="dossier.status === 'error'">
									<button
										:disabled="dossiersBeingReprocessed.includes(dossier.id)"
										:title="$t('common.reprocessDossier')"
										class="dossiers-table__action"
										@click="reprocessDossier(dossier.id)">
										<span
											:class="[
												dossiersBeingReprocessed.includes(dossier.id) &&
													'animate-spin',
											]"
											class="material-icons-round">
											cached
										</span>
									</button>
								</li>
								<li v-if="['success', 'error'].includes(dossier.status)">
									<button
										:title="$t('common.downloadResult')"
										class="dossiers-table__action"
										@click="exportDossier({ dossier, isDetailed: true })">
										<span class="material-symbols-outlined"> download </span>
									</button>
								</li>
								<li v-if="['success', 'error'].includes(dossier.status)">
									<button
										@click="navigateToDossier(dossier)"
										:title="$t('listDossiers.seeDossier')"
										class="dossiers-table__action">
										<span class="material-icons-round"> open_in_new </span>
									</button>
								</li>
							</ul>
						</td>
					</tr>

					<tr
						class="dossiers-table__row"
						v-show="checkIfDossierIsBeingShown(dossier)">
						<td
							colspan="4"
							class="dossiers-table__data dossiers-table__data--derivation">
							{{ $t('common.queryProfile') }}: {{ dossier.perfil }}
						</td>
						<td
							colspan="1"
							class="dossiers-table__data dossiers-table__data--derivation">
							{{ dossier.criterio }}
						</td>

						<td
							colspan="7"
							class="dossiers-table__data dossiers-table__data--derivation">
							{{ $t('common.responsible') }}: {{ dossier.usuario }}
						</td>
					</tr>
				</template>
			</tbody>
		</table>
	</template>
</template>

<script setup>
import CustomSelect from '../CustomSelect.vue'

import { useExportationsStore } from '@/stores/exportationsStore'

import { RouterLink } from 'vue-router'
import { computed, ref, watch } from 'vue'
import { onClickOutside } from '@vueuse/core'
import { useTagsStore } from '../../stores/tags'
import { storeToRefs } from 'pinia'
import { formatNumberDossier, extractYearFromDate } from '@/utils/helpers'
import { useI18n } from 'vue-i18n'

const tagsStore = useTagsStore()
const { tags } = storeToRefs(tagsStore)

const floatMenu = ref(null)

onClickOutside(floatMenu, () => {
	dossierToShowFloatMenu.value = ''
})

const exportationStore = useExportationsStore()

const exportDossier = ({ dossier, isDetailed }) => {
	exportationStore.exportDirectly({
		dossier,
		isDetailed,
	})

	emits('openWindow', {
		name: 'exportings',
		data: {},
	})
}

const { t } = useI18n()

const headers = [
	{
		text: t('listDossiers.table.headings.dossierNumber'),
		id: 'number-dossie',
	},
	{ text: t('listDossiers.table.headings.createdAt'), id: 'created_at' },
	{ text: t('listDossiers.table.headings.tag'), id: 'tag' },
	{ text: t('listDossiers.table.headings.criteria'), id: 'criteria' },
	{ text: t('listDossiers.table.headings.progress'), id: 'progress' },
	{ text: t('listDossiers.table.headings.status'), id: 'status' },
	{ text: t('listDossiers.table.headings.monitoring'), id: 'monitoring' },
	{ text: t('listDossiers.table.headings.workflow'), id: 'workflow' },
	{ text: t('listDossiers.table.headings.score'), id: 'score' },
	{ text: t('listDossiers.table.headings.actions'), id: 'actions' },
]

const props = defineProps({
	dossiers: {
		type: Array,
		required: true,
	},
	dossiersBeingReprocessed: {
		type: Array,
		required: false,
		default: () => [],
	},
	amITheOwnerOfDossiers: {
		type: Boolean,
		required: false,
		default: true,
	},
	hasOthersFiltersBesideResponsible: {
		type: Boolean,
		required: false,
		default: false,
	},
	page: {
		type: Number,
		required: true,
	},
})

const hasDossiers = computed(() => props.dossiers.length > 0)

watch(
	() => props.page,
	(newPage) => {
		selectedDossiers.value = []
		dossiersToShowDetails.value = []
		dossierToShowFloatMenu.value = ''
	}
)

const dossierToShowFloatMenu = ref(null)

const toggleDossierToShowFloatMenu = (dossier) => {
	const alreadyBeingShown = dossierToShowFloatMenu.value === dossier.id

	if (alreadyBeingShown) {
		dossierToShowFloatMenu.value = null
	}
	dossierToShowFloatMenu.value = dossier.id
}

const dossiersToShowDetails = ref([])

const checkIfDossierIsBeingShown = (dossier) => {
	return dossiersToShowDetails.value.includes(dossier.id)
}

const toggleDetailsFor = (dossier) => {
	const alreadyBeingShown = checkIfDossierIsBeingShown(dossier)

	if (alreadyBeingShown) {
		dossiersToShowDetails.value = dossiersToShowDetails.value.filter(
			(dossierBeingShown) => dossierBeingShown !== dossier.id
		)
		return
	}
	dossiersToShowDetails.value.push(dossier.id)
}

const toggleDetailsForAllDossiers = () => {
	const shouldShow =
		dossiersToShowDetails.value.length !== props.dossiers.length

	if (!shouldShow) {
		dossiersToShowDetails.value = []
		return
	}

	props.dossiers.forEach((dossier) => {
		const alreadyBeingShown = checkIfDossierIsBeingShown(dossier)

		if (!alreadyBeingShown) {
			dossiersToShowDetails.value.push(dossier.id)
		}
	})
}

// TAG

const getTagStyle = (tag) => {
	if (!tag) {
		return
	}

	return {
		'background-color': tag.color,
		color: tag.font_color,
		padding: '4px 8px',
		'border-radius': '4px',
		width: 'max-content',
	}
}

// DOSSIE STATUS

const getDossierStatusDetails = (dossier) => {
	const messages = {
		processing: {
			text: t('common.dossierStatuses.processing'),
			variant: 'info',
		},
		created: {
			text: t('common.dossierStatuses.created'),
			variant: 'custom',
		},
		queue: {
			text: t('common.dossierStatuses.queue'),
			variant: 'text',
		},
		success: {
			text: t('common.dossierStatuses.success'),
			variant: 'success',
		},
		error: {
			text: t('common.dossierStatuses.error'),
			variant: 'warning',
		},
	}

	return messages[dossier.status] || messages['created']
}

const getRowCriteriaIcon = (type) => {
	const mapping = {
		PJ: 'corporate_fare',
		PF: 'account_circle',
	}

	return mapping[type]
}

const getWorkflowDetails = (workflowStatus) => {
	const mapping = {
		aprovado: {
			variant: t('common.workflow.approved'),
			text: 'Aprovado',
		},
		reprovado: {
			variant: 'error',
			text: t('common.workflow.reproved'),
		},
		em_analise: {
			variant: 'info',
			text: t('common.workflow.in_analysis'),
		},
		'sem-workflow': {
			variant: '',
			text: '',
		},
	}

	return mapping[workflowStatus]
}

const getScoreDetails = (scoreResult) => {
	const mapping = {
		low: {
			icon: 'arrow_downward',
			text: t('common.low'),
		},
		medium: {
			icon: 'arrow_right_alt',
			text: t('common.medium'),
		},
		high: {
			icon: 'arrow_upward',
			text: t('common.high'),
		},
	}

	return (
		mapping[scoreResult] || {
			text: '',
			icon: '',
		}
	)
}

// REPROCESS

const selectedDossiers = ref([])
const reprocessDossier = (dossierId) => {
	emits('reprocessDossier', { dossiersToReprocess: [dossierId] })
}

const reprocessSelectedDossiers = () => {
	emits('reprocessSelectedDossiers', {
		dossiersToReprocess: selectedDossiers.value,
	})
	selectedDossiers.value = []
}

const shouldDisableReprocessSelectedDossiers = computed(() => {
	const dossiersWithInvalidStatus = []

	selectedDossiers.value.forEach((selectedDossierId) => {
		const dossierData = props.dossiers.find(
			(dossier) => dossier.id === selectedDossierId
		)
		const isAllowedToReprocess = dossierData.status === 'error'
		if (!isAllowedToReprocess) {
			dossiersWithInvalidStatus.push(selectedDossierId.toString())
		}
	})

	const shouldAllow = dossiersWithInvalidStatus.length === 0

	if (!shouldAllow) {
		const formatter = new Intl.ListFormat('pt-BR', {
			style: 'long',
			type: 'conjunction',
		})

		return {
			value: true,
			text: t(
				'listDossiers.notAllowedToReprocessDossiers',
				{
					dossiers: formatter.format(dossiersWithInvalidStatus),
				},
				dossiersWithInvalidStatus.length
			),
		}
	}

	return {
		value: false,
		text: t('common.reprocess'),
	}
})

const shouldDisableExportSelectedDossiers = computed(() => {
	const dossiersWithInvalidStatus = []

	selectedDossiers.value.forEach((selectedDossierId) => {
		const dossierData = props.dossiers.find(
			(dossier) => dossier.id === selectedDossierId
		)

		const isAllowedToExport = ['error', 'success'].includes(dossierData.status)
		if (!isAllowedToExport) {
			dossiersWithInvalidStatus.push(selectedDossierId.toString())
		}
	})

	const shouldAllow = dossiersWithInvalidStatus.length === 0

	if (!shouldAllow) {
		const formatter = new Intl.ListFormat('pt-BR', {
			style: 'long',
			type: 'conjunction',
		})

		return {
			value: true,
			text: t(
				'listDossiers.notAllowedToExportDossiers',
				{
					dossiers: formatter.format(dossiersWithInvalidStatus),
				},
				dossiersWithInvalidStatus.length
			),
		}
	}

	return {
		value: false,
		text: t('common.export'),
	}
})

const exportSelectedDossiers = () => {
	selectedDossiers.value.forEach((selectedDossierId) => {
		const dossier = props.dossiers.find(
			(dossier) => dossier.id === selectedDossierId
		)

		exportDossier({ dossier, isDetailed: true })
	})
}

const shouldDisableSelectAll = computed(() => {
	return props.dossiers.every(
		(dossier) => !['success', 'error'].includes(dossier.status)
	)
})

const allSelected = computed({
	get() {
		return props.dossiers
			.filter((dossier) => ['success', 'error'].includes(dossier.status))
			.every((dossie) => {
				return selectedDossiers.value.some((dossierSelectToReprocess) => {
					return dossierSelectToReprocess === dossie.id
				})
			})
	},
	set(value) {
		if (value) {
			selectedDossiers.value = props.dossiers
				.filter((dossier) => ['success', 'error'].includes(dossier.status))
				.map((dossier) => dossier.id)
		} else {
			selectedDossiers.value = []
		}
	},
})

const legadoDossierUrl = import.meta.env.VITE_APP_DOSSIE_LEGADO_URL

function getDossier(dossier) {
	const generatedNewDossier = '2'
	const creationYearDossier = extractYearFromDate(dossier.data_criacao)
	const baseYearNewDossier = 2024

	if (creationYearDossier < baseYearNewDossier) {
		return `${legadoDossierUrl}dossie/${dossier.company}/${dossier.id}`
	}

	if (
		dossier.dossie_versao !== generatedNewDossier &&
		!dossier.dossie_convertido
	) {
		return `/dossiers/convert?dossieID=${dossier.id}`
	} else {
		return `/dossiers/result/${dossier.id}`
	}
}

function navigateToDossier(dossier) {
	const url = getDossier(dossier)
	window.open(url, '_blank')
}

const emits = defineEmits([
	'openWindow',
	'reprocessDossier',
	'reprocessSelectedDossiers',
	'updateDossierTag',
])

const updateDossierTag = ({ dossier, tag }) => {
	dossier.tag = tag

	emits('updateDossierTag', { dossier, tag })
}

const getMonitoringState = (monitoring) => {
	if (!monitoring) {
		return 'off'
	}

	const isInactive = !monitoring.ativo

	const isStopped = monitoring.status === 0

	if (isStopped || isInactive) {
		return 'off'
	}

	return 'on'
}

const openMonitoringWindow = (dossier) => {
	emits('openWindow', {
		name: 'dossier-monitoring',
		data: dossier,
	})
}

const openDossierProcessingWindow = (dossier) => {
	emits('openWindow', {
		name: 'dossier-processing',
		data: dossier,
	})
}
</script>

<style lang="scss" scoped>
@import url('./styles.scss');

:deep(.custom-select__trigger) {
	background-color: transparent !important;
}
</style>
