<template>
	<TitleBar :borderTop="true" style="width: 100%">
		<template v-slot:left>
			<h3>{{ title }}</h3>
		</template>

		<template v-slot:content>
			<ul class="content">
				<li v-for="(item, index) in items" :key="index" class="item">
					<template v-if="typeof item === 'object' && item !== null">
						<strong v-if="item.nome">{{ item.nome }}</strong>
						<p v-if="item.descricao">{{ item.descricao }}</p>
						<template v-if="!item.nome && !item.descricao">
							{{ item }}
						</template>
					</template>
					<template v-else>
						{{ item }}
					</template>
				</li>
			</ul>
		</template>
	</TitleBar>
</template>
<script setup>
import TitleBar from '@/components/TitleBar/TitleBar.vue'

defineProps({
	title: { type: String, required: true },
	items: { type: Array, required: true },
})
</script>

<style scoped>
@import url(./Cards.scss);
</style>
