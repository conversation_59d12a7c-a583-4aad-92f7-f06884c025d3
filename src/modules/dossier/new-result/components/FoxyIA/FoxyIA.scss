.foxyIA {
	display: grid;
	grid-template-columns: 70% 30%;
	grid-column-gap: 24px;

	padding: 24px;

	border-radius: 0px 0px 4px 4px;
	border-right: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	border-bottom: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	border-left: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	background: var(--Light-neutral-light-pure, #fff);
}

.header {
	display: flex;
	flex-direction: row;
	gap: 16px;

	&__title {
		color: var(--Dark-neutral-dark-03, #666);

		font-size: 10px;
		font-style: normal;
		font-weight: 400;
		line-height: 14px;
	}

	&__info {
		color: var(--Dark-neutral-dark-pure, #121212);

		font-size: 12px;
		font-style: normal;
		font-weight: 600;
		line-height: 16px;
	}

	&__domain {
		color: var(--Brand-brand-secondary-01, #7922b9);
		font-variant-numeric: lining-nums proportional-nums;

		font-size: 12px;
		font-style: normal;
		font-weight: 600;
		line-height: 16px;

		&--items {
			cursor: pointer;
		}
	}

	&__no-domain {
		color: var(--Dark-neutral-dark-pure, #121212);
	}

	&__social-media {
		display: flex;
		align-items: center;
		gap: 4px;
		margin-top: -3px;
	}
}

.status {
	&__ativo {
		display: flex;
		align-items: center;
		color: var(--Feedback-feedback-sucess, #0a9e5a);

		font-size: 12px;
		font-style: normal;
		font-weight: 600;
		line-height: 16px;

		&--dot {
			background-color: var(--Feedback-feedback-sucess, #0a9e5a);
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin-right: 8px;
		}
	}

	&__inativo {
		display: flex;
		align-items: center;
		color: var(--Feedback-feedback-sucess, #cc3333);

		font-size: 12px;
		font-style: normal;
		font-weight: 600;
		line-height: 16px;

		&--dot {
			background-color: var(--Feedback-feedback-sucess, #cc3333);
			width: 10px;
			height: 10px;
			border-radius: 50%;
			margin-right: 8px;
		}
	}
}

.summary {
	margin: 16px 0px;
	&__title {
		color: var(--Dark-neutral-dark-03, #666);

		font-size: 10px;
		font-style: normal;
		font-weight: 400;
		line-height: 14px;
	}

	&__description {
		color: var(--Dark-neutral-dark-pure, #121212);

		font-size: 12px;
		font-style: normal;
		font-weight: 400;
		line-height: 16px;
	}
}

.cards {
	display: flex;
	flex-direction: column;
	width: 100%;
	gap: 16px;

	&__items {
		display: flex;

		gap: 16px;
	}
}

:deep(.foxyIA > .window) {
	padding: 24px !important;
}

.modal-content p {
	color: var(--Dark-neutral-dark-pure, #121212);

	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
	text-align: left;
}

.view-more {
	color: var(--Feedback-feedback-info, #505aff);

	font-size: 12px;
	font-style: normal;
	font-weight: 600;
	line-height: 16px;

	margin-top: 14px;
}

.localization--with-cards {
	margin-top: 48px;
}

.localization--without-cards {
	margin-top: 16px;
}

:deep(.flex) {
	border-style: solid;
}

.modal-content-domains-item {
	padding: 10px 11px;
	border-bottom: 1px solid var(--Light-neutral-light-02, #ccc);
	background-color: var(--Light-neutral-light-pure, #fff);

	color: var(--Dark-neutral-dark-03, #666);
	font-variant-numeric: lining-nums proportional-nums;

	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
	cursor: pointer;
}

.business-section {
	margin-top: 20px;
	width: 100%;
	padding: 8px 0;

	.info-row {
		display: flex;
		flex-wrap: wrap;
		gap: 24px;
		
		div {
			min-width: 200px;
			flex: 1;
		}
	}
}

.activities-section,
.directors-section,
.shareholders-section {
	margin-top: 20px;
	width: 100%;
	padding: 8px 0;
	
	.header__info div {
		margin-bottom: 10px;
		padding: 6px 0;
		
		&:last-child {
			margin-bottom: 0;
		}
	}
}
