<template>
	<UpAccordion
		:items="items"
		default-value="insights"
		class="accordion"
		v-if="isLoading || dataFoxy"
		style="margin-bottom: 16px">
		<template #icon>
			<NewInlineSvg name="foxy-orange.svg" width="19" height="19" />
		</template>
		<template #insights>
			<v-skeleton-loader v-if="isLoading" type="card"></v-skeleton-loader>
			<div class="foxyIA" v-else>
				<div>
					<header class="header">
						<div>
							<span class="header__title"
								>{{ t('result.foxy_insights.status') }}:</span
							>
							<span></span>
							<span
								:class="{
									status__ativo:
										dataFoxy.general_information.status === 'Ativo',
									status__inativo:
										dataFoxy.general_information.status !== 'Ativo',
									}">
								<p
									:class="{
										'status__ativo--dot':
											dataFoxy.general_information.status === 'Ativo',
										status__inativo:
											dataFoxy.general_information.status !== 'Ativo',
										}"></p>
										<p>{{ displayValue(dataFoxy.general_information.status) }}</p>
									</span>
								</div>

						<div>
							<span class="header__title">{{
								t('result.foxy_insights.cnpj')
							}}</span>
									<p class="header__info">
										{{ displayValue(dataFoxy.general_information.cnpj) }}
									</p>
								</div>
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.company_name')
							}}</span>
									<p class="header__info">
								{{
									displayValue(
										dataFoxy.general_information.company_fantasy_name
									)
								}}
									</p>
								</div>
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.opening_date')
							}}</span>
									<p class="header__info">
										{{ displayValue(dataFoxy.general_information.opening_date) }}
									</p>
								</div>
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.domains')
							}}</span>
							<div
								class="header__domain"
								:class="{
									'header__domain--items':
										dataFoxy.general_information.domains.length > 0,
								}">
								<button
									style="
										display: flex;
										justify-content: center;
										align-items: center;
										gap: 4px;
									"
									@click="openWindowModal"
									v-if="dataFoxy.general_information.domains.length > 0">
											{{ padNumber(dataFoxy.general_information.domains.length) }}
									<NewInlineSvg
										name="arrowRight.svg"
										width="8"
										height="8"
										fill="#7922B9" />
										</button>
										<p v-else class="header__no-domain">---</p>
									</div>
								</div>
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.social_medias')
							}}</span>
									<div class="header__social-media">
										<span v-if="!hasSocialNetworks"> --- </span>
								<span
									v-for="(url, socialNetwork) in dataFoxy.general_information
										.social_networks"
									:key="socialNetwork">
									<a
										v-if="typeof url === 'string' && url.trim() !== ''"
										:href="url"
										target="_blank"
										rel="noopener noreferrer">
										<NewInlineSvg
											:name="getSocialNetworkIcon(socialNetwork)"
													:width="getIconSize(socialNetwork).width"
													:height="getIconSize(socialNetwork).height" />
											</a>
										</span>
							</div>
						</div>
					</header>
					<div class="summary">
						<span class="summary__title">{{
							t('result.foxy_insights.resume_activity')
						}}</span>
						<p class="summary__description">{{ truncatedText }}</p>
						<button
							class="view-more"
							v-if="isTruncated"
							@click="openReadMoreModal">
							{{ t('result.foxy_insights.view_more') }}
						</button>
					</div>
					<div class="cards">
						<span class="cards__items">
							<Cards
								v-if="hasCharacter(dataFoxy.marks_and_products)"
								:title="t('result.foxy_insights.marks_and_products')"
								:items="dataFoxy.marks_and_products" />
							<Cards
								v-if="
									hasCharacter(dataFoxy.technologies_and_business_processes)
								"
								:title="
									t('result.foxy_insights.technologies_business_processes')
								"
								:items="dataFoxy.technologies_and_business_processes" />
						</span>
						<span class="cards__items">
							<Cards
								v-if="hasCharacter(dataFoxy.competitivity_and_positioning)"
								:title="t('result.foxy_insights.competitivity_and_positioning')"
								:items="dataFoxy.competitivity_and_positioning" />
							<Cards
								v-if="hasCharacter(dataFoxy.trends_and_insights)"
								:title="t('result.foxy_insights.trends_and_insights')"
								:items="dataFoxy.trends_and_insights" />
						</span>
					</div>
					
					<!-- Business Information Section -->
					<div class="business-section" v-if="dataFoxy.general_information">
						<div class="info-row">
							<div v-if="dataFoxy.general_information.business_size">
								<span class="header__title">{{
									t('result.foxy_insights.business_size')
								}}</span>
								<p class="header__info">
									{{ displayValue(dataFoxy.general_information.business_size) }}
								</p>
							</div>
							<div v-if="dataFoxy.general_information.legal_nature">
								<span class="header__title">{{
									t('result.foxy_insights.legal_nature')
								}}</span>
								<p class="header__info">
									{{ displayValue(dataFoxy.general_information.legal_nature) }}
								</p>
							</div>
							<div v-if="dataFoxy.general_information.initial_capital">
								<span class="header__title">{{
									t('result.foxy_insights.initial_capital')
								}}</span>
								<p class="header__info">
									{{ displayValue(dataFoxy.general_information.initial_capital) }}
								</p>
							</div>
							<div v-if="dataFoxy.general_information.main_activity">
								<span class="header__title">{{
									t('result.foxy_insights.main_activity')
								}}</span>
								<p class="header__info">
									{{ displayValue(dataFoxy.general_information.main_activity) }}
								</p>
							</div>
						</div>
					</div>
					
					<!-- Secondary Activities Section -->
					<div class="activities-section" v-if="hasCharacter(dataFoxy.general_information.secondary_activities)">
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.secondary_activities')
							}}</span>
							<div class="header__info">
								<div v-for="(activity, index) in dataFoxy.general_information.secondary_activities" :key="index">
									{{ displayValue(activity) }}
								</div>
							</div>
						</div>
					</div>
					
					<!-- Board of Directors Section -->
					<div class="directors-section" v-if="hasCharacter(dataFoxy.general_information.board_of_directors)">
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.board_of_directors')
							}}</span>
							<div class="header__info">
								<div v-for="(director, index) in dataFoxy.general_information.board_of_directors" :key="index">
									{{ director.nome }} <span v-if="director.cargo && director.cargo !== 'Não especificado'">({{ director.cargo }})</span>
								</div>
							</div>
						</div>
					</div>
					
					<!-- Shareholders Section -->
					<div class="shareholders-section" v-if="hasCharacter(dataFoxy.general_information.shareholders)">
						<div>
							<span class="header__title">{{
								t('result.foxy_insights.shareholders')
							}}</span>
							<div class="header__info">
								<div v-for="(shareholder, index) in dataFoxy.general_information.shareholders" :key="index">
									{{ shareholder.nome }} <span v-if="shareholder.qualificacao">({{ shareholder.qualificacao }})</span>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div :class="localizationClass">
					<Localization
						:address="dataFoxy.address_information.address"
						:latitude="dataFoxy.address_information.latitude"
						:longitude="dataFoxy.address_information.longitude"
						:contacts="dataFoxy.contacts" />
				</div>
			</div>
		</template>
	</UpAccordion>

	<Window
		@on-close="closeWindow"
		:is-open="openWindow"
		:title="{
			svg: {
				name: 'captive_portal.svg',
				width: 20,
				height: 20,
				fill: '#7922B9',
			},
			text: t('result.foxy_insights.domains_registered'),
		}">
		<ul>
			<li
				v-for="(item, index) in dataFoxy.general_information.domains"
				:key="index"
				class="modal-content-domains-item">
				<a :href="'https://' + item" target="_blank">{{ item }}</a>
			</li>
		</ul>
	</Window>
	<Window
		@on-close="closeReadMoreWindow"
		:is-open="openReadMoreWindow"
		:title="t('result.foxy_insights.resume_activity')">
		<div class="modal-content">
			<p>{{ dataFoxy.conclusion }}</p>
		</div>
	</Window>
</template>

<script setup>
import Cards from './Cards/Cards.vue'
import Localization from './Localization/Localization.vue'

import Window from '@/components/Window/Window.vue'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'

import { useI18n } from 'vue-i18n'
import { ref, computed, watch } from 'vue'

const props = defineProps({
	dataFoxy: { type: Object, required: false },
	isLoading: { type: Boolean, required: true },
})

const emits = defineEmits(['update:isLoading', 'update:dataFoxy'])

watch(props.isLoading, (newLoadingState) => {
	emits('update:isLoading', newLoadingState)
})

watch(props.dataFoxy, (newDataFoxy) => {
	emits('update:dataFoxy', newDataFoxy)
})

const { t } = useI18n()

const items = [{ value: 'insights', title: t('result.foxy_insights.title') }]

function hasCharacter(array) {
	if (!Array.isArray(array)) return false;
	return array.some((element) => {
		if (typeof element === 'string') return element.trim() !== '';
		if (element && typeof element === 'object') {
			if (element.nome && typeof element.nome === 'string') return element.nome.trim() !== '';
			if (element.descricao && typeof element.descricao === 'string') return element.descricao.trim() !== '';
			return Object.values(element).some(value => 
				typeof value === 'string' && value.trim() !== ''
			);
		}
		return false;
	});
}

function displayValue(value) {
	if (!value || typeof value !== 'string') return '---';
	return value.trim() !== '' ? value : '---';
}

const openWindow = ref(false)
const openReadMoreWindow = ref(false)

function closeWindow() {
	openWindow.value = false
}

function openWindowModal() {
	openWindow.value = true
}

function closeReadMoreWindow() {
	openReadMoreWindow.value = false
}

function openReadMoreModal() {
	openReadMoreWindow.value = true
}

function padNumber(number) {
	return number < 10 ? '0' + number : number.toString()
}

const socialNetworkIcons = {
	LinkedIn: 'linkedin.svg',
	Instagram: 'instagram.svg',
	Outros: 'outros.svg',
	Facebook: 'facebook.svg',
	Twitter: 'twitter.svg',
	Youtube: 'youtube_red.svg',
}

const iconSizes = {
	LinkedIn: { width: 18, height: 18 },
	Instagram: { width: 14, height: 14 },
	Outros: { width: 18, height: 18 },
	Facebook: { width: 22, height: 22 },
	Twitter: { width: 16, height: 16 },
	Youtube: { width: 18, height: 18 },
}

const getSocialNetworkIcon = (socialNetwork) => {
	return socialNetworkIcons[socialNetwork] || ''
}

const getIconSize = (socialNetwork) => {
	return iconSizes[socialNetwork] || { width: 20, height: 20 }
}

const hasCards = computed(() => {
	return (
		hasCharacter(props.dataFoxy.marks_and_products) ||
		hasCharacter(props.dataFoxy.technologies_and_business_processes) ||
		hasCharacter(props.dataFoxy.competitivity_and_positioning) ||
		hasCharacter(props.dataFoxy.trends_and_insights)
	)
})

const maxCharacters = computed(() => {
	return hasCards.value ? 200 : 400
})

const truncatedText = computed(() => {
	if (props.dataFoxy.conclusion.length > maxCharacters.value) {
		return props.dataFoxy.conclusion.slice(0, maxCharacters.value) + '...'
	}
	return props.dataFoxy.conclusion
})

const isTruncated = computed(
	() => props.dataFoxy.conclusion.length > maxCharacters.value
)

const localizationClass = computed(() => {
	return hasCards.value
		? 'localization--with-cards'
		: 'localization--without-cards'
})

const hasSocialNetworks = computed(() => {
	return Object.values(props.dataFoxy.general_information.social_networks).some(
		(url) => typeof url === 'string' && url.trim() !== ''
	)
})
</script>

<style scoped>
@import url(./FoxyIA.scss);
</style>
