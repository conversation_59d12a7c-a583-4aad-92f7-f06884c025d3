<template>
	<section
		class="accordion-source"
		:data-source="props.source.id"
		id="accordion-source">
		<div class="accordion-source__header">
			<div class="accordion-source__header--options">
				<button @click="getDetailsDictionary(source)">
					<NewInlineSvg
						name="informationInline.svg"
						width="20"
						height="20"
						fill="#666666"
						class="accordion-source__icon--svg" />
				</button>

				<button
					@click="toggle"
					:title="source.sourceName"
					class="accordion-source__header--options-button">
					<h3 class="accordion-source__name">{{ source.sourceName }}</h3>
				</button>
			</div>
			<div class="accordion-source__settings">
				<div
					class="accordion-source__filter"
					:class="{ active: hasFilter }"
					v-if="sourceHasFilters() && isOpen">
					<Dropdown v-model="filtersIsOpen" direction="bottom">
						<template v-slot:trigger>
							<inline-svg
								name="filter.svg"
								class="accordion-source__filter--img" />
						</template>

						<span class="accordion-source__filter--menu">
							<Filters
								:filters="convertFilters()"
								@change="onFilter"
								@close="onCloseFilters" />
						</span>
					</Dropdown>
				</div>
				<span class="accordion-source__settings--tag">
					<Dropdown v-model="tagsIsOpen" direction="top">
						<template v-slot:trigger>
							<Tooltip v-if="!selectedTag" :content="$t('components.addTag')">
								<span>
									<NewInlineSvg
										name="marking.svg"
										width="20"
										height="20"
										fill="#666666" />
								</span>
							</Tooltip>
							<span
								v-else
								class="accordion-source__tag--item"
								:style="{ backgroundColor: findTag(selectedTag.id)?.color }"
								>{{ findTag(selectedTag.id)?.name }}</span
							>
						</template>

						<span class="accordion-source__tag--drop">
							<div
								class="accordion-source__tag--edit"
								v-if="!showTags && selectedTag">
								<Tooltip :content="$t('tooltip.editTagCategory')">
									<span @click="openEditTag()">
										<inline-svg
											name="edit.svg"
											width="24"
											height="24"
											fill="#666666" />
									</span>
								</Tooltip>
								<Tooltip :content="$t('tooltip.removeTagCategory')">
									<span type="button" @click="removeTagSource(selectedTag)">
										<inline-svg
											name="delete.svg"
											width="24"
											height="24"
											fill="#666666" />
									</span>
								</Tooltip>
							</div>
							<Tags
								v-else
								@add="onAddTag"
								@remove="onRemoveTag"
								@select="onSelectTag"
								@close="onCloseTags"
								:isOpen="tagsIsOpen" />
						</span>
					</Dropdown>
				</span>
				<span class="accordion-source__settings--reaction">
					<Dropdown v-model="reactionIsOpen" direction="top">
						<template v-slot:trigger>
							<Tooltip
								v-if="!selectedReaction"
								:content="$t('tooltip.addReaction')">
								<span>
									<NewInlineSvg
										name="reaction.svg"
										width="20"
										height="20"
										fill="#666666" />
								</span>
							</Tooltip>

							<div v-else>
								<Tooltip :content="selectedReaction.label">
									<div class="reaction">
										<span
											v-if="selectedReaction?.label"
											class="reaction__label"
											:style="{
												backgroundColor: selectedReaction?.fill,
											}">
											{{ selectedReaction?.label }}
										</span>
										<Reaction
											:id="selectedReaction?.id"
											:active="reactionIsOpen" />
									</div>
								</Tooltip>
							</div>
						</template>
						<Reactions @react="onSelectReaction" />
					</Dropdown>
				</span>

				<div class="accordion-source__settings--comments">
					<Tooltip
						:content="
							$t(
								`result.comments.${
									showComments ? 'close' : 'open'
								}SourceComments`
							)
						">
						<button @click="toggleShowComments(!showComments)">
							<NewInlineSvg
								:aria-label="$t('result.comments.sourceWithComments')"
								v-if="source.hasComments"
								name="haveComments.svg"
								width="24"
								height="24"
								fill="#666666" />
							<NewInlineSvg
								:aria-label="$t('result.comments.sourceWithoutComments')"
								v-else
								name="comments.svg"
								width="20"
								height="20"
								fill="#666666" />
						</button>
					</Tooltip>
				</div>
				<Tooltip :content="$t('tooltip.exportFont')">
					<ButtonExportSource :sourceId="source.id" />
				</Tooltip>

				<button @click="toggle">
					<span class="accordion-source__icon" :class="{ rotate: isOpen }">
						<NewInlineSvg
							name="expandMore.svg"
							width="20"
							height="20"
							fill="#7922B9"
							class="accordion-source__icon--svg" />
					</span>
				</button>
			</div>
		</div>
		<div v-if="isOpen" class="accordion-source__content">
			<v-skeleton-loader
				v-if="showLoadingSkeleton"
				:elevation="2"
				type="article"></v-skeleton-loader>
			<template v-if="!showLoadingSkeleton">
				<AlertFilterJuridico v-if="filtersStore.hasFiltersApplied"/>
				<RenderSourceComponent
					v-if="sourceData?.components"
					:components="sourceData?.components"
					:relevanceOption="sourceData?.hasRelevant" />
				<span v-else class="source-unavailable">{{
					$t('components.sourceUnavailable')
				}}</span>

				<div ref="commentsRef">
					<Comments
						v-show="showComments"
						:source-id="source.id"
						:dossier-id="dossieId"
						@close-comments="toggleShowComments(false)" />
				</div>
			</template>
		</div>
	</section>
	<Window
		class="window__dictionary"
		@on-close="closeWindow"
		:is-open="openWindow"
		:title="source.sourceName">
		<div
			v-if="isLoadingDictionary"
			style="
				height: 30rem;

				display: flex;
				align-items: center;
				justify-content: center;
			">
			<Loading color="#7922b9" size="30" />
		</div>
		<div v-else>
			<div v-if="fontDictionary.descricao_fornecedor_fonte">
				<p class="window__title">Descrição:</p>
				<p
					class="window__description"
					v-html="fontDictionary.descricao_fornecedor_fonte"></p>
			</div>
			<NotResult v-else />
		</div>
	</Window>
</template>

<script setup>
import RenderSourceComponent from '../RenderSourceComponent/RenderSourceComponent.vue'
import Reactions from '../Reactions/Reactions.vue'
import Reaction from '../Reactions/Reaction.vue'
import { getReaction } from '../Reactions/reactions'
import Tags from '../Tags/Tags.vue'
import Comments from '../Comments/Comments.vue'
import Filters from '../Filters/Filters.vue'
import Window from '@/components/Window/Window.vue'
import Loading from '@/components/Loading/Loading.vue'
import {
	addReactionSource,
	removeReactionSource,
	createTag,
	removeTagFromHistory,
	setSourceTag,
	editTag,
	removeTagFromSource,
	testTemplate,
	getSourceDictionary,
} from '../../../services/dossie_results.js'

import { getSource } from '../../../services/result.js'
import { useTagsStore, useTagsSourceStore } from '../../../stores/tags_store.js'
import { useReactionSourceStore } from '../../../stores/reactions_store'
import ButtonExportSource from '../ButtonExportSource/ButtonExportSource.vue'

import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'
import Tooltip from '@/components/Tooltip/Tooltip.vue'
import InlineSvg from '@/components/InlineSvg/InlineSvg.vue'
import Dropdown from '@/components/Dropdown/Dropdown.vue'
import AlertFilterJuridico from '../../AlertFilterJuridico/AlertFilterJuridico.vue'

import { useToast } from '@aplicativo/uplexis-ui'
import { useTimeout } from '@vueuse/core'
import {
	ref,
	computed,
	watch,
	inject,
	onMounted,
	provide,
	readonly,
	nextTick,
} from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute } from 'vue-router'
import NotResult from './NotResult.vue'
import { useFiltersStore } from '../../../stores/filters_juridico_store.js'

const props = defineProps({
	source: { type: Object, required: true },
	isMock: { type: Boolean, required: false },
	isOpenAllAccordion: { type: Boolean, default: false, required: false}
})

const toast = useToast()
const { t } = useI18n({})
const route = useRoute()

const sourceData = ref({})
const isOpen = ref(false)
const filtersIsOpen = ref(false)
const reactionIsOpen = ref(false)
const tagsIsOpen = ref(false)
const appliedFilters = ref([])
const reactionsStore = useReactionSourceStore()
const { addReaction, removeReaction, findReaction } = reactionsStore
const selectedReaction = computed(() => findReaction(props.source?.id))
const tagToEdit = ref(false)
const fontDictionary = ref(null)
const isLoadingDictionary = ref(true)

const showComments = ref(false)
const commentsRef = ref(null)
const toggleShowComments = (newValue) => {
	if (!isOpen.value) {
		isOpen.value = true
	}

	showComments.value = newValue

	if (showComments.value) {
		nextTick(() => {
			setTimeout(() => {
				commentsRef.value.scrollIntoView({
					behavior: 'smooth',
				})
			}, 1000)
		})
	}
}

const emit = defineEmits(['update-open-accordions-source'])

watch(() => props.isOpenAllAccordion, (newValue) => {
  if (newValue === true && !isOpen.value) {
    toggle()
  } else if (newValue === false && isOpen.value) {
    toggle()
  }
})

const openWindow = ref(false)

function closeWindow() {
	openWindow.value = false
}

function openWindowModal() {
	openWindow.value = true
}

provide('tag-to-edit', tagToEdit)
provide('source-data', readonly(sourceData))
provide('accordion-source-filters', readonly(appliedFilters))

const dossieId = inject('dossie-id')
const focusedSource = inject('focused-source')

watch(focusedSource, () => {
	focusedSourceList()
})

async function getDetailsDictionary(source) {
	try {
		openWindowModal()
		const idSource = source.id
		fontDictionary.value = await getSourceDictionary(idSource)
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	} finally {
		isLoadingDictionary.value = false
	}
}

const { start, stop } = useTimeout(10000, { controls: true })

const tagStore = useTagsStore()
const tagSourceStore = useTagsSourceStore()
const { removeTag, findTag } = tagStore
const { addTagSource, findTagSource, selectTagSource, deleteTagSource } =
	tagSourceStore
const selectedTag = computed(() => {
	return findTagSource(props.source.id)
})
const showTags = ref(false)

const hasFilter = computed(() => Object.keys(appliedFilters.value).length > 0)
const urlDictionarySources = import.meta.env.VITE_APP_DOSSIE_LEGADO_URL


const isFilterAnaliticsSources = computed(() => [408, 400, 410].includes(props.source.id))

function focusedSourceList() {
	if (props.source?.id === focusedSource.value.id) {
		toggle()
		const source = document.querySelector(
			`[data-source="${focusedSource.value.id}"]`
		)
		source.scrollIntoView({ behavior: 'smooth', block: 'start' })

		source.scrollIntoView({ behavior: 'smooth', block: 'start' })

		setTimeout(() => {
			const offset =
				source.getBoundingClientRect().top + window.pageYOffset - 200
			window.scrollTo({
				top: offset,
				behavior: 'smooth',
			})
		})
	}
}

watch(tagsIsOpen, () => {
	stop()

	if (!tagsIsOpen.value && selectedTag) {
		tagToEdit.value = false
		showTags.value = false
	}
})

watch(reactionIsOpen, () => {
	if (reactionIsOpen.value) {
		stop()
	}
})

watch(
	() => props.source,
	() => changeSelectedTag()
)

function onCloseTags() {
	tagsIsOpen.value = false
}

async function removeTagSource() {
	try {
		await removeTagFromSource(dossieId.value, props.source.id)
		deleteTagSource(props.source.id)
		tagsIsOpen.value = false
	} catch (error) {
		toast.addToast({
			content: t('toast.genericError'),
			type: 'error',
		})
	}
}

function changeSelectedTag() {
	if (!props.source.tag) return

	if (selectedTag.value === null) return

	selectTagSource(props.source.id, {
		id: props.source.tag.tag_id,
		name: props.source.tag.tag,
		color: props.source.tag.cor,
	})
}

async function onRemoveTag(tag) {
	try {
		await removeTagFromHistory(dossieId.value, tag)
		removeTag(tag)
		deleteTagSource(props.source.id)
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function onAddTag(tag, clear) {
	if (tag.name === '') {
		toast.addToast({
			content: 'Informe um nome de tag valida',
			type: 'error',
		})

		return
	}

	try {
		if (tagToEdit.value) {
			const newTag = { ...tag, id: tagToEdit.value }

			const newTags = await editTag(dossieId.value, props.source.id, newTag)
			addTagSource(props.source.id, newTags, tag)
			clear()
			tagsIsOpen.value = false

			return
		}

		const newTags = await createTag(dossieId.value, props.source.id, tag)
		addTagSource(props.source.id, newTags, tag)
		clear()
		tagsIsOpen.value = false
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

function openEditTag() {
	showTags.value = true
}

async function onSelectTag(tag, clear) {
	if (tag.name) {
		try {
			tagsIsOpen.value = false
			const newTags = await setSourceTag(dossieId.value, props.source.id, tag)
			addTagSource(props.source.id, newTags, tag)
			clear()
		} catch (error) {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
		}
	}
}

function toggle() {
	isOpen.value = !isOpen.value

	showComments.value = isOpen.value

	if (isFilterAnaliticsSources.value || !sourceData.value?.components) {
		loadSourceData()
	}
	emit('update-open-accordions-source', { isOpen: isOpen.value, id: props.source.id });
}

function onFilter(fields) {
	appliedFilters.value = fields
}

function onCloseFilters() {
	filtersIsOpen.value = false
}

async function onSelectReaction(reaction) {
	try {
		if (reaction?.id === selectedReaction.value?.id) {
			await removeReactionSource({
				dossierId: dossieId.value,
				sourceId: props.source.id,
			})
			removeReaction(props.source.id)
		} else {
			await addReactionSource({
				dossierId: dossieId.value,
				sourceId: props.source.id,
				reactionId: reaction.id,
			})
			addReaction(props.source.id, reaction)
			start()
		}

		reactionIsOpen.value = false
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}
function sourceHasFilters() {
	return Object.keys(sourceData.value?.filters || {}).length > 0
}

function convertFilters() {
	if (!sourceData.value?.filters) return
	const convertedFilters = sourceData.value?.filters?.map((filter) => {
		if (
			filter.values &&
			typeof filter.values === 'object' &&
			!Array.isArray(filter.values)
		) {
			const convertedValues = Object.entries(filter.values).map(
				([key, value]) => [key, value]
			)
			filter.values = convertedValues
		}
		return filter
	})

	return convertedFilters
}

const showLoadingSkeleton = ref(false)
const filtersStore = useFiltersStore()

function loadSourceData() {
	showLoadingSkeleton.value = true

	const filters = filtersStore.filters

	if (props.isMock) {
		testTemplate(props.source.id)
			.then((data) => {
				sourceData.value = {
					sourceId: props.source.id,
					...data,
				}
			})
			.finally(() => {
				showLoadingSkeleton.value = false
			})
		return
	}

	getSource(route.params.dossierId, props.source.id, isFilterAnaliticsSources.value ? filters : undefined)
		.then((data) => {
			sourceData.value = data
		})
		.catch((error) => {
			if (error?.response) {
				toast.addToast({
					content: error.response?.data.message,
					type: 'error',
				})
			}
		})
		.finally(() => {
			showLoadingSkeleton.value = false
		})
}

onMounted(() => stop())
onMounted(() => changeSelectedTag())

onMounted(() => {
	if (focusedSource?.value) focusedSourceList()
})

onMounted(() => {
	const initialReaction = getReaction(props.source?.reactions)
	const reactionFromStore = findReaction(props.source.id)

	// When switching tabs, this component is rebuilt again.
	// Therefore, I need to check if it's undefined
	// to determine whether the reaction has passed through the store at some point or not.
	if (reactionFromStore === undefined) {
		addReaction(props.source.id, initialReaction)
	}
})
</script>

<style lang="scss" scoped>
@import './AccordionSource.scss';
</style>
