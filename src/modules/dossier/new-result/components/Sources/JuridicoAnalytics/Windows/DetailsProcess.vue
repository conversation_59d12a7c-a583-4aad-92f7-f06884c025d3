<template>
	<div>
		<span class="description">{{
			description || $t('result.checkProcesses')
		}}</span>

		<div v-if="cardData.length" class="card-quantitative">
			<Quantitative
				v-for="(card, index) in cardData"
				:key="index"
				:icon="card.icon"
				:value="card.value"
				:description="card.description"
				:width="card.width" />
		</div>

		<table class="table-container" aria-label="details-process">
			<thead>
				<tr>
					<th class="col-uf">{{ $t('result.uf') }}</th>
					<th class="col-title">{{ $t('result.processTitle') }}</th>
					<th>{{ $t('result.processNumber') }}</th>
					<th>{{ $t('result.processValue') }}</th>
					<th>{{ $t('result.participation') }}</th>
					<th class="text-center download-all">
						<button @click="downloadAllProcesses" :disabled="isLoadingAll">
							<template v-if="!isLoadingAll">
								{{ $t('result.downloadAll') }}
							</template>
							<template v-else>
								<Loading color="#7922B9" width="3" size="16" />
							</template>
						</button>
					</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(item, index) in getProcessData()" :key="index">
					<td class="truncate col-uf" :title="item.uf">{{ item.uf }}</td>
					<td class="truncate col-title" :title="item.titulo">
						{{ item.titulo }}
					</td>
					<td class="truncate" :title="item.num">{{ item.num }}</td>
					<td class="truncate" :title="item.valor">{{ item.valor }}</td>
					<td class="truncate" :title="item.participacao">
						{{ item.participacao }}
					</td>
					<td>
						<div class="actions-column">
							<button
								class="visibility"
								@click="handleDetailsSource(item.sourceId)">
								<NewInlineSvg
									name="visibility.svg"
									width="16"
									height="16"
									fill="#7922B9" />
								{{ $t('result.details') }}
							</button>
							<div class="export">
								<button @click="downloadProcess(item.num, index)">
									<template v-if="!loadingStates[index]">
										<NewInlineSvg
											name="arrow_downward_alt.svg"
											width="12"
											height="12"
											fill="#7922B9" />
									</template>
									<template v-else>
										<Loading color="#7922B9" width="3" size="16" />
									</template>
								</button>
							</div>
						</div>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'
import { onMounted, ref, inject } from 'vue'
import Quantitative from '../Graphics/Quantitative.vue'
import { setDownloadPdfProcess } from '../../../../services/result'
import Loading from '@/components/Loading/Loading.vue'
import { useToast } from '@aplicativo/uplexis-ui'

const { t } = useI18n()

const props = defineProps({
	process: { type: [Array, Object], required: true },
	type: { type: String, default: null },
	total: { type: [String, Number], default: null },
})

const emit = defineEmits(['item-selected'])

const description = ref(null)
const toast = useToast()
const dossierId = inject('dossie-id')

const loadingStates = ref({})
const isLoadingAll = ref(false)

function handleDetailsSource(id) {
	emit('item-selected', id)
}

function getType() {
	const type = props.type

	if (type === 'autor') {
		return (description.value = t('result.authorDescription'))
	} else if (type === 'reu') {
		return (description.value = t('result.defendantDescription'))
	} else if (type === 'envolvido') {
		return (description.value = t('result.involvedDescription'))
	}
}

function getProcessData() {
	if (Array.isArray(props.process)) {
		return props.process
	} else if (props.process && props.process.data) {
		return props.process.data
	}
	return []
}

const cardData = ref([])

const updateCardData = () => {
	cardData.value = [
		{
			icon: 'money.svg',
			value: props.total,
			description: t('result.processLike') + ` ${props.type}`,
			width: '160px',
		},
		{
			icon: setTypeIcon(),
			value: props.process.amount || 0,
			description: t('result.totalProcessValue'),
			width: 'auto',
		},
	]
}

function setTypeIcon() {
	const type = props.type

	if (type === 'autor') {
		return 'person.svg'
	} else if (type === 'reu') {
		return 'data_loss_prevention.svg'
	} else if (type === 'envolvido') {
		return 'supervisor_account.svg'
	}
}

function downloadProcessHelper(
	courtProceedings,
	loadingStateRef,
	stateKey = null
) {
	const id = dossierId.value

	if (stateKey !== null) {
		loadingStateRef.value = { ...loadingStateRef.value, [stateKey]: true }
	} else {
		loadingStateRef.value = true
	}

	setDownloadPdfProcess(id, courtProceedings)
		.then(() => {
			toast.addToast({
				content: t('result.pdfProcessing'),
				type: 'success',
			})
		})
		.catch((error) => {
			console.error('Error:', error)
			toast.addToast({
				content:
					error.response?.data?.message || 'Ocorreu um erro desconhecido',
				type: 'error',
			})
		})
		.finally(() => {
			if (stateKey !== null) {
				loadingStateRef.value = { ...loadingStateRef.value, [stateKey]: false }
			} else {
				loadingStateRef.value = false
			}
		})
}

function downloadProcess(process, index) {
	const courtProceedings = [process]
	downloadProcessHelper(courtProceedings, loadingStates, index)
}

function downloadAllProcesses() {
	const allProcess = []

	getProcessData().map((item) => {
		allProcess.push(item.num)
	})

	downloadProcessHelper(allProcess, isLoadingAll)
}

if (props.type) {
	getType()
}

onMounted(() => {
	if (props.type && props.total !== null) {
		updateCardData()
	}
})
</script>

<style scoped>
@import url(./DetailsProcess.scss);
</style>
