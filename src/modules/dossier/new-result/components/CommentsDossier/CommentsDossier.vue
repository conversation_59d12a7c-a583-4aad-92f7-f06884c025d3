<template>
	<TitleBar :borderTop="true">
		<template v-slot:left>
			<NewInlineSvg name="commentList.svg" width="20" height="20" fill="#666666" />

			<h3>{{ $t('result.comments.title') }}</h3>
		</template>
		<template v-slot:content>
			<v-skeleton-loader type="card" v-if="loading"></v-skeleton-loader>
			<div v-else>
				<section class="comment">
					<div class="comment__add">
						<span class="comment__add--title">{{
							$t('result.comments.writeComment')
						}}</span>
						<textarea v-model="comment" name="comment" id="comment"
							:placeholder="$t('result.comments.typeHere')" cols="30" rows="4"></textarea>
					</div>
					<div class="comment__export-option">
						<button @click="toogleRelevant" class="toggle-all-btn">
							<span class="comment__add--title">Enviar esse comentário como relevante</span>
							<NewInlineSvg :name="allRelevant ? 'kid_star_green.svg' : 'star-border.svg'" width="20"
								height="20" :fill="allRelevant ? '#0B8A2D' : '#666666'" />

						</button>
						<UpButton @click="createCommentDossier" v-bind="{
							type: 'primary',
							size: 'medium',
							disabled: comment.length === 0,
							content: 'Desabilitado',
						}">{{ $t('result.comments.send') }}</UpButton>
					</div>
				</section>
				<section v-if="comments.length > 0">
					<ListComments :commentsList="comments" :dossieId="dossieId" @saveEditComment="updateCommentDossier"
						@updateRelevance="updateRelevance" />
				</section>
			</div>
		</template>
	</TitleBar>
</template>

<script setup>
import ListComments from './ListComments/ListComments.vue'

import {
	getComments,
	createComment,
	updateComment,
	markCommentAsRelevant,
} from '../../services/dossie_results'

import TitleBar from '@/components/TitleBar/TitleBar.vue'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'

import { useI18n } from 'vue-i18n'
import { useToast } from '@aplicativo/uplexis-ui'
import { inject, ref, onBeforeMount, computed } from 'vue'

const { t } = useI18n()
const toast = useToast()
const dossieId = inject('dossie-id')
const comment = ref('')
const comments = ref([])
const loading = ref(true)
const isRelevant = ref(false)


const allRelevant = computed(() => isRelevant.value)

function populateComments() {
	loading.value = true
	const id = dossieId.value
	getComments(id)
		.then((response) => {
			comments.value = response
			comment.value = ''
		})
		.catch((error) => {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
			loading.value = false
		})
		.finally(() => {
			loading.value = false
		})
}

function toogleRelevant() {
	isRelevant.value = !isRelevant.value
}

function createCommentDossier() {
	loading.value = true
	const id = dossieId.value
	const commentDossier = comment.value

	createComment(id, commentDossier)
		.then((response) => {
			if (isRelevant.value) {
				return markCommentAsRelevant(id, response[0].comment_id)
			}
			return response
		})
		.then(() => {
			toast.addToast({
				content: t('result.comments.commentCreated'),
				type: 'success',
			})
			populateComments()
			comment.value = ''
		})
		.catch((error) => {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
			loading.value = false
		})
		.finally(() => {
			loading.value = false
		})
}

function updateCommentDossier(payload) {
	loading.value = true
	const id = dossieId.value
	const idComment = payload.newCommentRaw.comment_id
	const newComment = payload.newComment
	updateComment(id, newComment, idComment)
		.then(() => {
			populateComments()
			toast.addToast({
				content: t('result.comments.commentEdited'),
				type: 'success',
			})
		})
		.catch((error) => {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
			loading.value = false
		})
		.finally(() => {
			loading.value = false
		})
}

function updateRelevance() {
	populateComments()
}

onBeforeMount(() => {
	populateComments()
})
</script>

<style scoped>
@import './CommentsDossier.scss';
</style>
