<template>
	<div class="edit-comment" v-if="newComment?.length > 0">
		<div class="edit-comment__title">
			<span>{{ $t('result.comments.madeComments') }}</span>
			<span>{{ $t('result.comments.name') }}</span>
			<span>{{ $t('result.comments.commentDate') }}</span>
		</div>
		<div class="edit-comment__new">
			<textarea
				v-model="newComment"
				name="new-comment"
				id="new-comment"
				:placeholder="$t('result.comments.typeHere')"
				cols="30"
				rows="4"></textarea>

			<div class="edit-comment__footer">
				<UpButton
					@click="save"
					v-bind="{
						type: 'primary',
						size: 'medium',
						disabled: false,
						content: 'Desabilitado',
					}"
					>{{ $t('result.comments.send') }}</UpButton
				>
			</div>
		</div>
	</div>
	<div class="toggle-all-btn-container">
		<button @click="toggleAllRelevance" class="toggle-all-btn">
			<NewInlineSvg
				:name="allRelevant ? 'kid_star_green.svg' : 'star-border.svg'"
				width="24"
				height="24"
				:fill="allRelevant ? '#0B8A2D' : '#666666'" />
			<span>{{ allRelevant ? $t('result.comments.unmarkAllRelevant') : $t('result.comments.markAllRelevant') }}</span>
		</button>
	</div>
	<div class="table">
		<table class="table-container" aria-label="comments-dossier">
			<thead>
				<tr>
					<th>{{ $t('result.comments.madeComments') }}</th>
					<th></th>
					<th>{{ $t('result.comments.name') }}</th>
					<th>{{ $t('result.comments.commentDate') }}</th>
				</tr>
			</thead>

			<tbody>
				<tr v-for="(item, index) in commentsList" :key="index">


					<td>
						<div class="cell-content">
						<button 
							class="star-button" 
							@click="toggleRelevance(item)"
							:class="{ 'star-active': item.is_relevante }">
							<NewInlineSvg
								:name="item.is_relevante ? 'kid_star_green.svg' : 'star-border.svg'"
								width="20"
								height="20"
								:fill="item.is_relevante ? '#0B8A2D' : '#666666'" />
						</button>
						<span class="truncate" :title="item.comentario">{{
								item.comentario
							}}</span>
							</div>
					</td>

					
					<td>
						<div class="cell-content">
							
							<button
								v-if="item.my_comment"
								@click="editComment(item)"
								class="flex end">
								<NewInlineSvg
									name="pencil.svg"
									width="20"
									height="20"
									fill="#7922B9" />
							</button>
						</div>
					</td>


					<td>
						<div class="truncate" :title="item.creator">
							{{ item.creator }}
						</div>
					</td>

					
					<td class="truncate" :title="item.data">{{ item.data }}</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script setup>
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'
import { ref, computed } from 'vue'
import { 
	markCommentAsRelevant, 
	unmarkCommentAsRelevant,
	toggleAllCommentsRelevance 
} from '../../../services/dossie_results'

const emits = defineEmits(['saveEditComment', 'updateRelevance'])

const props = defineProps({
	commentsList: { type: Array, required: false },
	dossieId: { type: String, required: true }
})

const newComment = ref(null)
const newCommentRaw = ref(null)

const allRelevant = computed(() => {
	if (!props.commentsList?.length) return false
	return props.commentsList.every(comment => comment.is_relevante)
})

function editComment(comment) {
	newCommentRaw.value = comment
	newComment.value = comment.comentario
}

function save() {
	const payload = {
		newComment: newComment.value,
		newCommentRaw: newCommentRaw.value,
	}
	emits('saveEditComment', payload)
	newComment.value = null
}

async function toggleRelevance(comment) {
	try {
		const dossieId = props.dossieId
		const commentId = comment.comment_id

		if (comment.is_relevante) {
			await unmarkCommentAsRelevant(dossieId, commentId)
		} else {
			await markCommentAsRelevant(dossieId, commentId)
		}
		
		comment.is_relevante = !comment.is_relevante
		emits('updateRelevance')
	} catch (error) {
		console.error('Erro ao alterar relevância:', error)
	}
}

async function toggleAllRelevance() {
	try {
		const newValue = !allRelevant.value
		await toggleAllCommentsRelevance(props.dossieId, newValue)
		
		props.commentsList.forEach(comment => {
			comment.is_relevante = newValue
		})
		
		emits('updateRelevance')
	} catch (error) {
		console.error('Erro ao alterar relevância de todos:', error)
	}
}

</script>

<style scoped>
@import './ListComments.scss';
</style>
