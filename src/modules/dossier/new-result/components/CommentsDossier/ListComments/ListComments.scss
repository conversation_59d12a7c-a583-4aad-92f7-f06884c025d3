.table {
	width: 100%;
	font-size: 12px;
	
	.table-container {
		width: 100%;
		border-collapse: collapse;
		
		th {
			padding: 10px 11px;
			text-align: left;
			font-weight: 500;
			color: #000000;
			white-space: nowrap;
			// border-bottom: 1px solid #E0E0E0;

			&:first-child {
				width: 40px;
			}

			&:nth-child(2) {
				width: 100%;
				justify-content: space-between;
			}

			&:nth-child(3) {
				width: 30%;
			}

			&:nth-child(4) {
				width: 20%;
				text-align: right;
			}
		}

		td {
			padding: 12px 16px;
			border-bottom: 1px solid #E0E0E0;
			color: #777777;
			background: #FFFFFF;

			&:first-child {
				width: 40px;
				padding: 12px 8px;
			}

			&:nth-child(4) {
				text-align: right;
			}

			.cell-content {
				display: flex;
				align-items: center;
				gap: 2px;
				width: 100%;

				.truncate {
					flex: 1;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					min-width: 0;
				}

				button {
					background: none;
					border: none;
					padding: 4px;
					cursor: pointer;
					display: flex;
					align-items: center;
					margin-left: auto;
					flex-shrink: 0;
				}
			}
		}

		tr:hover td {
			background: #F5F5F5;
		}
	}
}

.star-button {
	background: none;
	border: none;
	padding: 4px;
	cursor: pointer;
	display: flex;
	align-items: center;
	justify-content: center;
}

.relevance-column {
	width: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8px;

	span {
		font-size: 12px;
		color: #666666;
	}

}
.toggle-all-btn-container{
	
	display: flex;
	background-color: white;
}
.toggle-all-btn {
	margin-bottom: 4px;
	margin-left: 4px;
	margin-top: 4px;
	background: white;
	border: none;
	padding: 4px 8px;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 4px;
	color: #666666;
	border-radius: 4px;
	// transition: background-color 0.2s;
	font-family: Poppins;
	font-weight: 600;
	font-size: 10px;
	line-height: 14px;


	&:hover {
		background: #F0F0F0;
	}

	svg {
		margin-left: 4px;
	}
}

.truncate {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 300px;
}

#edit-comment {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	background: var(--Light-neutral-light-pure, #fff);

	resize: none;
	user-select: none;
}

.edit-comment {
	padding: 0px 16px 16px 16px;
}

.edit-comment__title {
	display: flex;
	width: 100%;
	justify-content: space-between;
	background: var(--Light-neutral-light-03, #f0f0f0);
	color: var(--Dark-neutral-dark-pure, #121212);
	font-size: 10px;
	font-style: normal;
	font-weight: 600;
	line-height: 14px;
	padding: 10px 11px;
	font-variant-numeric: lining-nums proportional-nums;
	box-sizing: border-box;
}

.edit-comment__title span:first-child {
	flex: 0 0 65%;
}

.edit-comment__title span:nth-child(2) {
	flex: 0 0 15%;
}

.edit-comment__title span:nth-child(3) {
	flex: 0 0 15%;
}

.edit-comment__new {
	width: 100%;
}

.edit-comment__new textarea {
	width: 100%;
	padding: 8px;
	box-sizing: border-box;
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	background: var(--Light-neutral-light-pure, #fff);

	resize: none;
	user-select: none;
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
}

.edit-comment__footer {
	display: flex;
	justify-content: end;
}

.star-button:hover {
	opacity: 0.8;
}

.star-active {
	color: #4CAF50;
}