<template>
	<template v-if="loaderGeneral">
		<div style="
				height: 100dvh;
				width: 100dvw;
				display: flex;
				align-items: center;
				justify-content: center;
			">
			<LoadingUplexis />
		</div>
	</template>

	<div v-else class="result">
		<MainContainer class="result__menu">
			<div class="result__menu">
				<div class="result__options">
					<div class="options__tabs">
						<div class="result__header__title">
							<RouterLink to="/dossiers">
								<NewInlineSvg name="back.svg" width="20" height="20" fill="#666666" />
							</RouterLink>
							<h1>
								{{ $t('result.headerTitle') }}
								{{ formatNumberDossier(dossierId) }}
							</h1>
						</div>
						<Switch class="toggle" v-model="isDetailed" @change="handleSwitchChange"
							:onLabel="$t('result.detailedResult')" :offLabel="$t('result.summaryResult')" />
					</div>
					<div class="options__infos">
						<UpButton class="btn__nav" v-bind="{
							type: 'tertiary',
							size: 'medium',
							disabled: false,
							content: 'Tertiary',
						}" @click="scrollToDossierInformation">
							<NewInlineSvg name="arrow_up_ward.svg" width="12" height="12" fill="#7922B9" />
							{{ $t('result.start') }}
						</UpButton>

						<UpButton @click="scrollToAnalisis" class="btn__nav" v-bind="{
							type: 'tertiary',
							size: 'medium',
							disabled: false,
							content: 'Tertiary',
						}">
							<NewInlineSvg name="has_comments.svg" width="24" height="24" fill="#7922B9" />
							{{ $t('result.AnalysisComments') }}
						</UpButton>

						<UpButton v-if="informationDossier?.derivados.length > 0" @click="openModalDerivation"
							class="btn__nav" v-bind="{
								type: 'tertiary',
								size: 'medium',
								disabled: false,
								content: 'Tertiary',
							}">
							<NewInlineSvg name="derivation.svg" width="22" height="22" fill="#7922B9" />
							{{ $t('result.derivations') }}
						</UpButton>

						<UpButton v-if="dossierHistorical?.length > 0" @click="openHistoricalDossier" class="btn__nav"
							v-bind="{
								type: 'tertiary',
								size: 'medium',
								disabled: false,
								content: 'Tertiary',
							}">
							<NewInlineSvg name="historic.svg" width="24" height="24" fill="#7922B9" />
							{{ $t('result.dossierHistory') }}
						</UpButton>
						<UpButton class="btn__nav" @click="exportPdfDossier" :class="{ disabled: !isExportDossier }"
							:disabled="!isExportDossier" v-bind="{
								type: 'tertiary',
								size: 'medium',
								content: 'Tertiary',
							}">
							<NewInlineSvg name="export.svg" width="24" height="24"
								:fill="!isExportDossier ? '#666666' : '#7922B9'" />
							{{ $t('result.export') }}
						</UpButton>
					</div>
				</div>

				<div v-if="sortedSources" class="result__menu-sources">
					<ViewNavigationMenu :sources="sortedSources" />
				</div>
			</div>
		</MainContainer>

		<div>
			<template v-if="showLoader">
				<div style="
						height: 100dvh;
						width: 100dvw;
						display: flex;
						align-items: center;
						justify-content: center;
					">
					<LoadingUplexis />
				</div>
			</template>

			<div v-if="!showLoader">
				<div style="margin-top: -32px; padding: 16px 0px">
					<MainContainer background="#F0F0F0" padding="16px 71px" v-if="informationDossier"
						id="dossier-information">
						<DossierInformation :info="informationDossier" :workflow="detailsWorkflow"
							:upFlag="detailsUpFlag" :sourceWithResult="detailsSourcesWithResults"
							:sourceWithoutResult="detailsSourcesWithoutResults" :upScore="detailsUpScore"
							:analysisWorkflow="analysisWorkflow" @updateWorkflow="setAnalysisWorkflow" />
					</MainContainer>
				</div>

				<div style="margin-top: -16px" v-if="isSummaryResult">
					<MainContainer padding="16px 71px">
						<div v-if="hasFoxyIA">
							<FoxyIA :dataFoxy="resultFoxyIa" :isLoading="isLoadingFoxy"
								@update:isLoading="updateLoadingState" />
						</div>

						<div v-for="(group, index) in sortedSources" :key="index">
							<AccordionSummary v-if="
								group.fontes.length > 0 &&
								group.alias !== 'juridico' &&
								group.alias !== 'midia_internet'
							" :category="group" :id="group.alias">
								<div class="source-list" v-if="
									group.alias !== 'juridico' &&
									group.alias !== 'midia_internet'
								">
									<CategoryConsultedSource :fontSummary="group" @focused="onFocus" />
								</div>
							</AccordionSummary>
							<AccordionSummary :category="group" :id="group.alias"
								v-if="group.alias === 'juridico' && hasSourcesJuridicasSummary">
								<template v-if="isLoadingJuridicoAnalytics">
									<v-skeleton-loader style="width: 100%; height: 400px"
										type="card"></v-skeleton-loader>
								</template>
								<template v-else>
									<JuridicoAnalytics v-if="resultAnalyticsJuridico?.juridico || loadingJuridico"
										:juridicoAnalytics="resultAnalyticsJuridico" :loading="loadingJuridico"
										@filter-update="updateFiltersAnalyticsJuridico"
										@item-selected="handleItemSelectedInPage" />
								</template>
							</AccordionSummary>
							<AccordionSummary :id="group.alias" :category="group" v-if="
								group.alias === 'midia_internet' && hasSourcesMediasSummary
							">
								<template v-if="isLoadingMediasAnalytics">
									<v-skeleton-loader style="width: 100%; height: 400px"
										type="card"></v-skeleton-loader>
								</template>
								<template v-else>
									<MediasAnalytics :dataAnalyticsMedia="dataAnalyticsMedias?.midia_internet_analysis[0].summaries
										" />
								</template>
							</AccordionSummary>
						</div>
					</MainContainer>

					<MainContainer background="#F0F0F0" padding="16px 71px">
						<div class="analysisFinal">
							<AnalysisDossier v-if="analysisDossier" id="analysis" @updateAnalysis="handleAnalysisUpdate"
								:analysis="analysisDossier" :dossieId="informationDossier.dossieID" />

							<div class="comments">
								<CommentsDossier />
							</div>
						</div>
					</MainContainer>
				</div>

				<div v-else>
					<div>
						<MainContainer padding="0px 71px">
							<!-- Fontes detalhadas -->

							<div class="sources">
								<div v-for="(group, groupIndex) in sortedSources" :key="groupIndex">
									<AccordionCategory v-show="group.fontes.length > 0 &&
										!checkWithResult(group) &&
										group.withResult
										" :category="group" :id="group.alias" :quantityResults="quantityWithResult(group.fontes)">
										<div class="source-list">
											<button v-if="!openAllSources[groupIndex]"
												@click="toggleAllAccordionSources(groupIndex, true)"
												class="openSources">
												<span>{{ $t('result.expandAllSources') }}</span>
												<NewInlineSvg name="expandMore.svg" width="20" height="20"
													fill="#7922B9" />
											</button>
											<button v-else @click="toggleAllAccordionSources(groupIndex, false)"
												class="openSources">
												<span>{{ $t('result.collapseAllSources') }}</span>
												<NewInlineSvg name="expandMore.svg" width="20" height="20"
													fill="#7922B9" :class="{ rotated: openAllSources[groupIndex] }" />
											</button>

											<div v-for="(source, fonteIndex) in group.fontes" :key="source.id">
												<AccordionSource v-if="source.withResult" :key="fonteIndex"
													:source="source" :isOpenAllAccordion="accordionStates[`${groupIndex}-${fonteIndex}`]
														" @update-open-accordions-source="
														handleUpdateOpenAccordions(
															groupIndex,
															fonteIndex,
															$event
														)
														" />
											</div>
										</div>
									</AccordionCategory>
								</div>
							</div>
						</MainContainer>

						<div style="margin-top: 16px">
							<MainContainer background="#F0F0F0" padding="16px 71px">
								<div class="analysisFinal">
									<AnalysisDossier id="analysis" @updateAnalysis="handleAnalysisUpdate"
										:analysis="analysisDossier" />

									<div class="comments">
										<CommentsDossier />
									</div>
								</div>
							</MainContainer>
						</div>
					</div>
				</div>
			</div>
		</div>

		<CommonBaseboard />
	</div>

	<Window class="window__historical" @on-close="closeWindow" :is-open="isOpenWindowHistorical"
		title="Histórico de Dossiês">
		<HistoricalDossierWindow :dossiers="dossierHistorical" />
	</Window>
	<Window class="window__derivation" @on-close="closeWindow" :is-open="isOpenWindowDerivation"
		title="Dossiês derivados">
		<DerivationDossierWindow :derivation="informationDossier.derivados" />
	</Window>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import ViewNavigationMenu from './components/ViewNavigationMenu/ViewNavigationMenu.vue'
import HistoricalDossierWindow from './components/HistoricalDossierWindow/HistoricalDossierWindow.vue'
import DerivationDossierWindow from './components/DerivationDossierWindow/DerivationDossierWindow.vue'
import DossierInformation from './components/DossierInformation/DossierInformation.vue'
import AccordionCategory from './components/Sources/AccordionCategory/AccordionCategory.vue'
import AccordionSource from './components/Sources/AccordionSource/AccordionSource.vue'
import CommentsDossier from './components/CommentsDossier/CommentsDossier.vue'
import CategoryConsultedSource from './components/Sources/CategoryConsultedSources/CategoryConsultedSources.vue'
import {
	getInfoDossier,
	getDetailsWorkflow,
	getDetailsUpflag,
	getGroupSources,
	historicalDossier,
	sourcesWithResults,
	sourcesWithoutResults,
	upScore,
	getTags,
	createAnalysis,
	getAnalysisHistory,
	saveAnalysisWorkflow,
	getAnalysisWorkflow,
	getAnalyticsJuridico,
	getFilterAnalyticsJuridico,
	getAnalyticsMedias,
	getFoxyIA,
} from './services/result.js'
import { useTagsStore } from './stores/tags_store.js'
import AnalysisDossier from './components/AnalysisDossier/AnalysisDossier.vue'
import AccordionSummary from './components/Sources/AccordionCategory/AccordionSummary.vue'
import JuridicoAnalytics from './components/Sources/JuridicoAnalytics/JuridicoAnalytics.vue'
import { findSourceByAlias } from './mapper/getSources'
import FoxyIA from './components/FoxyIA/FoxyIA.vue'
import MediasAnalytics from './components/Sources/MediasAnalytics/MediasAnalytics.vue'

import LoadingUplexis from '@/components/LoadingUplexis/LoadingUplexis.vue'
import { formatNumberDossier, validateCNPJ } from '../../../utils/helpers.js'

import { useExportationsStore } from '@/stores/exportationsStore'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'
import MainContainer from '@/components/MainContainer/MainContainer.vue'
import Window from '@/components/Window/Window.vue'
import Switch from '@/components/Switch/Switch.vue'

import { useToast } from '@aplicativo/uplexis-ui'
import { useRoute } from 'vue-router'
import { ref, readonly, provide, onMounted, computed, watch } from 'vue'

import { useUserStore } from '@/stores/userStore'
import { storeToRefs } from 'pinia'
import CommonBaseboard from '../../../components/CommonBaseboard/CommonBaseboard.vue'

const { t } = useI18n()

const route = useRoute()

const toast = useToast()

const dossierId = ref(route.params.dossierId)
provide('dossie-id', readonly(dossierId))

const informationDossier = ref(null)
const detailsWorkflow = ref(null)
const detailsUpFlag = ref(null)
const allSources = ref([])
const isSummaryResult = ref(true)
const showLoader = ref(false)
const dossierHistorical = ref(null)
const isOpenWindowHistorical = ref(false)
const isOpenWindowDerivation = ref(false)
const loaderGeneral = ref(false)
const detailsSourcesWithResults = ref(null)
const detailsSourcesWithoutResults = ref(null)
const detailsUpScore = ref(null)
const tagsStore = useTagsStore()
const analysisDossier = ref(null)
const analysisWorkflow = ref(null)
const isExportDossier = ref(true)
const isDetailed = ref(false)
const resultAnalyticsJuridico = ref('')
const loadingJuridico = ref(false)
const dataAnalyticsMedias = ref()
const resultFoxyIa = ref(null)
const hasFoxyIA = ref(false)
const isLoadingFoxy = ref(false)
const isLoadingJuridicoAnalytics = ref(true)
const isLoadingMediasAnalytics = ref(true)
const sortedSources = computed(() => sortSources(allSources.value))

const userStore = useUserStore()
const { user } = storeToRefs(userStore)

const openAllSources = ref({})
const accordionStates = ref({})

const initializeAccordionStates = (groups) => {

	if (!Array.isArray(groups) || groups.length === 0) {
		return
	}

	groups.forEach((group, groupIndex) => {
		if (group && Array.isArray(group.fontes)) {
			group.fontes.forEach((source, fonteIndex) => {
				if (source.withResult) {
					accordionStates.value[`${groupIndex}-${fonteIndex}`] = false
				}
			})

			openAllSources.value[groupIndex] = false
		}
	})
}

const toggleAllAccordionSources = (groupIndex, isOpen) => {
	openAllSources.value[groupIndex] = isOpen

	Object.keys(accordionStates.value).forEach((key) => {
		if (key.startsWith(`${groupIndex}-`)) {
			accordionStates.value[key] = isOpen
		}
	})
}


const handleUpdateOpenAccordions = (groupIndex, fonteIndex, { isOpen }) => {
	accordionStates.value[`${groupIndex}-${fonteIndex}`] = isOpen


	const groupAccordions = Object.keys(accordionStates.value)
		.filter((key) => key.startsWith(`${groupIndex}-`))
		.map((key) => accordionStates.value[key])

	const allOpen = groupAccordions.every((state) => state === true)
	const allClosed = groupAccordions.every((state) => state === false)


	if (allOpen) {
		openAllSources.value[groupIndex] = true
	} else if (allClosed) {
		openAllSources.value[groupIndex] = false
	}


}

const focusedSource = ref()
provide('focused-source', readonly(focusedSource))

const idSourcesJuridicoSummary = [408, 400, 410]

const hasSourcesJuridicasSummary = ref(false)

const hasSourcesMediasSummary = ref(false)
const idSourcesMediasSummary = [166, 232]
const interprisePlanId = Number(import.meta.env.VITE_APP_PLAN_INTERPRISE)
const professionalPlanId = Number(import.meta.env.VITE_APP_PLAN_PROFESSIONAL)

const handleSwitchChange = () => {
	showLoader.value = true

	setTimeout(() => {
		isSummaryResult.value = !isSummaryResult.value
		isDetailed.value = !isSummaryResult.value
		showLoader.value = false
	}, 500)
}

const onFocus = (source) => {
	handleSwitchChange()
	focusedSource.value = source

	isDetailed.value = !isSummaryResult.value

	setTimeout(() => {
		focusedSource.value = null
	}, 1000)
}

const exportationStore = useExportationsStore()

function exportPdfDossier() {
	const dossier = {
		id: informationDossier.value.dossieID,
		lote: informationDossier.value.loteID,
	}

	isExportDossier.value = false

	exportDossier({ dossier, isDetailed: true })
}

const quantityWithResult = (fontes) => {
	return fontes.filter((source) => source.withResult !== false).length
}

const exportDossier = ({ dossier, isDetailed }) => {

	const baseConfigs = {
		id_lote: dossier.lote,
		id_dossie: dossier.id,
		download: 0,
		graficos_mapas: true,
		comentarios: true,
		foxy_ia: true,
		analyze: {
			relevantes: true
		}
	}

	let detailedConfig = {}

	if (isDetailed) {
		detailedConfig = {
			comentarios: {
				mais_recentes: false,
				relevantes: true,
				workflow: true,
				fontes: true,
			},
			categorias: [],
			anexos: true,
		}
	}

	if (detailedConfig.analyze) {
		delete detailedConfig.analyze
	}

	const exportConfigs = {
		...baseConfigs,
		...detailedConfig
	}

	exportationStore.exportDirectly({
		dossier,
		isDetailed,
		config: exportConfigs,
	}).finally(() => {
		isExportDossier.value = true
		toast.addToast({
			content: t(`result.${exportationStore.pdfProcessing ? 'pdfProcessing' : 'pdfProcessed'}`),
			type: 'success',
		})
	})
}

const scrollToDossierInformation = () => {
	const dossierInfoElement = document.getElementById('dossier-information')
	if (dossierInfoElement) {
		const offset = dossierInfoElement.offsetTop - 400
		window.scrollTo({
			top: offset,
			behavior: 'smooth',
		})
	}
}

const scrollToAnalisis = () => {
	const dossierInfoElement = document.getElementById('analysis')
	if (dossierInfoElement) {
		const offset = dossierInfoElement.offsetTop - 200
		window.scrollTo({
			top: offset,
			behavior: 'smooth',
		})
	}
}

function handleItemSelectedInPage(id) {
	const source = findSourceById(id)
	onFocus(source)
}

function closeWindow() {
	isOpenWindowHistorical.value = false
	isOpenWindowDerivation.value = false
}

function openHistoricalDossier() {
	isOpenWindowHistorical.value = true
}

function openModalDerivation() {
	isOpenWindowDerivation.value = true
}

function checkWithResult(data) {
	return data.fontes.every((source) => source.withResult === false)
}

function updateLoadingState(newLoadingState) {
	isLoadingFoxy.value = newLoadingState
}

async function setFoxy(cnpj) {
	isLoadingFoxy.value = true
	try {
		resultFoxyIa.value = await getFoxyIA(cnpj)
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
		hasFoxyIA.value = false
	} finally {
		isLoadingFoxy.value = false
	}
}

async function updateFiltersAnalyticsJuridico(filters) {
	loadingJuridico
	const id = dossierId.value

	try {
		loadingJuridico.value = true
		const response = await getFilterAnalyticsJuridico(id, filters)
		resultAnalyticsJuridico.value = response
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	} finally {
		loadingJuridico.value = false
	}
}

async function getAnalysisJuridico() {
	const id = dossierId.value
	try {
		await getAnalyticsJuridico(id).then((response) => {
			resultAnalyticsJuridico.value = response

			if (
				Object.keys(resultAnalyticsJuridico.value).length === 0 ||
				!resultAnalyticsJuridico.value.hasOwnProperty('juridico')
			)
				hasSourcesJuridicasSummary.value = false
		})
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
		hasSourcesJuridicasSummary.value = false
	} finally {
		isLoadingJuridicoAnalytics.value = false
	}
}

function getTagsDossier() {
	const id = dossierId.value || route.params.dossierId

	return getTags(id)
		.then((response) => {
			tagsStore.updateTags(response)
		})
		.catch((error) => {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
		})
}

async function getInfo() {
	const id = dossierId.value

	try {
		const response = await getInfoDossier(id)
		informationDossier.value = response

		if (informationDossier.value) {
			getHistoricalDossier()

			if (validateCNPJ(informationDossier.value.criterio)) {
				if (
					user.value.id_plano === interprisePlanId ||
					user.value.id_plano === professionalPlanId
				) {
					hasFoxyIA.value = true

					setFoxy(informationDossier.value.criterio)
				}
			}
		}
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getSourceAnalyticsMedias() {
	const id = dossierId.value
	try {
		const response = await getAnalyticsMedias(id, 'midia_internet')
		dataAnalyticsMedias.value = response
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
		hasSourcesMediasSummary.value = false
	} finally {
		isLoadingMediasAnalytics.value = false
	}
}

async function getWorkflow() {
	const id = dossierId.value
	try {
		await getDetailsWorkflow(id).then((response) => {
			detailsWorkflow.value = response ? response : null
		})
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function setAnalysisWorkflow(data) {
	const status = data.status
	const reason = data.reason
	const id = dossierId.value
	const level = data.level
	try {
		await saveAnalysisWorkflow(id, status, reason, level)
		getWorkflow()
		getAnalysisApproversWorkflow()
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getAnalysisApproversWorkflow() {
	const id = dossierId.value
	try {
		await getAnalysisWorkflow(id).then((response) => {
			analysisWorkflow.value = response
		})
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getUpFlag() {
	const id = dossierId.value
	try {
		await getDetailsUpflag(id).then((response) => {
			detailsUpFlag.value = response ? response : null
		})
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function handleAnalysisUpdate(data) {
	const status = data.status
	const reason = data.reason
	const isRelevant = data.is_relevante
	const id = dossierId.value
	try {
		await createAnalysis(id, reason, status, isRelevant)
		getAnalysis()
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getAnalysis() {
	const id = dossierId.value
	try {
		await getAnalysisHistory(id).then((response) => {
			analysisDossier.value = response
		})
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

const sortSources = (sources) => {
	const priority = [
		'juridico',
		'midia_internet',
		'financeiro',
		'cadastro',
		'reguladores',
		'bens_imoveis',
		'listas_restritivas',
		'profissional',
		'socio_ambiental'
	];

	return sources.sort((a, b) => {
		const indexA = priority.indexOf(a.alias);
		const indexB = priority.indexOf(b.alias);

		if (indexA === -1 || indexB === -1) {
			return 0;
		}

		return indexA - indexB;
	});
};
async function getSources() {
	const id = dossierId.value
	try {
		await getGroupSources(id).then((response) => {
			allSources.value = sortSources(response)
		})

		hasSourcesJuridicasSummary.value = findSourceByAlias(
			allSources.value,
			'juridico',
			idSourcesJuridicoSummary
		)

		hasSourcesMediasSummary.value = findSourceByAlias(
			allSources.value,
			'midia_internet',
			idSourcesMediasSummary
		)

		if (hasSourcesJuridicasSummary.value) getAnalysisJuridico()
		if (hasSourcesMediasSummary.value) getSourceAnalyticsMedias()
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getHistoricalDossier() {
	const id = dossierId.value
	const criteria = informationDossier.value.criterio

	try {
		await historicalDossier(id, criteria).then((response) => {
			dossierHistorical.value = response.data
		})
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getSourcesWithResults() {
	const id = dossierId.value

	try {
		await sourcesWithResults(id).then((response) => {
			detailsSourcesWithResults.value = response
		})
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getSourceswithoutResults() {
	const id = dossierId.value

	try {
		await sourcesWithoutResults(id).then((response) => {
			detailsSourcesWithoutResults.value = response
		})
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function getUpScore() {
	const id = dossierId.value

	try {
		await upScore(id).then((response) => {
			detailsUpScore.value = response
		})
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

async function loadData() {
	loaderGeneral.value = true
	try {
		await Promise.all([
			getInfo(),
			getWorkflow(),
			getUpFlag(),
			getSources(),

			getSourcesWithResults(),
			getSourceswithoutResults(),
			getUpScore(),
			getTagsDossier(),
			getAnalysis(),
			getAnalysisApproversWorkflow(),
		])
	} catch (error) {
		toast.addToast({
			content: error.response.data.message || 'Erro ao carregar dados!',
			type: 'error',
		})
	} finally {
		loaderGeneral.value = false
	}
}

function findSourceById(id) {
	for (const category of allSources.value) {
		for (const source of category.fontes) {
			if (source.id == id) {
				return { ...source, category: category.alias }
			}
		}
	}
	return null
}

watch(
	sortedSources,
	(newSources) => {
		initializeAccordionStates(newSources)
	},
	{ immediate: true }
)

onMounted(async () => {
	const sourceToNavigateTo = route.query.sourceToNavigateTo

	await loadData()

	initializeAccordionStates(sortedSources)

	if (sourceToNavigateTo) {
		const sourceData = findSourceById(sourceToNavigateTo)
		if (sourceData) {
			onFocus(sourceData)
		}
	}
})
</script>

<style scoped>
@import url(./Page.scss);
</style>
