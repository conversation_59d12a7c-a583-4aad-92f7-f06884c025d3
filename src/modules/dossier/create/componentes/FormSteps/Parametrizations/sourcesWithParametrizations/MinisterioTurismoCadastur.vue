<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.ministerioTurismoTitle'),
				value: 'MinistérioTurismoCadastrur',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #MinistérioTurismoCadastrur>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div style="min-width: 430px">
					<label class="text-usu text-neutral-dark-3">
						{{ $t('create.parametrizations.state') }}
					</label>
					<CustomSelect
						v-model="selectedOption"
						:options="options"
						min-width="100%"
						:allow-multiple="false"
						:searchable="false"
						placeholder="Selecione">
						<template #selected-option="selectedOption">
							<span
								class="text-xxxs text-neutral-dark-3 text-left"
								style="
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									padding: 12px 8px;
								">
								{{ selectedOption.label }}
							</span>
						</template>

						<template v-slot:option="option">
							<div
								class="text-xxxs text-neutral-dark-3 text-left"
								style="
									border-bottom: 1px solid #ccc;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									padding: 12px 8px;
								">
								{{ option.label }}
							</div>
						</template>
					</CustomSelect>
				</div>
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="selectedOption"
						source="MinisterioTurismoCadastur"
						@select-parametrization="setParametrization" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import PreParametrization from './PreParametrization.vue'

import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import CustomSelect from '@/modules/dossier/list/components/CustomSelect.vue'

import { onBeforeMount, ref, watch } from 'vue'

const options = ref([])
const selectedOption = ref(null)

const emits = defineEmits(['selectParametrization'])

watch(selectedOption, (newValue) => {
	emits('selectParametrization', { uf: newValue?.valor })
})

const setParametrization = (parametrization) => {
    const ufValue = parametrization?.parametros?.valor 
        ? String(parametrization.parametros.valor) 
        : parametrization?.parametros?.uf 
            ? String(parametrization.parametros.uf) 
            : undefined;

    if (ufValue) {

        const matchedOption = options.value.find(option => String(option.valor) === ufValue);

        if (matchedOption) {
            selectedOption.value = matchedOption;
        } 
    } 
};




onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('MinisterioTurismoCadastur')

		options.value = data.parametro.UF
	} catch (error) {
		console.error(error)
	}
})
</script>

<style lang="scss" scoped>
.custom-select {
	width: 100%;
	padding: 8px;
	border: 1px solid #ccc;
	border-radius: 4px;
}
.options {
	display: flex;
	align-items: center;
}
.options__label {
	margin-right: 10px;
}
</style>
