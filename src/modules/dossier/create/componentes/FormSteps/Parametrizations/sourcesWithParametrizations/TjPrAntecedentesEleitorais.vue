<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: 'Antecedentes 2 Grau - TjPr',
				value: 'TjPrAntecedentesEleitorais',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #TjPrAntecedentesEleitorais>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div class="option">
					<span>{{ $t('create.parametrizations.fullName') }}</span>

					<input
						type="text"
						v-model="name"
						:placeholder="$t('create.parametrizations.fullName')"
						@input="onInputNameChange" />

					<span>{{ $t('create.parametrizations.motherFullName') }}</span>
					<input
						type="text"
						v-model="nameMother"
						:placeholder="$t('create.parametrizations.motherFullName')"
						@input="onInputNameMotherChange" />
				</div>
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="selectedOption"
						source="TjPrAntecedentesEleitorais"
						@select-parametrization="setParametrization" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import PreParametrization from './PreParametrization.vue'
import { ref, watch } from 'vue'

const selectedOption = ref(null)
const name = ref(null)
const nameMother = ref(null)

const emits = defineEmits(['selectParametrization'])

watch([name, nameMother], ([newName, newMotherName]) => {
  selectedOption.value = {
	parametros: {
		name: newName, motherName: newMotherName 
	}
  }
  emits('selectParametrization', selectedOption.value)
})

const setParametrization = (parametrization) => {

	if(parametrization.parametros.parametros) {
		name.value = parametrization.parametros.parametros.name
		nameMother.value = parametrization.parametros.parametros.motherName
	} else {
		name.value = parametrization.parametros.name
		nameMother.value = parametrization.parametros.motherName
	}

}

const onInputNameChange = (event) => {
	name.value = event.target.value
}

const onInputNameMotherChange = (event) => {
	nameMother.value = event.target.value
}

</script>

<style scoped>
.option {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.option > span {
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

input {
	width: 300px;
	border-radius: 4px;
	padding: 8px 16px;
	border-radius: 4px !important;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	background: var(--Light-neutral-light-pure, #fff);

	color: var(--Dark-neutral-dark-03, #666);

	
	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px; 
}
</style>
