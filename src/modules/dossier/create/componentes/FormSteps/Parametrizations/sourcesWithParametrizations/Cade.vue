<template>
	<UpAccordion
	  class="up-accordion-pessoa-gold-boa-vista"
	  :items="[
		{
		  title: $t('create.parametrizations.cadeTitle'),
		  value: 'cade',
		},
	  ]">
	  <template v-slot:description>
		<UpBadge
		  style="background-color: #c371ff"
		  v-bind="{
			block: false,
			disabled: false,
			type: 'custom',
			size: 'sm',
			variant: 'secondary',
			content: 'Secondary Badge',
		  }">
		  <p>{{ $t('create.parametrizations.mandatory') }}</p>
		</UpBadge>
	  </template>
  
	  <template #cade>
		<div style="padding-top: 16px" class="flex items-center justify-between">
		  <div>
			<UpCheckbox
			  class="check"
			  style="margin-bottom: 22px"
			  :label="$t('create.parametrizations.relevanceCheckboxCade')"
			  :disabled="false"
			  v-model:checked="queriesAutomaticallyAsRelevant" />
  
			<div class="flex gap-4 items-center">
			  <div>
				<span class="title">{{ $t('create.parametrizations.exactSearchLabel') }}:</span>
				<UpCheckbox
				  class="check"
				  style="margin-top: 8px"
				  :label="$t('create.parametrizations.exactNameSearch')"
				  :disabled="false"
				  v-model:checked="exactNameSearch" />
			  </div>
			  <div>
				<span class="title">{{ $t('create.parametrizations.searchInLabel') }}</span>
				<div class="flex items-center gap-3 content">
				  <UpCheckbox
					class="check"
					:label="$t('create.parametrizations.processesLabel')"
					:disabled="false"
					v-model:checked="researchProcesses" />
  
				  <UpCheckbox
					class="check"
					:label="$t('create.parametrizations.generatedDocumentsLabel')"
					:disabled="false"
					v-model:checked="researchGeneratedDocuments" />
  
				  <UpCheckbox
					class="check"
					:label="$t('create.parametrizations.externalDocumentsLabel')"
					:disabled="false"
					v-model:checked="researchExternalDocuments" />
				</div>
			  </div>
			</div>
		  </div>
		  <div style="width: 400px">
			<PreParametrization
			  :user-selected-parametrization="form"
			  source="cade"
			  @select-parametrization="setParametrization" />
		  </div>
		</div>
	  </template>
	</UpAccordion>
  </template>
  
  <script setup>
  import { ref, watch, onBeforeMount } from 'vue';
  import PreParametrization from './PreParametrization.vue';
  import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations';
  
  const emits = defineEmits(['selectParametrization']);
  
  const exactNameSearch = ref(false);
  const queriesAutomaticallyAsRelevant = ref(false);
  const researchProcesses = ref(false);
  const researchGeneratedDocuments = ref(false);
  const researchExternalDocuments = ref(false);
  
  const form = ref({
	auto_relevante: false,
	parametros: {
	  target: false,
	  pesquisa: [],
	  auto_relevante_label: '',
	  exact_search_label: '',
	  exact_name_search_label: '',
	  pesquisa_options: [],
	  consulta_options: [],
	},
  });


  let consultaLabels = {};
  let pesquisaLabels = {};
  
  const setMappingFromData = (data) => {
	consultaLabels = {};
	pesquisaLabels = {};
  
	if (data.consulta) {
	  data.consulta.forEach((item) => {
		consultaLabels[item.label] = item.id;
	  });
	}
  
	if (data.pesquisa) {
	  data.pesquisa.forEach((item) => {
		pesquisaLabels[item.label] = item.id;
	  });
	}
  };
  
  const setParametrization = (parametrization) => {

 
  const parametrosInternos = parametrization.parametros.parametros || parametrization.parametros;

  if (!parametrosInternos) {
    return; 
  }

  form.value.parametros = {
    ...form.value.parametros,
    ...parametrosInternos,
  };


  if (parametrosInternos.consulta && Array.isArray(parametrosInternos.consulta)) {
    queriesAutomaticallyAsRelevant.value = parametrosInternos.consulta.includes(consultaLabels['Marcar consultas automáticamente como relevantes'] || 198);
    exactNameSearch.value = parametrosInternos.consulta.includes(consultaLabels['Consulta Critério Nome Exato'] || 199);
  } else {

    queriesAutomaticallyAsRelevant.value = false;
    exactNameSearch.value = false;
  }


  if (parametrosInternos.pesquisa && Array.isArray(parametrosInternos.pesquisa)) {
    researchProcesses.value = parametrosInternos.pesquisa.includes(pesquisaLabels['Processos'] || 167);
    researchGeneratedDocuments.value = parametrosInternos.pesquisa.includes(pesquisaLabels['Documentos Gerados'] || 168);
    researchExternalDocuments.value = parametrosInternos.pesquisa.includes(pesquisaLabels['Documentos Externos'] || 169);
  } else {
 
    researchProcesses.value = false;
    researchGeneratedDocuments.value = false;
    researchExternalDocuments.value = false;
  }

  form.value = { ...form.value };
};


  
watch(
  [
    queriesAutomaticallyAsRelevant,
    exactNameSearch,
    researchProcesses,
    researchGeneratedDocuments,
    researchExternalDocuments,
  ],
  (
    [
      newQueriesAutomaticallyAsRelevant,
      newExactNameSearch,
      newResearchProcesses,
      newResearchGeneratedDocuments,
      newResearchExternalDocuments,
    ]
  ) => {


    form.value.auto_relevante = false;
	form.value.parametros.target = false;

    form.value.parametros.pesquisa = [];
    form.value.parametros.consulta = [];


    if (newResearchProcesses && pesquisaLabels['Processos']) {
      form.value.parametros.pesquisa.push(pesquisaLabels['Processos']);
    }
    if (newResearchGeneratedDocuments && pesquisaLabels['Documentos Gerados']) {
      form.value.parametros.pesquisa.push(pesquisaLabels['Documentos Gerados']);
    }
    if (newResearchExternalDocuments && pesquisaLabels['Documentos Externos']) {
      form.value.parametros.pesquisa.push(pesquisaLabels['Documentos Externos']);
    }


    if (newQueriesAutomaticallyAsRelevant && consultaLabels['Marcar consultas automáticamente como relevantes']) {
	  form.value.auto_relevante = true;
      form.value.parametros.consulta.push(consultaLabels['Marcar consultas automáticamente como relevantes']);
    }
    if (newExactNameSearch && consultaLabels['Consulta Critério Nome Exato']) {
	  form.value.parametros.target = true;
      form.value.parametros.consulta.push(consultaLabels['Consulta Critério Nome Exato']);
    }

    emits('selectParametrization', {
      auto_relevante: form.value.auto_relevante,
      parametros: {
        target: form.value.parametros.target,
        pesquisa: form.value.parametros.pesquisa,
        consulta: form.value.parametros.consulta,
      },
    });
  },
  { deep: true }
);

  
  onBeforeMount(async () => {
	try {
	  const { data } = await getParametrizationFor('cade');

	  if (data.parametro) {
		setMappingFromData(data.parametro);
	  }
  
	  if (data.consulta) {
		form.value.parametros.consulta_options = data.consulta;
	  }
  
	  if (data.pesquisa) {
		form.value.parametros.pesquisa_options = data.pesquisa;
	  }
	} catch (error) {
	  console.error(error);
	}
  });
  </script>
  
  <style scoped>
  .title {
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
  }
  
  .check {
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
  }
  
  :deep(.up-checkbox__text) {
	color: #666666 !important;
  }
  
  .content {
	margin-top: 8px;
  }
  </style>
  