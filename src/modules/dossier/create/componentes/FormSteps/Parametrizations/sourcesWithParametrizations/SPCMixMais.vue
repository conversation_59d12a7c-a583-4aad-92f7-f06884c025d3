<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: t('create.parametrizations.spcMixMaisTitle'),
				value: 'SPCMixMais',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #SPCMixMais>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div class="options">
					<UpCheckbox
						:label="t('create.parametrizations.participationInCompanies')"
						:disabled="false"
						v-model:checked="
							selectedOption[
								t('create.parametrizations.participationInCompanies')
							]
						" />

					<UpCheckbox
						:label="t('create.parametrizations.societalControl')"
						:disabled="false"
						v-model:checked="
							selectedOption[t('create.parametrizations.societalControl')]
						" />

					<UpCheckbox
						:label="t('create.parametrizations.action')"
						:disabled="false"
						v-model:checked="
							selectedOption[t('create.parametrizations.action')]
						" />
				</div>
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="selectedOption"
						source="spcLocalizaMix"
						@select-parametrization="setParametrization" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import PreParametrization from './PreParametrization.vue'
import { useI18n } from 'vue-i18n'
import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import { ref, watch, onBeforeMount } from 'vue'

const { t } = useI18n()

const selectedOption = ref({})
const optionIdMapping = ref({})

const initializeOptions = () => {
	selectedOption.value = {
		[t('create.parametrizations.participationInCompanies')]: false,
		[t('create.parametrizations.societalControl')]: false,
		[t('create.parametrizations.action')]: false,
	}

	optionIdMapping.value = {
		[t('create.parametrizations.participationInCompanies')]: null,
		[t('create.parametrizations.societalControl')]: null,
		[t('create.parametrizations.action')]: null,
	}
}

initializeOptions()

const emits = defineEmits(['selectParametrization'])

watch(
	selectedOption,
	(newValue) => {
		const selectedIds = Object.keys(newValue)
			.filter((key) => newValue[key])
			.map((key) => optionIdMapping.value[key])
			.filter((id) => id !== null)

		emits('selectParametrization', selectedIds)
	},
	{ deep: true }
)

const setParametrization = (parametrization) => {

    let updatedOption = { ...selectedOption.value };

    if (Array.isArray(parametrization.parametros)) {
        parametrization.parametros.forEach((paramId) => {
            const label = Object.keys(optionIdMapping.value).find(
                (key) => optionIdMapping.value[key] === paramId
            );
            if (label) {
                updatedOption[label] = true;
            }
        });
    } else {
       
        const parametros = parametrization.parametros || {};
        Object.keys(parametros).forEach((paramKey) => {
            if (updatedOption[paramKey] !== undefined) {
                updatedOption[paramKey] = parametros[paramKey];
            }
        });
    }

    selectedOption.value = updatedOption;
};


onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('spcLocalizaMix')

		data.parametro.Insumo.forEach((param) => {
			if (optionIdMapping.value[param.label] !== undefined) {
				optionIdMapping.value[param.label] = param.id
			}
		})
	} catch (error) {
		console.log(error)
	}
})
</script>

<style lang="scss">
.custom-select {
	min-width: 100%;
	padding: 12px 8px;
	border: 1px solid #ccc;
	border-radius: 4px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.options {
	display: flex;
	justify-content: center;
	gap: 24px;
}
</style>
