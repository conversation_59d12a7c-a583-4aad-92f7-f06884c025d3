<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: 'Base Jurídica Offline (Kurrier)',
				value: 'kurrier',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff; color: #ffff; font-size: 10px"
				:block="false"
				:disabled="false"
				type="custom"
				size="sm"
				variant="secondary"
				content="Secondary Badge">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #kurrier>
			<div class="info">
				<UpBadge
					style="background-color: #d77b0a; color: #ffff; font-size: 10px"
					v-bind="{
						block: false,
						disabled: false,
						type: 'PEP',
						size: 'lg',
						variant: 'secondary',
						content: 'Secondary Badge',
					}">
					<p>{{ $t('create.parametrizations.attention') }}</p>
				</UpBadge>
				<span>{{
					$t('create.parametrizations.offlineLegalDatabaseNotice')
				}}</span>
			</div>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div style="min-width: 430px">
					<div>
						<div>
							<span class="title">{{
								$t('create.parametrizations.parts')
							}}</span>
							<ul class="flex gap-2 mb-2">
								<li v-for="(part, index) in parts" :key="index">
									<UpCheckbox
										class="check"
										style="margin-top: 8px"
										:label="part.label"
										:disabled="false"
										v-model:checked="part.checked" />
								</li>
							</ul>
						</div>

						<div>
							<span class="title">{{
								$t('create.parametrizations.justice')
							}}</span>
							<ul class="flex gap-2">
								<li v-for="(justice, index) in justices" :key="index">
									<UpCheckbox
										class="check"
										style="margin-top: 8px"
										:label="justice.label"
										:disabled="false"
										v-model:checked="justice.checked" />
								</li>
							</ul>
						</div>
					</div>
				</div>
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="userSelectedParametrization"
						source="kurrier"
						@select-parametrization="setParametrization" />
				</div>
			</div>
			<div style="margin-top: 10px">
				<span class="title">{{ $t('create.parametrizations.uf') }}</span>
				<div class="select">
					<UpSelect
						:options="filteredOptionsUfs"
						v-model="selectedOptionUfs"
						allowMultiple
						:searchable="true"
						@search="(searchTerm) => handleSearch(searchTerm, 'ufs')"
						:placeholder="
							$t('create.parametrizations.keywordPlaceholderDiarioOficial')
						" />
				</div>
				<div class="badge-container">
					<div
						v-for="(item, index) in selectedOptionUfs"
						:key="index"
						class="badge">
						{{ item.label }}
						<button @click="removeSelection(index)">
							<NewInlineSvg
								name="close_x.svg"
								width="8"
								height="8"
								fill="#666666" />
						</button>
					</div>
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { onBeforeMount, ref, watch, computed, nextTick } from 'vue'
import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import { useToast } from '@aplicativo/uplexis-ui'
import PreParametrization from './PreParametrization.vue'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'

const toast = useToast()

const optionsUfs = ref([])

const selectedOptionUfs = ref([])

const emits = defineEmits(['selectParametrization'])

const filteredOptionsUfs = ref([])

const parts = ref([])
const justices = ref([])

watch(
	[selectedOptionUfs],
	async () => {
		await nextTick()
		emitSelectParametrization()
	},
	{ deep: true }
)

watch(
	() => parts.value.map((part) => part.checked),
	async () => {
		await nextTick()
		emitSelectParametrization()
	},
	{ deep: true }
)

watch(
	() => justices.value.map((justice) => justice.checked),
	async () => {
		await nextTick()
		emitSelectParametrization()
	},
	{ deep: true }
)

const removeSelection = (index) => {
	selectedOptionUfs.value.splice(index, 1)
}

function emitSelectParametrization() {
	const ufParametros = selectedOptionUfs.value.map((item) => String(item.id))

	const selectedParts = parts.value
		.filter((part) => part.checked)
		.map((part) => String(part.id))

	const selectedJustices = justices.value
		.filter((justice) => justice.checked)
		.map((justice) => String(justice.id))

	emits('selectParametrization', {
		parametros: {
			uf: ufParametros,
			partes: selectedParts,
			justica: selectedJustices,
		},
	})
}

const userSelectedParametrization = computed(() => ({
	parametros: {
		uf: selectedOptionUfs.value.map((item) => String(item.id)),
		partes: parts.value
			.filter((part) => part.checked)
			.map((part) => String(part.id)),
		justica: justices.value
			.filter((justice) => justice.checked)
			.map((justice) => String(justice.id)),
	},
}))

const setParametrization = (parametrization) => {
	const parametros = parametrization?.parametros?.parametros
		? parametrization.parametros.parametros
		: parametrization.parametros

	if (!parametros) {
		return
	}

	parts.value = parts.value.map((part) => ({
		...part,
		checked:
			parametros?.partes?.includes(String(part.id)) ||
			parametros?.partes?.includes(Number(part.id)) ||
			false,
	}))

	justices.value = justices.value.map((justice) => ({
		...justice,
		checked:
			parametros?.justica?.includes(String(justice.id)) ||
			parametros?.justica?.includes(Number(justice.id)) ||
			false,
	}))

	selectedOptionUfs.value = optionsUfs.value.filter(
		(uf) =>
			parametros?.uf?.includes(String(uf.id)) ||
			parametros?.uf?.includes(Number(uf.id))
	)
}

const mappedOptionsUfs = computed(() =>
	optionsUfs.value.map((item) => ({
		label: item.label,
		id: item.id,
	}))
)

const handleSearch = (searchTerm) => {
	filteredOptionsUfs.value = mappedOptionsUfs.value.filter((option) =>
		option.label.toLowerCase().includes(searchTerm.toLowerCase())
	)
}

onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('kurrier')

		const parametro = {
			Uf: data.parametro.Uf || data.parametro.uf || [],
			Partes: data.parametro.Partes || data.parametro.partes || [],
			Justica: data.parametro.Justica || data.parametro.justica || [],
		}
		optionsUfs.value = parametro.Uf
		parts.value = parametro.Partes.map((part) => ({
			...part,
			checked: false,
		}))
		justices.value = parametro.Justica.map((justice) => ({
			...justice,
			checked: false,
		}))

		filteredOptionsUfs.value = mappedOptionsUfs.value
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error?.response?.data.message,
			})
		}
	}
})
</script>

<style lang="scss" scoped>
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}

.title {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

.select {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	margin-bottom: 22px;
}

.info {
	display: flex;
	flex-direction: column;
	margin-top: 16px;
}

.info > span {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;

	margin-top: 8px;
}

.check {
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 16px;
}

:deep(.up-checkbox__text) {
	color: #666666 !important;
}

.badge-container {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: center;
	margin: 8px 0px;
	gap: 8px;
}

.badge {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-02, #ccc);
	background: var(--Light-neutral-light-pure, #fff);

	color: var(--Dark-neutral-dark-03, #666);

	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}
</style>
