<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.certidaoJudicialTitle'),
				value: 'CertidaoJudicialTrf4',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #CertidaoJudicialTrf4>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div style="min-width: 430px">
					<label class="text-usu text-neutral-dark-3">
						{{ $t('create.parametrizations.certificateType') }}
					</label>
					<CustomSelect
						v-model="selectedOption"
						:options="options"
						min-width="100%"
						:allow-multiple="false"
						:searchable="false"
						placeholder="Selecione">
						<template #selected-option="selectedOption">
							<span
								class="text-xxxs text-neutral-dark-3 text-left"
								style="
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
								">
								{{ selectedOption.label }}
							</span>
						</template>

						<template v-slot:option="option">
							<div
								class="text-xxxs text-neutral-dark-3 text-left"
								style="
									border-bottom: 1px solid #ccc;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									padding: 12px 8px;
								">
								{{ option.label }}
							</div>
						</template>
					</CustomSelect>
				</div>
				<div style="width: 400px">
					<!-- <PreParametrization
						:user-selected-parametrization="selectedOption"
						source="CertidaoJudicialTrf4"
						@select-parametrization="setParametrization" /> -->
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { onBeforeMount, ref, watch } from 'vue'
import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import PreParametrization from './PreParametrization.vue'
import CustomSelect from '@/modules/dossier/list/components/CustomSelect.vue'

const options = ref([])
const selectedOption = ref(null)

const emits = defineEmits(['selectParametrization'])
watch(selectedOption, () => {
	emits('selectParametrization', {
		tipo_certidao: selectedOption.value?.valor,
	})
})

const setParametrization = (parametrization) => {
	selectedOption.value = parametrization.parametros
}

onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('CertidaoJudicialTrf4')
		options.value = data.parametro.tipo_certidao
	} catch (error) {
		console.log(error)
	}
})
</script>

<style lang="scss">
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}
</style>
