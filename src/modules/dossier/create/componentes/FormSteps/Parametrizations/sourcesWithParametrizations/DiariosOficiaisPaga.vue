<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.titleDiarioOficialPay'),
				value: 'DiariosOficiaisPaga',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff; color: #fff; font-size: 10px"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #DiariosOficiaisPaga>
			<div style="padding-top: 16px">
				<div>
					<div class="flex items-center justify-between">
						<UpCheckbox
							class="relevance"
							:label="
								$t('create.parametrizations.relevanceCheckboxDiarioOficial')
							"
							:disabled="false"
							v-model:checked="selectedRelevance" />

						<div style="width: 400px">
							<PreParametrization
								:user-selected-parametrization="selectedParametrizationData"
								source="diariosOficiais"
								@select-parametrization="setParametrization" />
						</div>
					</div>

					<label class="text-usu text-neutral-dark-3"
						>{{ $t('create.parametrizations.diarioLabel') }}
					</label>

					<div class="diario">
						<UpSelect
							:options="filteredOptions"
							v-model="selectedOption"
							allowMultiple
							:searchable="true"
							@search="handleSearch"
							:placeholder="
								$t('create.parametrizations.keywordPlaceholderDiarioOficial')
							">
							<template #selected-options>
								<span
									v-if="selectedOption.length === 0"
									style="text-align: left">
									{{ $t('create.parametrizations.select') }}
								</span>
								<span
									v-else-if="selectedOption.length === options.length"
									style="text-align: left">
									{{ $t('create.parametrizations.allSelected') }}
								</span>
								<span
									v-else
									class="selected-options-text"
									style="
										text-align: left;
										white-space: nowrap;
										overflow: hidden;
										text-overflow: ellipsis;
									">
									{{ selectedOption.map((option) => option.label).join(', ') }}
								</span>
							</template>
						</UpSelect>
					</div>

					<div class="badge-container">
						<div
							v-for="(item, index) in selectedOption"
							:key="index"
							class="badge">
							{{ item.label }}
							<button @click="removeSelection(index)">
								<NewInlineSvg
									name="close_x.svg"
									width="8"
									height="8"
									fill="#666666" />
							</button>
						</div>
					</div>

					<div>
						<span class="description-keyword">{{
							$t('create.parametrizations.keywordPlaceholderDiarioOficial')
						}}</span>
						<div>
							<UpInput
								:props="{
									type: 'text',
									placeholder: 'Selecione',
									icon: {
										name: 'add',
									},
								}"
								v-model="keyword"
								@click="addKeyword"
								@enter="addKeyword" />
						</div>
						<div class="badge-container">
							<div v-for="(word, index) in keywords" :key="index" class="badge">
								{{ word }}
								<button @click="removeWord(index)">
									<NewInlineSvg
										name="close_x.svg"
										width="8"
										height="8"
										fill="#666666" />
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { onBeforeMount, ref, watch, computed, nextTick } from 'vue'
import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import PreParametrization from './PreParametrization.vue'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'

import { useToast } from '@aplicativo/uplexis-ui'

const toast = useToast()

const options = ref([])
const selectedOption = ref([])
const selectedRelevance = ref(false)

const emits = defineEmits(['selectParametrization'])

watch(
	selectedOption,
	async () => {
		await nextTick()

		emits('selectParametrization', {
			diariosOficiais: {
				parametros: selectedOption.value.map((item) => String(item.id)),
				palavras: keywords.value,
				auto_relevante: selectedRelevance.value,
			},
		})
	},
	{ deep: true }
)

const setParametrization = (parametrization) => {

if (Array.isArray(parametrization.parametros)) {
	const selectedItems = parametrization.parametros.map((param) => ({
		id: param.id,
		label: param.label,
	}));
	selectedOption.value = selectedItems;
} 

else if (parametrization.parametros && parametrization.parametros.parametros) {
	const selectedItems = parametrization.parametros.parametros.map((paramId) => {
		const matchedOption = options.value.find(option => String(option.name) === String(paramId));
		return matchedOption ? { id: matchedOption.name, label: matchedOption.description } : null;
	}).filter((item) => item !== null);

	selectedOption.value = selectedItems;

	if (typeof parametrization.parametros.palavras === 'string') {
		keywords.value = parametrization.parametros.palavras.split(',').map((word) => word.trim());
	} else if (Array.isArray(parametrization.parametros.palavras)) {
		keywords.value = parametrization.parametros.palavras;
	} else {
		keywords.value = [];
	}
} else {
	console.error("Formato de parametrization não reconhecido:", parametrization);
}

selectedRelevance.value = parametrization.parametros?.auto_relevante || false;
};


const keyword = ref('')
const keywords = ref([])

const addKeyword = () => {
	if (keyword.value.trim() !== '') {
		keywords.value.push(keyword.value.trim())
		keyword.value = ''
	}
}

const mappedOptions = computed(() =>
	options.value.map((item) => ({
		label: item.description,
		id: item.name,
	}))
)

const selectedParametrizationData = computed(() => ({
	parametros: selectedOption.value.map((item) => String(item.id)),
	palavras: keywords.value,
	auto_relevante: selectedRelevance.value,
}))

const filteredOptions = ref([])

const handleSearch = (searchTerm) => {
	if (searchTerm.trim() === '') {
		filteredOptions.value = mappedOptions.value
	} else {
		filteredOptions.value = mappedOptions.value.filter((option) =>
			option.label.toLowerCase().includes(searchTerm.toLowerCase())
		)
	}
}

const removeWord = (index) => {
	keywords.value.splice(index, 1)
}

const removeSelection = (index) => {
	selectedOption.value.splice(index, 1)
}

onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('diariosOficiaisPaga')

		options.value = data.parametro.Diarios
		filteredOptions.value = mappedOptions.value
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
})
</script>

<style lang="scss" scoped>
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}

.description-keyword {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

.badge-container {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: center;
	margin: 8px 0px;
	gap: 8px;
}

.badge {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-02, #ccc);
	background: var(--Light-neutral-light-pure, #fff);

	color: var(--Dark-neutral-dark-03, #666);

	font-size: 12px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

.diario {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
}

.relevance {
	margin-bottom: 32px;
}

.relevance > .up-checkbox__text {
	color: #666666 !important;
}
</style>
