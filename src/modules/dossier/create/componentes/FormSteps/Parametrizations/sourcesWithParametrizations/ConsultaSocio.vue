<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.consultaSocioTitle'),
				value: 'ConsultaSocio',
			},
		]">
		<template v-slot:description>
			<!-- <UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge> -->
		</template>
		<template #ConsultaSocio>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div class="option">
					<span>{{ $t('create.parametrizations.proximityCriterion') }}</span>

					<input
						type="number"
						v-model="inputValue"
						placeholder="Similaridade"
						@input="onInputChange" />
				</div>
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="selectedOption"
						source="consultaSocio"
						@select-parametrization="setParametrization" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import PreParametrization from './PreParametrization.vue'

import { ref, watch, onMounted } from 'vue'

const selectedOption = ref(null)
const inputValue = ref(80)

const emits = defineEmits(['selectParametrization'])

watch(inputValue, (newValue) => {
	selectedOption.value = { valor: newValue }
	emitSelectedParametrization()
})

const setParametrization = (parametrization) => {
  
  if (parametrization.parametros.valor) {
    selectedOption.value = { valor: parametrization.parametros.valor }
    inputValue.value = parseInt(parametrization.parametros.valor) 
  } else if (parametrization.parametros.proximidade) {
    selectedOption.value = { valor: parametrization.parametros.proximidade }
    inputValue.value = parseInt(parametrization.parametros.proximidade) 
  } else {
    selectedOption.value = null
  }

  emitSelectedParametrization()
}


const onInputChange = (event) => {
	inputValue.value = event.target.value
}

const emitSelectedParametrization = () => {
	emits('selectParametrization', {
		similaridade: selectedOption.value?.valor,
	})
}

onMounted(() => {
	selectedOption.value = { valor: inputValue.value }
	emitSelectedParametrization()
})
</script>

<style scoped>
.option {
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.option > span {
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

input {
	width: 166px;
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	background: var(--Light-neutral-light-pure, #fff);
}
</style>
