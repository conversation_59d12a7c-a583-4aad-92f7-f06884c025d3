<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.pessoaGoldBoaVistaTitle'),
				value: 'PessoaGoldBoaVista',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #PessoaGoldBoaVista>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<ul style="gap: 16px" class="flex items-center">
					<li v-for="option in options">
						<UpCheckbox
							:label="option.label"
							:value="option.id"
							v-model:checked="selectedOptions" />
					</li>
				</ul>
				<div style="width: 400px">
					<PreParametrization
						source="PessoaGoldBoaVista"
						@select-parametrization="setParametrization"
						:user-selected-parametrization="selectedOptions" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { onBeforeMount, ref, watch } from 'vue'
import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import PreParametrization from './PreParametrization.vue'
const options = ref([])

const props = defineProps({
	initialData: {
		type: [Array, null],
		required: false,
	},
})

const selectedOptions = ref(Array.isArray(props.initialData) ? props.initialData : [])

const emits = defineEmits(['selectParametrization'])

watch(selectedOptions, () => {
  if (selectedOptions.value && selectedOptions.value.length === 0) {
    emits('selectParametrization', [])
  } else {
    emits('selectParametrization', selectedOptions.value)
  }
})


const setParametrization = (parametrization) => {
	selectedOptions.value = parametrization.parametros
}

onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('PessoaGoldBoaVista')
		options.value = data.parametro['Opção']
	} catch (error) {
		console.log(error)
	}
})
</script>

<style lang="scss">
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}
</style>
