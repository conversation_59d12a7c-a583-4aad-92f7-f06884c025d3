<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.buscadorGoogleTitle'),
				value: 'BuscadorGoogle',
			},
		]">
		<template #BuscadorGoogle>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div>
					<UpCheckbox
						:label="$t('create.parametrizations.autoMarkAsRelevant')"
						:disabled="false"
						v-model:checked="selectedOption" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { ref, watch } from 'vue'

const selectedOption = ref(null)

const emits = defineEmits(['selectParametrization'])

watch(selectedOption, (newValue) => {
	if (newValue) {
		emits('selectParametrization', { auto_relevante: selectedOption.value })
	}
})
</script>
