<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.BaseJuridicaOfflineEscavador'),
				value: 'BaseJuridicaOfflineEscavador',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff; color: #ffff"
				:block="false"
				:disabled="false"
				type="custom"
				size="sm"
				variant="secondary"
				content="Secondary Badge">
				<p style="color: #ffff">
					{{ $t('create.parametrizations.mandatory') }}
				</p>
			</UpBadge>
		</template>
		<template #BaseJuridicaOfflineEscavador>
			<div class="flex items-end justify-between">
				<div class="info">
					<UpBadge
						style="background-color: #d77b0a; color: #ffff"
						v-bind="{
							block: false,
							disabled: false,
							type: 'PEP',
							size: 'lg',
							variant: 'secondary',
							content: 'Secondary Badge',
						}">
						<p style="text-wrap: wrap;">{{ $t('create.parametrizations.attention') }}</p>
					</UpBadge>
					<span>{{
						$t('create.parametrizations.offlineLegalDatabaseNotice')
					}}</span>
				</div>
				<div style="width: 400px">
					<PreParametrization
						source="BaseOffEscavador"
						@select-parametrization="setParametrization"
						:user-selected-parametrization="selectedOption" />
				</div>
			</div>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div style="min-width: 430px">
					<Switch
						class="toggle"
						v-model="isUFMode"
						:onLabel="$t('create.parametrizations.uf')"
						:offLabel="$t('create.parametrizations.tribunais')" />
					<div>
						<div v-if="isUFMode" style="margin-top: 10px">
							<span class="title">{{ $t('create.parametrizations.uf') }}</span>
							<div class="select w-[630px]">
								<UpSelect
									:options="filteredOptionsUfs"
									v-model="selectedOptionUfs"
									allowMultiple
									:searchable="true"
									@search="(searchTerm) => handleSearch(searchTerm, 'ufs')"
									:placeholder="
										$t(
											'create.parametrizations.keywordPlaceholderDiarioOficial'
										)
									" />
							</div>
						</div>
						<div v-else style="margin-top: 10px">
							<span class="title">{{
								$t('create.parametrizations.tribunais')
							}}</span>
							<div class="select w-[630px]">
								<UpSelect
									:options="filteredOptionsTribunais"
									v-model="selectedOptionTribunais"
									allowMultiple
									:searchable="true"
									@search="
										(searchTerm) => handleSearch(searchTerm, 'tribunais')
									"
									placeholder="
										$t(
											'create.parametrizations.keywordPlaceholderDiarioOficial'
										)
									" />
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { onBeforeMount, ref, watch, computed, nextTick } from 'vue'
import { getParametrizationFor } from '@/modules/dossier/create/services/parametrizations'
import PreParametrization from './PreParametrization.vue'
import Switch from '@/components/Switch/Switch.vue'
import { useToast } from '@aplicativo/uplexis-ui'

const toast = useToast()

const optionsTribunais = ref([])
const optionsUfs = ref([])

const selectedOptionTribunais = ref([])
const selectedOptionUfs = ref([])

const isUFMode = ref(false)

const selectedRelevance = ref(false)

const emits = defineEmits(['selectParametrization'])

const filteredOptionsTribunais = ref([])
const filteredOptionsUfs = ref([])

const setParametrization = (parametrization) => {

	const tribunais = parametrization.parametros.tribunais || []
	const ufs = parametrization.parametros.uf || []

	selectedOptionTribunais.value = optionsTribunais.value.filter((option) =>
		tribunais.includes(option.id)
	)
	selectedOptionUfs.value = optionsUfs.value.filter((option) => ufs.includes(option.id))
}

watch(
	[selectedOptionTribunais, selectedOptionUfs],
	async () => {
		await nextTick()

		const ufParametros = selectedOptionUfs.value.map((item) => String(item.id))
		const tribunaisParametros = selectedOptionTribunais.value.map((item) =>
			String(item.id)
		)

		emits('selectParametrization', {
			parametros: {
				tribunais: isUFMode.value ? [] : tribunaisParametros,
				uf: isUFMode.value ? ufParametros : [],
			},
		})
	},
	{ deep: true }
)

watch(isUFMode, (newValue) => {
	if (newValue) {
		selectedOptionTribunais.value = []
	} else {
		selectedOptionUfs.value = []
	}
})

const mappedOptionsTribunais = computed(() =>
	optionsTribunais.value.map((item) => ({
		label: item.label,
		id: item.id,
	}))
)

const mappedOptionsUfs = computed(() =>
	optionsUfs.value.map((item) => ({
		label: item.label,
		id: item.id,
	}))
)

const handleSearch = (searchTerm, type) => {
	if (type === 'ufs') {
		filteredOptionsUfs.value = mappedOptionsUfs.value.filter((option) =>
			option.label.toLowerCase().includes(searchTerm.toLowerCase())
		)
	} else if (type === 'tribunais') {
		filteredOptionsTribunais.value = mappedOptionsTribunais.value.filter(
			(option) => option.label.toLowerCase().includes(searchTerm.toLowerCase())
		)
	}
}

onBeforeMount(async () => {
	try {
		const { data } = await getParametrizationFor('BaseOffEscavador')
		optionsTribunais.value = data.parametro.Tribunais || []
		optionsUfs.value = data.parametro.UF || []

		filteredOptionsTribunais.value = mappedOptionsTribunais.value
		filteredOptionsUfs.value = mappedOptionsUfs.value
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error?.response?.data.message,
			})
		}
	}
})
</script>

<style lang="scss" scoped>
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}

.title {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

.select {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	margin-bottom: 22px;
}

.info {
	display: flex;
	flex-direction: column;
	margin-top: 16px;
}

.info > span {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;

	margin-top: 8px;
}
</style>
