<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.ppeTitlutarPrivadaTitle'),
				value: 'PPETitularPrivada',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #PPETitularPrivada>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<div style="min-width: 430px">
					<label class="text-usu text-neutral-dark-3"
						>Buscar Pessoas Relacionadas:
					</label>
					<CustomSelect
						v-model="selectedOption"
						:options="options"
						min-width="100%"
						:allow-multiple="false"
						:searchable="false"
						placeholder="Selecione">
						<template #selected-option="selectedOption">
							<span
								class="text-xxxs text-neutral-dark-3 text-left"
								style="
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									padding: 12px 8px;
								">
								{{ selectedOption.label }}
							</span>
						</template>

						<template v-slot:option="option">
							<div
								class="text-xxxs text-neutral-dark-3 text-left"
								style="
									border-bottom: 1px solid #ccc;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
									padding: 12px 8px;
								">
								{{ option.label }}
							</div>
						</template>
					</CustomSelect>
				</div>
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="selectedOption"
						source="ppeTitularPrivada"
						@select-parametrization="setParametrization" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import PreParametrization from './PreParametrization.vue'

import CustomSelect from '@/modules/dossier/list/components/CustomSelect.vue'

import { onBeforeMount, ref, watch } from 'vue'

const options = ref([])
const selectedOption = ref(null)

const emits = defineEmits(['selectParametrization'])

watch(selectedOption, () => {
	 if (selectedOption.value) {
    emits('selectParametrization', selectedOption.value.value)
  } else {
    emits('selectParametrization', null)
  }
})

onBeforeMount(() => {
	options.value = [
		{ label: 'Sim', value: 'S' },
		{ label: 'Não', value: 'N' },
	]
})

const setParametrization = (parametrization) => {
  if (parametrization.parametros.label && parametrization.parametros.value) {
    selectedOption.value = parametrization.parametros
  } else if (parametrization.parametros.detalhes) {
    selectedOption.value = {
      label: parametrization.parametros.detalhes === 'S' ? 'Sim' : 'Não',
      value: parametrization.parametros.detalhes
    }
  } else {

    selectedOption.value = null
  }
}

</script>

<style lang="scss" scoped>
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}
</style>
