<template>
	<UpAccordion
		class="up-accordion-pessoa-gold-boa-vista"
		:items="[
			{
				title: $t('create.parametrizations.comprotTitle'),
				value: 'Comprot',
			},
		]">
		<template v-slot:description>
			<UpBadge
				style="background-color: #c371ff"
				v-bind="{
					block: false,
					disabled: false,
					type: 'custom',
					size: 'sm',
					variant: 'secondary',
					content: 'Secondary Badge',
				}">
				<p>{{ $t('create.parametrizations.mandatory') }}</p>
			</UpBadge>
		</template>
		<template #Comprot>
			<div style="padding-top: 16px" class="flex items-center justify-between">
				<UpInput
					:props="{
						type: 'text',
						placeholder: '00/00/0000',
						label: 'Data inicial',
						pattern: '##/##/####',
					}"
					v-model="initialDate" />
				<div style="width: 400px">
					<PreParametrization
						:user-selected-parametrization="[initialDate]"
						source="Comprot"
						@select-parametrization="setParametrization" />
				</div>
			</div>
		</template>
	</UpAccordion>
</template>

<script setup>
import { ref, watch } from 'vue'

import PreParametrization from './PreParametrization.vue'

const initialDate = ref('')

const setParametrization = (parametrization) => {

    if (Array.isArray(parametrization.parametros)) {
        initialDate.value = parametrization.parametros[0]; 
    } else if (parametrization.parametros && parametrization.parametros.data_inicial) {
        initialDate.value = parametrization.parametros.data_inicial;
    } 
};


const emits = defineEmits(['selectParametrization'])
watch(initialDate, () => {
	emits('selectParametrization', {
		data_inicial: initialDate.value,
	})
})
</script>

<style lang="scss">
.up-accordion-pessoa-gold-boa-vista button[data-state] {
	border-style: solid !important;
}
.up-accordion-pessoa-gold-boa-vista .overflow-hidden {
	overflow: visible !important;
}
</style>
