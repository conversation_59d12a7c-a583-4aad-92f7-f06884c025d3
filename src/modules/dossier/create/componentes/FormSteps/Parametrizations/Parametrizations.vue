<template>
	<div
		style="
			min-height: 0;
			display: grid;
			grid-template-columns: 1fr;
			grid-template-rows: auto 1fr auto;
		">
		<h3 class="text-xxxs text-neutral-dark-3" style="padding-bottom: 24px">
			{{ $t('create.parametrizations.addParameters') }}
		</h3>
		<div style="overflow-y: auto">
			<ul class="flex flex-col" style="gap: 16px; padding-bottom: 32px">
				<template
					v-for="(source, index) in profile.sources.saidas"
					:key="index">
					<li v-if="checkIfSourceHasParametrization(source)">
						<PessoaGoldBoaVista
							v-if="source.captura === 'PessoaGoldBoaVista'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: parametrization
											? { parametros: parametrization }
											: null,
									})
							" />
						<BancoCentralCrsfnEmentasAcordaos
							v-if="source.captura === 'bancoCentralCrsfnEmentasAcordaos'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
						<CertidaoCeatTrtCampinasRegiao
							v-if="source.captura === 'CertidaoCeatTrtCampinasRegiao'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
						<TcuCertidaoContasIrregulares
							v-if="source.captura === 'TcuCertidaoContasIrregulares'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
						<CertidaoAcoesExecucoesCiveisCriminaisTrf
							v-if="
								source.captura === 'CertidaoAcoesExecucoesCiveisCriminaisTrf'
							"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
						<CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf
							v-if="
								source.captura ===
								'CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf'
							"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
						<CertidaoJudicialTrf4
							v-if="source.captura === 'CertidaoJudicialTrf4'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
						<CertidaoCgu
							v-if="source.captura === 'CertidaoCgu'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<Comprot
							v-if="source.captura === 'Comprot'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<PPETitularPaga
							v-if="source.captura === 'ppeTitularPaga'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<PPETitularPrivada
							v-if="source.captura === 'ppeTitularPrivada'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<MinisterioTurismoCadastur
							v-if="source.captura === 'MinisterioTurismoCadastur'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<BuscadorGoogle
							v-if="source.captura === 'googleGlobal'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<GoogleNaoEspecifica
							v-if="source.captura === 'googleGlobalNotSpecific'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<SPCMixMais
							v-if="source.captura === 'spcLocalizaMix'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<ConsultaSocio
							v-if="source.captura === 'consultaSocio'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<DiariosOficiais
							v-if="source.captura === 'diariosOficiais'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<Cade
							v-if="source.captura === 'cade'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<BaseOffEscavador
							v-if="source.captura === 'BaseOffEscavador'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<DiariosOficiaisPaga
							v-if="source.captura === 'diariosOficiaisPaga'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<BaseOffEscavadorDocumentos
							v-if="source.captura === 'BaseOffEscavadorV2'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<TjPrAntecedentesEleitorais
							v-if="source.captura === 'TjPrAntecedentesEleitorais'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<Kurrier
							v-if="source.captura === 'kurrier'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametrization },
									})
							" />

						<Djen
							v-if="source.captura === 'Djen'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<FontesJuridicas
							v-if="source.captura === 'FontesJuridicas'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<ProcessosJuridicos
							v-if="source.captura === 'ProcessosJuridicos'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />

						<MpfRelevancia
							v-if="source.captura === 'mpfRelevancia'"
							@select-parametrization="
								(parametrization) =>
									setParametrization({
										name: source.captura,
										value: { parametros: parametrization },
									})
							" />
					</li>
				</template>
			</ul>
		</div>

		<footer
			class="flex items-center"
			style="gap: 16px; padding-top: 16px; padding-bottom: 32px">
			<UpButton
				style="border-style: solid"
				type="secondary"
				size="medium"
				@click="previousStep">
				{{ $t('create.parametrizations.previousStep') }}
			</UpButton>

			<UpButton
				:disabled="shouldDisableNextButton"
				type="primary"
				size="medium"
				@click="nextStep">
				{{ $t('create.parametrizations.nextStepCreateDossier') }}
			</UpButton>

			<UpCheckbox
				:label="$t('create.autoCreateAndProcessDossiers')"
				v-model:checked="shouldProcessAutomatically" />
		</footer>
	</div>
</template>

<script setup>
import PessoaGoldBoaVista from './sourcesWithParametrizations/PessoaGoldBoaVista.vue'
import BancoCentralCrsfnEmentasAcordaos from './sourcesWithParametrizations/BancoCentralCrsfnEmentasAcordaos.vue'
import CertidaoCeatTrtCampinasRegiao from './sourcesWithParametrizations/CertidaoCeatTrtCampinasRegiao.vue'
import TcuCertidaoContasIrregulares from './sourcesWithParametrizations/TcuCertidaoContasIrregulares.vue'
import CertidaoAcoesExecucoesCiveisCriminaisTrf from './sourcesWithParametrizations/CertidaoAcoesExecucoesCiveisCriminaisTrf.vue'
import CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf from './sourcesWithParametrizations/CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf.vue'
import CertidaoJudicialTrf4 from './sourcesWithParametrizations/CertidaoJudicialTrf4.vue'
import CertidaoCgu from './sourcesWithParametrizations/CertidaoCgu.vue'
import Comprot from './sourcesWithParametrizations/Comprot.vue'
import PPETitularPaga from './sourcesWithParametrizations/PPETitularPaga.vue'
import PPETitularPrivada from './sourcesWithParametrizations/PPETitularPrivada.vue'
import MinisterioTurismoCadastur from './sourcesWithParametrizations/MinisterioTurismoCadastur'
import BuscadorGoogle from './sourcesWithParametrizations/BuscadorGoogle.vue'
import GoogleNaoEspecifica from './sourcesWithParametrizations/GoogleNaoEspecifica.vue'
import SPCMixMais from './sourcesWithParametrizations/SPCMixMais.vue'
import ConsultaSocio from './sourcesWithParametrizations/ConsultaSocio.vue'
import DiariosOficiais from './sourcesWithParametrizations/DiariosOficiais.vue'
import Cade from './sourcesWithParametrizations/Cade.vue'
import BaseOffEscavador from './sourcesWithParametrizations/BaseOffEscavador.vue'
import DiariosOficiaisPaga from './sourcesWithParametrizations/DiariosOficiaisPaga.vue'
import BaseOffEscavadorDocumentos from './sourcesWithParametrizations/BaseOffEscavadorDocumentos.vue'
import TjPrAntecedentesEleitorais from './sourcesWithParametrizations/TjPrAntecedentesEleitorais.vue'
import Kurrier from './sourcesWithParametrizations/Kurrier.vue'
import Djen from './sourcesWithParametrizations/Djen.vue'
import FontesJuridicas from './sourcesWithParametrizations/FontesJuridicas.vue'
import ProcessosJuridicos from './sourcesWithParametrizations/ProcessosJuridicos.vue'
import MpfRelevancia from './sourcesWithParametrizations/MpfRelevancia.vue'

import { ref, computed } from 'vue'

const props = defineProps({
	initialParametrizations: {
		type: Object,
		required: true,
	},

	profile: {
		type: Object,
		required: true,
	},
})

const sourcesWithParametrizations = [
	'bancoCentralCrsfnEmentasAcordaos',
	'ppeTitularPaga',
	'CertidaoCeatTrtCampinasRegiao',
	'TcuCertidaoContasIrregulares',
	'CertidaoAcoesExecucoesCiveisCriminaisTrf',
	'CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf',
	'CertidaoJudicialTrf4',
	'CertidaoCgu',
	'Comprot',
	'googleGlobal',
	'googleGlobalNotSpecific',
	'MinisterioTurismoCadastur',
	'PessoaGoldBoaVista',
	'ppeTitularPrivada',
	'consultaSocio',
	'spcLocalizaMix',
	'diariosOficiais',
	'cade',
	'BaseOffEscavador',
	'diariosOficiaisPaga',
	'BaseOffEscavadorV2',
	'TjPrAntecedentesEleitorais',
	'kurrier',
	'Djen',
	'FontesJuridicas',
	'ProcessosJuridicos',
	'mpfRelevancia'
]

const checkIfSourceHasParametrization = (source) => {
	return sourcesWithParametrizations.includes(source.captura)
}

const parametrizations = ref(props.initialParametrizations || {})
const shouldProcessAutomatically = ref(false)

const setParametrization = ({ name, value }) => {
	let content

	if (name === 'cade' || 'BaseOffEscavador') {
		content = value.parametrization ? value.parametrization : value
	} else {
		content = value
	}

	parametrizations.value = {
		...parametrizations.value,
		[name]: content,
	}
}

const isValid = computed(() => {
	const notRequired = ['googleGlobal', 'googleGlobalNotSpecific', 'Djen', 'FontesJuridicas', 'ProcessosJuridicos', 'mpfRelevancia']

	return Object.entries(parametrizations.value).every(([key, value]) => {
		if (notRequired.includes(key)) {
			return true
		}

		if (key === 'diariosOficiais') {
			return value?.diariosOficiais.parametros.length > 0
		}

		if (key === 'diariosOficiaisPaga') {
			return value?.diariosOficiais.parametros.length > 0
		}

		if (key === 'cade') {
			return value?.parametros?.pesquisa.length > 0
		}

		if (key === 'Comprot') {
			return validateDateComprot(value)
		}

		if (key === 'spcLocalizaMix') {
			return value?.parametros.length > 0
		}

		if (key === 'BaseOffEscavador') {
			return hasTribunaisOrUfEscavador(value)
		}

		if (key === 'BaseOffEscavadorV2') {
			return hasTribunaisOrUfEscavador(value)
		}

		if (key === 'MinisterioTurismoCadastur') {
			return value?.parametros.uf
		}

		if (key === 'CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf') {
			return value?.parametros.tipo_certidao
		}

		if (key === 'CertidaoJudicialTrf4') {
			return value?.parametros.tipo_certidao
		}

		if (key === 'CertidaoAcoesExecucoesCiveisCriminaisTrf') {
			return value?.parametros.tipo_certidao
		}

		if (key === 'TcuCertidaoContasIrregulares') {
			return value?.parametros.tipo_certidao
		}

		if (key === 'CertidaoCeatTrtCampinasRegiao') {
			return value?.parametros.processos_arquivados
		}

		if (key === 'bancoCentralCrsfnEmentasAcordaos') {
			return value?.parametros.exato
		}

		if (key === 'ppeTitularPrivada') {
			return value?.parametros
		}

		if (key === 'ppeTitularPaga') {
			return value?.parametros
		}

		if (key === 'PessoaGoldBoaVista') {
			return value?.parametros.length > 0
		}

		if (key === 'TjPrAntecedentesEleitorais') {
			return value?.parametros.name && value?.parametros.motherName
		}

		if (key === 'kurrier') {
			return (
				value?.parametros.justica.length > 0 &&
				value?.parametros.partes.length > 0 &&
				value?.parametros.uf.length > 0
			)
		}
		return Boolean(value)
	})
})

function validateDateComprot(value) {
	const datePattern = /^\d{2}\/\d{2}\/\d{4}$/

	if (value?.parametros?.data_inicial) {
		const dataInicial = value.parametros.data_inicial

		if (datePattern.test(dataInicial)) {
			const [day, month, year] = dataInicial.split('/').map(Number)
			const date = new Date(year, month - 1, day)

			return (
				date.getFullYear() === year &&
				date.getMonth() === month - 1 &&
				date.getDate() === day
			)
		}
	}

	return false
}

function hasTribunaisOrUfEscavador(value) {
	return (
		(value?.parametros?.tribunais && value?.parametros.tribunais.length > 0) ||
		(value?.parametros?.uf && value?.parametros.uf.length > 0)
	)
}

const shouldDisableNextButton = computed(() => {
	return !isValid.value
})

const emits = defineEmits(['nextStep', 'previousStep'])

const nextStep = () => {
	const stepData = {
		parametrizations: parametrizations.value,
		process: shouldProcessAutomatically.value,
	}

	emits('nextStep', { stepData })
}

const previousStep = () => {
	emits('previousStep')
}
</script>
