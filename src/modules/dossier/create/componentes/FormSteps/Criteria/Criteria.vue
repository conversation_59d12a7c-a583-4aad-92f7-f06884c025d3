<template>
	<div class="criteria" ref="criteriaContainerRef">
		<h2
			class="text-xxxs text-left text-neutral-dark-3"
			style="padding-bottom: 16px">
			{{ $t('create.criteria.insertCriteriaMessage') }}
		</h2>

		<div class="flex items-center" style="gap: 16px">
			<div style="position: relative">
				<UpInput
					:props="{
						type: 'text',
						placeholder: criteriaInputDetails.placeholder,
						icon: {
							name: 'add',
						},
						pattern: criteriaInputDetails.pattern,
					}"
					v-model="criteriaToAdd"
					@click="addCriteria"
					@enter="addCriteria" />

				<div
					ref="autocompleteSuggestionsElement"
					style="
						border-radius: 4px;
						background-color: white;
						border: 1px solid #ccc;
						position: absolute;
						width: 100%;
						z-index: 40;
					"
					v-if="showAutoComplete">
					<ul class="flex flex-col">
						<li class="flex-1" v-for="result in autocompleteSuggestions">
							<button
								class="w-full flex items-center justify-between"
								@click="setCriteria(result)"
								style="
									padding-right: 8px;
									border-radius: 4px;
									border: 1px solid #ccc;
								">
								<up-cell
									:key="result.document"
									:label="result.document"
									:value="result.name"
									v-bind="result" />

								<span
									class="material-symbols-outlined text-xxs text-brand-secondary-1"
									>add</span
								>
							</button>
						</li>
					</ul>
				</div>
			</div>

			<UpInput
				v-model="fileSelected"
				:props="{
					loading: isProcessingFile,
					type: 'file',
					placeholder: $t('create.criteria.addTemplate'),
					error: errors.file,
					accept: '*',
				}" />

			<UpButton
				type="tertiary"
				size="medium"
				@click="downloadXLSXExample"
				prependIcon="download">
				{{ $t('create.spreadsheetTemplate') }}
			</UpButton>

			<div class="flex items-center" style="margin-left: auto; gap: 16px">
				<p
					class="text-xxxs font-semibolder text-feedback-warning flex items-center"
					style="gap: 10px"
					v-if="amountOfCriteriasWithHomonyms">
					<span class="material-symbols-outlined text-xxs">emergency_home</span>

					{{
						new Intl.NumberFormat('pt-BR', {
							minimumIntegerDigits: 2,
						}).format(amountOfCriteriasWithHomonyms)
					}}
					homônimo
				</p>

				<span
					class="font-semibolder text-xxxs text-neutral-dark-3"
					v-if="criteriasToDisplay.length > 0">
					{{
						new Intl.NumberFormat('pt-BR', {
							minimumIntegerDigits: 2,
						}).format(criteriasToDisplay.length)
					}}
					{{ $t('create.criteria.criteria') }}
				</span>
			</div>
		</div>
		<div>
			<ul class="criteria-list">
				<li
					class="criteria-item"
					v-for="(criteria, index) in criteriasToDisplay"
					:key="criteria.value"
					:title="
						!criteria.hasHomonyms
							? criteria.value
							: `Este nome tem ${criteria.amountOfHomonyms} homônimos.\nAo pesquisá-lo, você receberá informações\ngerais sobre todas as pessoas com esse nome,\nnão especificamente sobre uma pessoa em\nparticular.`
					">
					<UpBadge
						:type="criteria.type"
						:prepend-icon="criteria.hasHomonyms ? 'theater-comedy-outline' : ''"
						append-icon="close"
						append-icon-btn
						append-icon-event="deleteCriteria"
						@delete-criteria="deleteCriteria(criteria)"
						:variant="getVariant(criteria)">
						<p>{{ criteria.value }}</p>
					</UpBadge>
				</li>
			</ul>
		</div>

		<footer class="flex items-center" style="gap: 16px">
			<UpButton
				style="border-style: solid"
				type="secondary"
				size="medium"
				@click="goToPreviousStep">
				{{ $t('create.common.toBack') }}
			</UpButton>

			<UpButton
				type="primary"
				size="medium"
				:disabled="shouldDisableNextButton"
				@click="goToNextStep">
				{{ $t('create.common.nextStep') }}
			</UpButton>
		</footer>
	</div>
	<Window
		v-if="detailsQueryProfileSelected"
		class="query-profile-window"
		@on-close-without-hide-sidebar="true"
		:is-open="isOpen"
		:collapsible="true"
		:is-collapsed="isCollapsed"
		title=""
		@update:isCollapsed="isCollapsed = $event">
		<QueryProfileDetails
			v-if="detailsQueryProfileSelected"
			:details="detailsQueryProfileSelected" />
	</Window>
</template>

<script setup>
import {
	getQueryProfileDetails,
	validateFileAndGetRows,
	getAutoCompleteCriteria,
	checkForHomonyms,
} from '../../../services/dossier_create'
import QueryProfileDetails from '../SelectQueryProfile/QueryProfileDetails/QueryProfileDetails.vue'

import Window from '@/components/Window/Window.vue'

import { useToast } from '@aplicativo/uplexis-ui'
import { computed, onBeforeMount, ref, watch, nextTick } from 'vue'
import { CNPJEntity } from '../../../entities/cnpj'
import { CPFEntity } from '../../../entities/cpf'
import {
	exportFileTypeCPF,
	exportFileTypeCnpj,
	exportFileTypeName,
	exportFileTypeCorporateReason,
} from '../../../mapper/criteria'

import { useDebounceFn } from '@vueuse/core'

import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
	profile: {
		type: Object,
		required: true,
	},
	initialCriterias: {
		type: Array,
		required: true,
	},
})

function getVariant(criteria) {
	if (!criteria.isValid) {
		return 'error'
	}

	return criteria.hasHomonyms ? 'warning' : 'text'
}

/*
 *
 *
 *  CRITERIOS
 *
 */

const criteriaType = computed(() => {
	const criteriaTypes = ['pf', 'pj', 'name', 'corporateName']

	let criteriaType = ''

	const isNameOrCorporateName = props.profile.tipo_pessoa === 3

	if (isNameOrCorporateName) {
		criteriaType = props.profile.type === 'pf' ? 'name' : 'corporateName'
	} else {
		criteriaType = criteriaTypes[props.profile.tipo_pessoa - 1]
	}

	return criteriaType
})

const criteriaInputDetails = computed(() => {
	const details = {
		pf: {
			placeholder: t('create.criteria.searchCPF'),
			pattern: '###.###.###-##',
		},

		pj: {
			placeholder: t('create.criteria.searchCNPJ'),
			pattern: '##.###.###/####-##',
		},

		name: {
			placeholder: t('create.criteria.searchName'),
			pattern: null,
		},

		corporateName: {
			placeholder: t('create.criteria.searchCorporateName'),
			pattern: null,
		},
	}
	return details[criteriaType.value]
})

const criterias = ref(props.initialCriterias)
const amountOfCriteriasWithHomonyms = computed(() => {
	return criterias.value.filter((criteria) => criteria.hasHomonyms).length
})

const compareFunction = (a, b) => {
	if (a.isValid === false && b.type !== false) {
		return -1
	} else if (a.isValid !== false && b.isValid === false) {
		return 1
	} else {
		return 0
	}
}
const criteriasToDisplay = computed(() => {
	return criterias.value.toSorted(compareFunction)
})

const shouldDisableNextButton = computed(() => {
	return (
		criterias.value.length === 0 ||
		criterias.value.some((criteria) => !criteria.isValid)
	)
})

const criteriaToAdd = ref('')
const setCriteria = (selectedSuggestion) => {
	shouldWatchCriteriaToAdd.value = false
	showAutoComplete.value = false
	if (['pf', 'pj'].includes(criteriaType.value)) {
		criteriaToAdd.value = selectedSuggestion.document
	}

	if (['name', 'corporateName'].includes(criteriaType.value)) {
		criteriaToAdd.value = selectedSuggestion.name
	}
}
const showAutoComplete = ref(false)

const autocompleteSuggestions = ref([])
const autocompleteSuggestionsElement = ref(null)
const updateAutoCompleteSuggestions = useDebounceFn(() => {
	const rawValue = criteriaToAdd.value.replace(/[./]/g, '')
	const type = props.profile.type

	if (!rawValue.trim()) {
		return
	}

	getAutoCompleteCriteria({
		query: rawValue,
		type,
	})
		.then((response) => {
			const hasSuggestions = response.length > 0

			if (!hasSuggestions) {
				showAutoComplete.value = false
				return
			}

			autocompleteSuggestions.value = response
		})
		.catch((error) => {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
		})
}, 100)

const shouldWatchCriteriaToAdd = ref(true)
watch(criteriaToAdd, (valueOfCriteriaToAdd) => {
	if (!shouldWatchCriteriaToAdd.value) {
		return
	}

	if (['name', 'corporateName', 'pj'].includes(criteriaType.value)) {
		const minimumAmountOfCharactersToSearch = 3
		const hasMinimumAmountOfCharactersToSearch =
			valueOfCriteriaToAdd.length >= minimumAmountOfCharactersToSearch

		showAutoComplete.value = hasMinimumAmountOfCharactersToSearch

		if (showAutoComplete.value) {
			updateAutoCompleteSuggestions()

			nextTick(() => {
				autocompleteSuggestionsElement.value.style.maxHeight =
					window.innerHeight -
					autocompleteSuggestionsElement.value.getBoundingClientRect().top -
					132 +
					'px'
				autocompleteSuggestionsElement.value.style.overflowY = 'auto'
			})
		}
	}
})

const downloadXLSXExample = () => {
	const exportations = {
		name: exportFileTypeName,
		pf: exportFileTypeCPF,
		pj: exportFileTypeCnpj,
		corporateName: exportFileTypeCorporateReason,
	}

	exportations[criteriaType.value]()
}

const fileSelected = ref(null)
const errors = ref({
	file: null,
})

const isProcessingFile = ref(false)
watch(fileSelected, async () => {
	errors.value.file = null

	if (!fileSelected.value) {
		return
	}

	const isXLSX = /\.(xlsx)$/i.test(fileSelected.value.name)

	if (!isXLSX) {
		errors.value.file = {
			message: t('create.criteria.errorSpreadsheet'),
		}
		return
	}

	const formData = new FormData()
	formData.append('arquivo', fileSelected.value)
	formData.append('type', props.profile.type)

	try {
		isProcessingFile.value = true
		const { data } = await validateFileAndGetRows(formData)

		const criteriasFromFile = data.criterio.map((criteria) => ({
			value: criteria.value,
			isValid: criteria.isValid,
			type: criteriaType.value,
			dtNas: criteria.dtNas,
			motherName: criteria.motherNam,
			drive: criteria.drive,
			city: criteria.city,
			name: criteria.name,
			rg: criteria.rg,
			fatherName: criteria.fatherName,
		}))

		criterias.value = criteriasFromFile
	} catch (error) {
		console.log(error)

		if (error.response) {
			toast.addToast({
				content: error.response.data.message,
				type: 'error',
			})
		}
	} finally {
		isProcessingFile.value = false
	}
})

const alreadyExists = (criteriaToVerify) => {
	return criterias.value.some((criteria) => criteria.value === criteriaToVerify)
}

const addCriteria = () => {
	if (!criteriaToAdd.value.trim()) {
		return
	}

	if (alreadyExists(criteriaToAdd.value)) {
		return
	}

	const entityType = criteriaType.value

	const entity =
		entityType === 'pf'
			? new CPFEntity(criteriaToAdd.value)
			: entityType === 'pj'
			? new CNPJEntity(criteriaToAdd.value)
			: { value: criteriaToAdd.value, checkIsValid: () => true }

	let criteriaModel = {
		value: entity.value,
		type: entityType,
		isValid: entity.checkIsValid(),
		hasHomonyms: false,
	}

	criterias.value.push(criteriaModel)

	if (entityType === 'name') {
		checkForHomonyms(criteriaModel.value).then(({ data: homonyms }) => {
			if (homonyms.length > 1) {
				criterias.value = criterias.value.map((criteria) => {
					if (criteria.value === criteriaModel.value) {
						criteria.hasHomonyms = true
						criteria.amountOfHomonyms = homonyms.length
					}

					return criteria
				})
			}
		})
	}

	shouldWatchCriteriaToAdd.value = true
	showAutoComplete.value = false
	criteriaToAdd.value = ''
}

const deleteCriteria = (criteriaToDelete) => {
	const indexToDelete = criterias.value.findIndex(
		(criteria) => criteria.value === criteriaToDelete.value
	)
	criterias.value.splice(indexToDelete, 1)
}

const toast = useToast()

const detailsQueryProfileSelected = ref()
const isOpen = ref(false)
const isCollapsed = ref(false)

onBeforeMount(() => {
	getDetails()
})

function getDetails() {
	getQueryProfileDetails({
		profileId: props.profile.id,
		mode: props.profile.isDefault ? 'default' : 'custom',
	})
		.then(({ data: response }) => {
			detailsQueryProfileSelected.value = response
			isOpen.value = true
		})
		.catch((error) => {
			if (error.response) {
				toast.addToast({
					type: 'error',
					content: error.response.data.message,
				})
			}
		})
}

const emits = defineEmits(['previousStep', 'nextStep'])

const goToNextStep = () => {
	const validCriterias = criterias.value.filter((criteria) => criteria.isValid)

	emits('nextStep', {
		stepData: {
			criterias: validCriterias,
			file: fileSelected.value && fileSelected.value.name,
		},
	})
}

const goToPreviousStep = () => {
	emits('previousStep')
}
</script>

<style scoped>
@import url(./Criteria.scss);
</style>
