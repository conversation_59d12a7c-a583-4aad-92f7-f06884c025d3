<template>
	<div style="position: relative">
		<main class="main-content">
			<div
				class="flex items-center justify-between"
				style="padding-bottom: 16px">
				<h1 class="font-bold-700 text-sm" style="font-weight: 600">
					{{ $t('create.title') }}
				</h1>

				<RouterLink to="/dossiers">
					<UpButton type="tertiary">{{
						$t('create.buttonBackToDossiersHistory')
					}}</UpButton>
				</RouterLink>
			</div>
			<section
				style="
					display: grid;
					width: 100%;
					max-width: 100%;
					grid-template-columns: 100%;
					grid-template-rows: auto 1fr;
					min-height: 0;
				">
				<UpStepper
					class="stepper"
					style="padding-bottom: 24px"
					v-model="currentStep"
					:items="itemsStepper" />

				<SelectQueryProfile
					v-if="currentStep.href === 'query-profile'"
					:initial-profile="dossier.profile"
					:initial-type="initialType"
					@next-step="nextStep"
					@previousStep="goToPreviousStep" />

				<Criteria
					v-if="currentStep.href === 'criteria'"
					:initial-criterias="dossier.criterias"
					:profile="dossier.profile"
					@previousStep="goToPreviousStep"
					@nextStep="nextStep" />

				<Tag
					:initial-tag="dossier.tag"
					v-if="currentStep.href === 'tag'"
					@nextStep="nextStep"
					@previousStep="goToPreviousStep"
					:has-next-step="itemsStepper[2].nextStep" />

				<Parametrizations
					v-if="currentStep.href === 'parametrizations'"
					:initial-parametrizations="dossier.parametrizations"
					:profile="dossier.profile"
					@nextStep="nextStep"
					@previousStep="goToPreviousStep" />
			</section>

			<DossierDuplicityDialog
				:items="duplicateDossies.dossies"
				:daysDuplication="duplicateDossies.dias_duplicidade"
				v-if="showDossierDuplicateDialog"
				@closeDialogDuplicate="handleCloseDialogDuplicate"
				@confirmActionDuplicate="handleConfirmActionDuplicate" />

			<DossierCriationDialog
				:amount-of-criterias="dossier.criterias.length"
				v-if="showDossierCreationDialog"
				@confirm-action="sendDossierCreation"
				@close-dialog="toggleShowDossierCreationDialogTo(false)" />

			<DossierAdditionalDialog
		        :additional-dossies="additional"
				v-if="showDossierAdditionalDialog"
				@confirm-action-additional="toggleShowDossierCAdditional(true)"
				@close-dialog-additional="showDossierAdditionalDialog = false" />
		</main>
	</div>
</template>

<script setup>
document.querySelector('html').style = 'font-size: 100% !important;'

import Criteria from './componentes/FormSteps/Criteria/Criteria.vue'
import SelectQueryProfile from './componentes/FormSteps/SelectQueryProfile/SelectQueryProfile.vue'
import Tag from './componentes/FormSteps/Tag/Tag.vue'
import { useI18n } from 'vue-i18n'
import { computed, onBeforeMount, ref } from 'vue'
import { createDossier, doubleStandards, validateDossieAdditional } from './services/dossier_create'
import { useRouter } from 'vue-router'
import Parametrizations from './componentes/FormSteps/Parametrizations/Parametrizations.vue'
import DossierCriationDialog from './componentes/DossierCreationDialog.vue'
import DossierAdditionalDialog from './componentes/DossierAdditionalDialog.vue'
import { useToast } from '@aplicativo/uplexis-ui'
import DossierDuplicityDialog from './componentes/DossierDuplicityDialog.vue'
import { useQueryClient } from '@tanstack/vue-query'

const { t } = useI18n({})

const dossier = ref({
	criterias: [],
	profile: null,
	ignoreDuplicityBlock: true,
	parametrizations: {},
	tag: null,
})

const initialType = ref('pf')
onBeforeMount(() => {
	const searchParams = new URLSearchParams(window.location.search)

	const hasCriteria = searchParams.has('documento')
	const hasProfileType = searchParams.has('tipo_documento')

	if (hasCriteria && hasProfileType) {
		const criteria = searchParams.get('documento')
		const profileType = searchParams.get('tipo_documento')

		dossier.value.criterias.push({ value: criteria, isValid: true })
		initialType.value = profileType === 'cnpj' ? 'pj' : 'pf'
	}
})

const sourcesWithParametrizations = [
	'bancoCentralCrsfnEmentasAcordaos',
	'ppeTitularPaga',
	'CertidaoCeatTrtCampinasRegiao',
	'TcuCertidaoContasIrregulares',
	'CertidaoAcoesExecucoesCiveisCriminaisTrf',
	'CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf',
	'CertidaoJudicialTrf4',
	'CertidaoCgu',
	'Comprot',
	'googleGlobal',
	'googleGlobalNotSpecific',
	'MinisterioTurismoCadastur',
	'PessoaGoldBoaVista',
	'ppeTitularPrivada',
	'consultaSocio',
	'spcLocalizaMix',
	'diariosOficiais',
	'cade',
	'BaseOffEscavador',
	'diariosOficiaisPaga',
	'BaseOffEscavadorV2',
	'TjPrAntecedentesEleitorais',
	'kurrier',
	'Djen',
	'FontesJuridicas',
	'ProcessosJuridicos',
	'mpfRelevancia'
]

const checkIfSourceHasParametrization = (source) => {
	return sourcesWithParametrizations.includes(source.captura)
}

const concludedSteps = ref([])
const itemsStepper = computed(() => {
	const hasParametrizations =
		Object.keys(dossier.value.parametrizations).length > 0

	const baseList = [
		{
			label: t('create.breadcrumb.queryProfile'),
			href: 'query-profile',
			active: currentStep.value.href === 'query-profile',
			nextStep: 'criteria',
			previousStep: null,
			disabled: false,
		},
		{
			label: t('create.breadcrumb.criteria'),
			href: 'criteria',
			active: currentStep.value.href === 'criteria',
			previousStep: 'query-profile',
			nextStep: 'tag',
			disabled: !concludedSteps.value.includes('criteria'),
		},
		{
			label: t('create.breadcrumb.tag'),
			href: 'tag',
			active: currentStep.value.href === 'tag',
			previousStep: 'criteria',
			nextStep: hasParametrizations ? 'parametrizations' : null,
			disabled:
				!concludedSteps.value.includes('tag') ||
				!dossier.value.criterias.length,
		},
	]
	return hasParametrizations
		? [
				...baseList,
				{
					label: t('create.breadcrumb.parametrizations'),
					href: 'parametrizations',
					active: currentStep.value.href === 'parametrizations',
					previousStep: 'tag',
					nextStep: null,
					disabled:
						!concludedSteps.value.includes('parametrizations') ||
						!dossier.value.criterias.length,
				},
		  ]
		: baseList
})

const currentStep = ref({
	label: t('create.breadcrumb.queryProfile'),
	href: 'query-profile',
	active: true,
	nextStep: 'criteria',
	previousStep: null,
	disabled: false,
})

const showDossierAdditionalDialog = ref(false)
const additional = ref(0)

const showDossierCreationDialog = ref(false)
const showDossierDuplicateDialog = ref(false)
const toggleShowDossierCreationDialogTo = (shouldShow) => {
	showDossierCreationDialog.value = shouldShow
}

const toggleShowDossierCAdditional = (shouldShow) => {
	showDossierAdditionalDialog.value = false
	showDossierCreationDialog.value = shouldShow
}
const duplicateDossies = ref(null)

const toast = useToast()
const queryClient = useQueryClient()

const handleDossierCreation = async () => {
	try {
	const { data } =  await validateDossieAdditional(dossier.value)
 	
	additional.value = data.extra
	
	if(data.extra === 0 ){
		toggleShowDossierCreationDialogTo(true)
	}else{
		showDossierAdditionalDialog.value = true
	}

	} catch  {
		toggleShowDossierCreationDialogTo(true)
	}
}

const sendDossierCreation = async () => {
	try {
		const { data } = await createDossier(dossier.value)
		queryClient.invalidateQueries({ queryKey: ['dossiers'] })
		toggleShowDossierCreationDialogTo(false)
		toast.addToast({
			type: 'success',
			content: t('create.toastDossierCreatedSuccess'),
		})
		router.push('/dossiers')
	} catch (error) {
		toggleShowDossierCreationDialogTo(false)

		if (error.response) {
			toast.addToast({
				content: error.response.data.message,
				type: 'error',
			})
		}
	}
}

const handleCloseDialogDuplicate = () => {
	showDossierDuplicateDialog.value = false
}

const handleConfirmActionDuplicate = async () => {
	showDossierDuplicateDialog.value = false

	handleDossierCreation()
}

const handleProfileSelection = (newProfile) => {
	initialType.value = newProfile.type

	const isFirstTimeAProfileIsSelected = dossier.value.profile === null
	if (isFirstTimeAProfileIsSelected) {
		dossier.value.profile = newProfile
	}

	if (!isFirstTimeAProfileIsSelected) {
		const currentProfileName = dossier.value.profile.nome
		const newProfileName = newProfile.nome

		const isSameProfile = currentProfileName === newProfileName
		if (!isSameProfile) {
			dossier.value = {
				profile: newProfile,
				criterias: [],
				ignoreDuplicityBlock: true,
				parametrizations: {},
				tag: null,
			}

			concludedSteps.value = []
		}
	}

	dossier.value.profile.sources.saidas.forEach((source) => {
		if (checkIfSourceHasParametrization(source)) {
			dossier.value.parametrizations[source.captura] = null
		}
	})
}

const nextStep = async ({ stepData }) => {
	if (currentStep.value.href === 'query-profile') {
		handleProfileSelection(stepData.profile)
	}

	dossier.value = {
		...dossier.value,
		...stepData,
	}

	const next = itemsStepper.value.find(
		(step) => step.href === currentStep.value.nextStep
	)

	if (!next) {
		const criterias = dossier.value.criterias.map((criteria) => criteria.value)
		duplicateDossies.value = await doubleStandards(criterias)

		if (duplicateDossies.value?.dossies?.length > 0) {
			showDossierDuplicateDialog.value = true
			return
		} else {
			handleDossierCreation()
			return
		}
	}
	concludedSteps.value.push(currentStep.value.href)
	currentStep.value = next
}

const router = useRouter()
const goToPreviousStep = () => {
	const previousStep = currentStep.value.previousStep

	if (!previousStep) {
		router.push('/dossiers')
	}

	currentStep.value = itemsStepper.value.find(
		(step) => step.href === previousStep
	)
}
</script>

<style scoped lang="scss">
.main-content {
	padding-top: 32px;
	padding-bottom: 32px;
	width: max(1220px, 95%);
	max-width: max(1220px, 95%);
	height: calc(100dvh - 100px);
	max-height: calc(100dvh - 100px);
	overflow: hidden;
	display: grid;
	grid-template-columns: 100%;
	grid-template-rows: auto 1fr;
	margin-inline: auto;

	@media screen and (max-height: 700px) {
		padding-top: 16px;
		padding-bottom: 16px;
	}
}

.stepper :deep(.font-bold) {
	font-weight: 600 !important;
}
</style>
