<script setup>
import ActiveProfilesTable from '@/modules/profile/list/components/ActiveProfilesTable'
import InactiveProfilesTable from '@/modules/profile/list/components/InactiveProfilesTable'
import DefaultProfilesTable from '@/modules/profile/list/components/DefaultProfilesTable'
import SourcesSelection from '@/modules/profile/list/components/SourcesSelection'
import { useSelectedItems } from '@/composables/useSelectedItems'
import UserGroupsSelection from '@/components/UserGroupsSelection'
import { useModal } from '@/composables/useModal'
import Modals from '@/components/Modals/index.vue'
import { Icon } from '@iconify/vue'
import { ref, computed, onBeforeMount, watch, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import {
	deactivateProfile,
	deleteProfileInServer,
	exportProfileInServer,
	getProfileAttachedMonitoring,
	getProfiles,
} from './services/profiles'
import { useToast } from '@aplicativo/uplexis-ui'
import { useUserStore } from '../../../stores/userStore'
import { storeToRefs } from 'pinia'
import { useGroupsStore } from '../stores/groups'
import { useCategoriesStore } from '../stores/categories'
import AlertAboutProfileWithMonitoring from './components/Modals/AlertAboutProfileWithMonitoring.vue'
import HintToCreateWorkflowAndScore from './components/Modals/HintToCreateWorkflowAndScore.vue'
import { useHintsStore } from '../stores/hints'
import Window from '@/components/Window/Window.vue'
import Workflow from './windows/Workflow.vue'

import UpScoreWindow from './windows/Upscore.vue'
import { getAllSourcesThatCanBeAttachedToScore } from './services/upscore'

const router = useRouter()

const showMonitoringWarning = ref({
	isOpen: false,
	data: null,
})

const checkPermissionToCreateOrEditProfile = (callback) => {
	const canAccessSearchProfile = userStore.checkIfUserHasPermissionTo(
		'access-search-profile'
	)
	const canCreateOrEditSearchProfile = userStore.checkIfUserHasPermissionTo(
		'edit-search-profile'
	)

	if (!canAccessSearchProfile || !canCreateOrEditSearchProfile) {
		toast.addToast({
			type: 'warning',
			content: t('common.notAllowedToUseFunctionality'),
		})
		return
	}

	return callback()
}

const goToCreateProfile = () => {
	router.push('/search-profiles/create')
}

const openedWindow = ref({ name: '', data: null })

const openWindow = ({ name, data }) => {
	openedWindow.value = { name, data }
}

const closeWindow = () => {
	openedWindow.value = { name: '', data: null }
}

const onSaveWorkflow = () => {
	openedWindow.value = { name: '', data: null }
	updateProfiles()
}

const goToEditProfile = async ({
	id,
	isActive,
	isDefault,
	skipCheckForMonitoring,
}) => {
	if (!skipCheckForMonitoring) {
		const { data: monitorings } = await getProfileAttachedMonitoring({
			profileId: id,
		})

		const hasMonitoringAttached = !Array.isArray(monitorings)

		if (hasMonitoringAttached) {
			showMonitoringWarning.value = {
				isOpen: true,
				data: { id, isActive, isDefault },
			}
			return
		}
	}

	const queryString = new URLSearchParams({ isActive, isDefault }).toString()
	router.push(`/search-profiles/edit/${id}?${queryString}`)
}

const { currentModal, setCurrentModal, closeModal } = useModal()
function onCloseModal() {
	setCurrentModal({ name: '', data: {} })
}

const userStore = useUserStore()
const { user } = storeToRefs(userStore)
const selectedTab = ref('')
const tabs = computed(() => {
	const baseTabs = [
		{ label: t('searchProfile.list.active_profiles'), value: 'actives' },
		{
			label: t('searchProfile.list.inactive_profiles'),
			value: 'inactives',
		},
	]

	if (user.value && user.value.isAdmin) {
		baseTabs.push({
			label: t('searchProfile.list.default_profiles'),
			value: 'default',
		})
	}

	return baseTabs
})
watch(selectedTab, () => {
	const shouldRetrieveActives = selectedTab.value === 'actives'

	filters.value.active = shouldRetrieveActives
	updateProfiles(true)
})

const openUpscore = (row) => {
	openWindow({
		name: 'upscore',
		data: row,
	})
}

const dataWorkflow = ref({
	idProfile: null,
	idGroup: null,
	isEdit: false,
	sourcesProfile: [],
})

function dataOpenWorkflow(profileId, groupId, isEdit, sources) {
	dataWorkflow.value.idProfile = profileId
	dataWorkflow.value.idGroup = groupId
	dataWorkflow.value.isEdit = isEdit
	dataWorkflow.value.sourcesProfile = sources
}

const createNewWorkflow = (row) => {
	const { group_id } = user.value
	const profileId = row.id

	const hasPermissionToAcessWorkflow = userStore.checkIfUserHasPermissionTo(
		'access-workflow-search-profile'
	)
	const hasPermissionToEditWorkflow = userStore.checkIfUserHasPermissionTo(
		'edit-workflow-search-profile'
	)

	if (!hasPermissionToAcessWorkflow || !hasPermissionToEditWorkflow) {
		return toast.addToast({
			type: 'warning',
			content: t('common.notAllowedToUseFunctionality'),
		})
	}

	dataOpenWorkflow(
		profileId,
		group_id,
		row.statusWorkflow === 'Ativo',
		row.sources
	)

	openWindow({
		name: 'workflow',
		data: dataWorkflow.value,
	})

	// redirecionamento workflow atual
	// window.open(
	// 	`${
	// 		import.meta.env.VITE_APP_urlBaseUpMiner
	// 	}/master/workflow/criar/${profileId}/${group_id}`,
	// 	'_blank'
	// )
}

const groupsStore = useGroupsStore()
const { groups } = storeToRefs(groupsStore)

const categoriesStore = useCategoriesStore()
const { categories } = storeToRefs(categoriesStore)

const { t } = useI18n()

const profiles = ref([])
const pagination = ref({
	page: 1,
	per_page: 15,
})

const setNewPage = (page) => {
	pagination.value.page = page
	updateProfiles()
}

const filters = ref({
	profileName: '',
	group: null,
	active: true,
})

const isLoadingProfiles = ref(false)

const tableProfileRef = ref(null)
const updateProfiles = async (shouldIgnoreFilters = false) => {
	try {
		isLoadingProfiles.value = true

		const { clientHeight: TABLE_TOTAL_HEIGHT } = tableProfileRef.value
		const ROW_HEIGHT = 36
		const PADDING_BOTTOM = 64
		const PAGINATION_HEIGHT = 32

		const amountOfRows = Math.ceil(
			(TABLE_TOTAL_HEIGHT - (PADDING_BOTTOM + PAGINATION_HEIGHT)) / ROW_HEIGHT
		)

		const { data, pagination: paginationFromServer } = await getProfiles({
			pagination: pagination.value,
			type: selectedTab.value,
			perPage: amountOfRows,
			filters: shouldIgnoreFilters
				? { active: filters.value.active, group: filters.value.group }
				: { ...filters.value, source: selectedSources.value[0] },
		})

		profiles.value = data.map((profile) => {
			const hasScore = profile.score

			const hasSourceToAttachScore = sourcesThatCanBeAttachedToScore.value.find(
				(source) => profile.sources.some(
						(profileSource) => profileSource.id === source.capturaId
					)
				
			)
			
			return {
				...profile,
				canAttachScore: Boolean(hasScore || hasSourceToAttachScore),
				statusUpScore:
					hasScore || hasSourceToAttachScore
						? profile.statusUpScore
						: 'Indisponível',
			}
		})
		pagination.value = paginationFromServer
	} catch (error) {
		console.log(error)
	} finally {
		isLoadingProfiles.value = false
	}
}

const {
	selectedItems: selectedSources,
	onSelect: onSelectSource,
	onSelectAll: onSelectAllSources,
	onDeleteAll: onDeleteAllSources,
	onSelectMultiples: onSelectMultiplesSources,
	onDeleteMultiples: onDeleteMultiplesSources,
} = useSelectedItems([], false)

const sourcesRaw = computed(() =>
	categories.value.reduce((acc, curr) => [...acc, ...curr.sources], [])
)

const inputSourcesLabel = computed(() =>
	selectedSources.value.map(({ label }) => label).join(', ')
)

function openDisableModal({ id }) {
	setCurrentModal({
		name: 'Confirm',
		data: {
			title: t('searchProfile.modals.disable_profile.title'),
			description: t('searchProfile.modals.disable_profile.description'),
			primaryButtonLabel: t(
				'searchProfile.modals.disable_profile.primary_button_label'
			),
			cancelAction: () => {
				document.querySelector(`#status-${id} .up-switch__labels`).click()
			},
			primaryButtonAction: async () => {
				try {
					await deactivateProfile({ id })
					updateProfiles()
				} catch (error) {
					console.log(error)
				} finally {
					closeModal()
				}
			},
		},
	})
}
function openDeleteProfileModal(row) {
	setCurrentModal({
		name: 'Confirm',
		data: {
			title: t('searchProfile.modals.delete_profile.title'),
			description: t('searchProfile.modals.delete_profile.description'),
			primaryButtonLabel: t(
				'searchProfile.modals.delete_profile.primary_button_label'
			),
			primaryButtonAction: async () => {
				try {
					await deleteProfileInServer({ id: row.id })
					updateProfiles()
					closeModal()
				} catch (error) {
					if (error.response) {
						toast.addToast({
							type: 'error',
							content: error.response.data.message,
						})
					}
				}
			},
		},
	})
}

function openDuplicateProfileModal(row) {
	setCurrentModal({
		name: 'DuplicateProfile',
		data: {
			baseProfileId: row.id,
			groups: groups.value,

			callbackAfterDuplicating: updateProfiles,
		},
	})
}

function openProfileModal(data) {
	setCurrentModal({
		name: 'Profile',
		data: {
			...data,
			profileType: selectedTab.value,
		},
	})
}

const toast = useToast()
const exportProfile = async ({ id, name }) => {
	try {
		const response = await exportProfileInServer({ id })
		const link = document.createElement('a')
		// Create a URL for the Blob and set it as the href attribute of the link
		link.href = window.URL.createObjectURL(response)
		// Set the download attribute to the desired file name
		link.download = `${name}.xls`
		// Append the link to the body
		document.body.appendChild(link)
		// Programmatically click the link to trigger the download
		link.click()
		// Remove the link from the document
		document.body.removeChild(link)
	} catch (error) {
		console.log(error)
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

const sourcesThatCanBeAttachedToScore = ref([])
onBeforeMount(async () => {
	filters.value.group = groups.value.find(
		(group) => group.id === user.value.group_id
	)
	const promises = [
		getAllSourcesThatCanBeAttachedToScore({ type: 'pf' }),
		getAllSourcesThatCanBeAttachedToScore({ type: 'pj' }),
	]

	const [
		{ value: sourcesPfThatCanBeAttached },
		{ value: sourcesPjThatCanBeAttached },
	] = (await Promise.allSettled(promises)).filter(
		(result) => result.status === 'fulfilled'
	)

	sourcesThatCanBeAttachedToScore.value = [
		...sourcesPfThatCanBeAttached.categories,
		...sourcesPjThatCanBeAttached.categories,
	]

	updateProfiles()
})

const hintsStore = useHintsStore()
const { hintToShow } = storeToRefs(hintsStore)
</script>
<template>
	<div
		class="home h-[calc(100dvh-105px)] w-[max(1220px,95%)] mx-auto pt-4 pb-3 grid grid-cols-1 grid-rows-[max-content_max-content_max-content_1fr]">
		<Modals @onClose="onCloseModal" v-bind="currentModal" />
		<Transition name="slide-up">
			<HintToCreateWorkflowAndScore
				v-if="hintToShow === 'hintToCreateWorkflowAndScore'" />
		</Transition>
		<AlertAboutProfileWithMonitoring
			v-model="showMonitoringWarning.isOpen"
			@confirmUnderstanding="
				(profile) =>
					goToEditProfile({ ...profile, skipCheckForMonitoring: true })
			"
			:profile="showMonitoringWarning.data" />
		<div class="flex justify-between items-center mb-3">
			<h1 class="font-semibold text-sm text-neutral-dark-pure">
				{{ $t('searchProfile.list.query_profiles') }}
			</h1>
			<nav class="flex items-center gap-2">
				<RouterLink to="/dossiers">
					<UpButton type="tertiary">
						{{ $t('searchProfile.goBackToDossiers') }}
					</UpButton>
				</RouterLink>
				<UpButton
					style="border-style: solid"
					type="secondary"
					@click="
						() =>
							checkPermissionToCreateOrEditProfile(() => goToCreateProfile())
					">
					{{ $t('searchProfile.list.create_new_profile') }}
				</UpButton>
			</nav>
		</div>

		<div class="flex gap-3 items-end">
			<div class="flex-1">
				<label class="text-us text-neutral-dark-3 mb-1">
					{{ $t('searchProfile.list.profile_name') }}
				</label>
				<UpInput
					v-model="filters.profileName"
					class="[&>label]:w-full"
					v-bind="{
						props: {
							type: 'text',
							disabled: false,
							placeholder: $t('searchProfile.list.type_profile_name'),
						},
					}" />
			</div>

			<div class="flex-1">
				<label class="text-us text-neutral-dark-3 mb-1">{{
					$t('common.source')
				}}</label>
				<SourcesSelection
					:value="inputSourcesLabel"
					:multiple="false"
					:placeholder="$t('searchProfile.list.select_source')"
					:data="categories"
					:dataSelected="selectedSources"
					:sourcesRaw="sourcesRaw"
					@onSelectAll="onSelectAllSources(sourcesRaw)"
					@onSelect="onSelectSource"
					@onSelectMultiples="onSelectMultiplesSources"
					@onDeleteAll="onDeleteAllSources(sources)"
					@onDeleteMultiples="onDeleteMultiplesSources" />
			</div>

			<div class="flex-1">
				<label class="text-us text-neutral-dark-3 mb-1 block">{{
					$t('common.group')
				}}</label>
				<UserGroupsSelection
					:multiple="false"
					:value="filters?.group?.label || ''"
					:placeholder="$t('searchProfile.list.select_user_groups')"
					:items="groups"
					@onSelect="(group) => (filters.group = group)" />
			</div>
			<UpButton @click="updateProfiles(false)">{{
				$t('common.search')
			}}</UpButton>
		</div>

		<UpTab
			v-model:tab="selectedTab"
			:showArrows="false"
			class="[&>div]:px-[0]"
			:items="tabs" />

		<div class="-mt-3" ref="tableProfileRef">
			<template v-if="isLoadingProfiles">
				<v-skeleton-loader
					:elevation="2"
					class="custom-files"
					type="table-tbody"></v-skeleton-loader>
			</template>

			<template v-if="!isLoadingProfiles">
				<div
					class="flex flex-col gap-[12px] pt-[168px] w-full justify-center items-center text-center"
					v-if="!profiles.length">
					<Icon
						icon="material-symbols:inbox-outline"
						width="43"
						height="43"
						class="text-neutral-light-1" />

					<p class="text-xxxs text-neutral-dark-1">
						{{ $t('searchProfile.list.notFound') }}
					</p>
				</div>
				<template v-else>
					<ActiveProfilesTable
						v-if="selectedTab === 'actives'"
						:pagination="pagination"
						:data="profiles"
						@onClickEdit="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									goToEditProfile({
										...row,
										isDefault: false,
										isActive: true,
										skipCheckForMonitoring: false,
									})
								)
						"
						@onClickUpScore="
							(row) =>
								checkPermissionToCreateOrEditProfile(() => openUpscore(row))
						"
						@onClickWorkflow="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									createNewWorkflow(row)
								)
						"
						@onClickInfo="openProfileModal"
						@onClickStatus="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									openDisableModal(row)
								)
						"
						@onClickCopy="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									openDuplicateProfileModal(row)
								)
						"
						@onClickDownload="exportProfile"
						@onChangePage="setNewPage" />

					<InactiveProfilesTable
						v-if="selectedTab === 'inactives'"
						:pagination="pagination"
						:data="profiles"
						@onClickEdit="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									goToEditProfile({
										...row,
										isDefault: false,
										isActive: false,
										skipCheckForMonitoring: false,
									})
								)
						"
						@onClickUpScore="
							(row) =>
								checkPermissionToCreateOrEditProfile(() => openUpscore(row))
						"
						@onClickInfo="openProfileModal"
						@onClickCopy="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									openDuplicateProfileModal(row)
								)
						"
						@onClickDownload="exportProfile"
						@onChangePage="setNewPage"
						@onDelete="
							(row) =>
								checkPermissionToCreateOrEditProfile(() =>
									openDeleteProfileModal(row)
								)
						" />
					<template v-if="user.isAdmin">
						<DefaultProfilesTable
							v-if="selectedTab === 'default'"
							:pagination="pagination"
							:data="profiles"
							@onChangePage="setNewPage"
							@onClickEdit="
								(row) =>
									checkPermissionToCreateOrEditProfile(() =>
										goToEditProfile({
											...row,
											isDefault: true,
											isActive: true,
											skipCheckForMonitoring: true,
										})
									)
							"
							@onClickInfo="openProfileModal" />
					</template>
				</template>
			</template>
		</div>

		<UpScoreWindow
			@update-profiles="() => updateProfiles()"
			@close-window="closeWindow"
			:is-open="openedWindow.name === 'upscore'"
			:data="openedWindow.data" />

		<Window
			@on-close="closeWindow"
			:is-open="openedWindow.name === 'workflow'"
			:title="{
				svg: {
					name: 'workflow.svg',
					width: 20,
					height: 20,
					fill: '#7922B9',
				},
				text: 'Workflow',
			}"
			size="large">
			<Workflow
				:idProfile="dataWorkflow?.idProfile"
				:idGroup="dataWorkflow?.idGroup"
				:isEdit="dataWorkflow?.isEdit"
				:sourcesProfile="dataWorkflow?.sourcesProfile"
				@save-workflow="onSaveWorkflow"
				@cancel-workflow="closeWindow" />
		</Window>
	</div>
</template>

<style scoped>
.slide-up-enter-active {
	transition: all 0.6s ease-out;
}

.slide-up-leave-active {
	transition: all 0.8s ease-in;
}

.slide-up-enter-from,
.slide-up-leave-to {
	transform: translate(-50%, 100%);
}
</style>
