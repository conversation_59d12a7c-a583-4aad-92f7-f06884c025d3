import { i18n } from '@/lang'
const { t } = i18n.global
const mapProfile = ({
	name,
	status,
	type,
	idPerfil,
	version,
	sources,
	groups,
	workflows,
	score,
	score_active
}) => ({
	name,
	status: status ? t('common.active') : t('common.inactive'),
	type,
	id: idPerfil,
	version,
	groups,
	sources: sources.filter(Boolean).map(({ idSource, nameSource }) => ({
		id: idSource,
		label: nameSource,
		name: nameSource,
	})),
	score,
	statusUpScore: score === null 
	? t('common.configure') 
	: !score_active 
	  ? t('common.inactive') 
	  : score 
		? t('common.active') 
		: t('common.inactive'),
	statusWorkflow:
		workflows && workflows.length > 0
			? t('common.active')
			: t('common.configure'),
	labelType: type === 1 ? t('common.person') : t('common.legalEntity'),
	objective:
		'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. ',
})

export const mapProfiles = ({ data, current_page, total, per_page }) => {
	return {
		data: data.map(mapProfile),
		pagination: {
			currentPage: current_page,
			totalItems: total,
			perPage: per_page,
		},
	}
}

export const mapGetWorkflow = (workflowData, sources) => {
	const selectedOptionLevels = {
		level1: [],
		level2: [],
		level3: [],
		level4: [],
	}

	if (workflowData.levels) {
		if (Array.isArray(workflowData.levels)) {
			if (workflowData.levels[0]) {
				selectedOptionLevels.level1 = workflowData.levels[0].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
			if (workflowData.levels[1]) {
				selectedOptionLevels.level2 = workflowData.levels[1].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
			if (workflowData.levels[2]) {
				selectedOptionLevels.level3 = workflowData.levels[2].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
			if (workflowData.levels[3]) {
				selectedOptionLevels.level4 = workflowData.levels[3].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
		} else if (typeof workflowData.levels === 'object') {
			if (workflowData.levels['1']) {
				selectedOptionLevels.level1 = workflowData.levels['1'].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
			if (workflowData.levels['2']) {
				selectedOptionLevels.level2 = workflowData.levels['2'].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
			if (workflowData.levels['3']) {
				selectedOptionLevels.level3 = workflowData.levels['3'].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
			if (workflowData.levels['4']) {
				selectedOptionLevels.level4 = workflowData.levels['4'].map((user) => ({
					id: user.id,
					label: user.fullname,
				}))
			}
		}
	}

	const additionalFields = workflowData.automaticApprove.map((field) => {
		const automaticApprovalStates = {
			words: field.tipo === 'palavra',
			noWords: field.tipo === 'sem-palavra',
			hasResult: field.tipo === 'consta',
			noResult: field.tipo === 'nao-consta',
		}

		const matchedSource = sources.find(
			(source) => source.id === field.id_captura
		)

		return {
			sourceSelected: {
				id: field.id_captura,
				label: matchedSource ? matchedSource.label : undefined,
			},
			automaticApprovalStates,
			keyword: '',
			keywords: field.palavras ? JSON.parse(field.palavras) : [],
			status: field.status,
			filterDossier: field.filtro_dossie,
		}
	})

	const automaticApprovals = workflowData.automaticApprove.length > 0

	return {
		selectedOptionLevels,
		additionalFields,
		automaticApprovals,
	}
}
