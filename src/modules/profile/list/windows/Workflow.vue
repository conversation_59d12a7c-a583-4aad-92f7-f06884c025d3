<template>
	<main>
		<div
			v-show="isLoading"
			style="
				height: 100dvh;
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
			">
			<Loading :size="36" color="#7922B9" />
		</div>

		<section v-show="!isLoading" style="margin-top: 8px" class="main-container">
			<div class="content-container">
				<article>
					<p class="workflow-description">
						{{ $t('searchProfile.workflow.description') }}
					</p>

					<div>
						<Accordion
							v-for="(level, index) in levels"
							:key="index"
							:title="$t(level.title)"
							:initialOpen="index === 0">
							<p class="user-level">{{ $t(level.user) }}</p>

							<label class="label">{{
								$t('searchProfile.workflow.users')
							}}</label>

							<div class="user-select">
								<UpSelect
									:options="level.filteredOptionsUsers"
									v-model="level.selectedOption"
									:searchable="true"
									allowMultiple
									@search="(searchTerm) => handleSearch(searchTerm, index)"
									:placeholder="
										$t('searchProfile.workflow.selectUserPlaceholder')
									"
									:disabled="index > 0 && levels[index - 1].selectedOption.length === 0"
									>
									<template #selected-options>
										<span
											v-if="level.selectedOption.length === 0"
											style="text-align: left">
											{{ $t('searchProfile.workflow.noUserSelected') }}
										</span>
										<span
											v-else-if="
												level.selectedOption.length ===
												level.filteredOptionsUsers.length
											"
											style="text-align: left">
											{{ $t('searchProfile.workflow.allSelected') }}
										</span>
										<span
											v-else
											class="selected-options-text"
											style="
												text-align: left;
												white-space: nowrap;
												overflow: hidden;
												text-overflow: ellipsis;
											">
											{{
												level.selectedOption
													.map((option) => option.label)
													.join(', ')
											}}
										</span>
									</template>
								</UpSelect>
							</div>

							<ul class="user-list">
								<li
									class="user"
									v-for="(item, itemIndex) in level.selectedOption"
									:key="itemIndex">
									{{ item.label }}
									<button @click="removeUser(item.id, index + 1)">
										<NewInlineSvg
											name="close_small.svg"
											width="16"
											height="16"
											fill="#666666" />
									</button>
								</li>
							</ul>
						</Accordion>
					</div>

					<UpCheckbox
						:disabled="!isButtonEnabled"
						style="margin-top: 24px"
						:label="$t('searchProfile.workflow.autoApproval')"
						v-model:checked="automaticApproval" />
				</article>

				<article v-if="automaticApproval">
					<section
						v-for="(field, index) in additionalFields"
						:key="index"
						class="automatic-options">
						<label class="label">{{
							$t('searchProfile.workflow.source')
						}}</label>
						<div class="user-select">
							<UpSelect
								:options="filteredOptionsSources"
								v-model="field.sourceSelected"
								:searchable="true"
								@search="(searchTerm) => handleSearchSource(searchTerm)"
								:placeholder="
									$t('searchProfile.workflow.selectSourcePlaceholder')
								">
								{{ field.sourceSelected?.name }}
							</UpSelect>
						</div>
						<div class="flex gap-2">
							<UpCheckbox
								style="margin-top: 24px"
								:class="{
									'checked-checkbox': field.automaticApprovalStates.words,
								}"
								:label="$t('searchProfile.workflow.wordsLabel')"
								:disabled="false"
								:checked="field.automaticApprovalStates.words"
								@update:checked="handleCheckboxChange(field, 'words')" />

							<UpCheckbox
								style="margin-top: 24px"
								:class="{
									'checked-checkbox': field.automaticApprovalStates.noWords,
								}"
								:label="$t('searchProfile.workflow.noWordsLabel')"
								:disabled="false"
								:checked="field.automaticApprovalStates.noWords"
								@update:checked="handleCheckboxChange(field, 'noWords')" />

							<UpCheckbox
								style="margin-top: 24px"
								:class="{
									'checked-checkbox': field.automaticApprovalStates.hasResult,
								}"
								:label="$t('searchProfile.workflow.hasResultLabel')"
								:disabled="false"
								:checked="field.automaticApprovalStates.hasResult"
								@update:checked="handleCheckboxChange(field, 'hasResult')" />

							<UpCheckbox
								style="margin-top: 24px"
								class="checkbox"
								:class="{
									'checked-checkbox': field.automaticApprovalStates.noResult,
								}"
								:label="$t('searchProfile.workflow.noResultLabel')"
								:disabled="false"
								:checked="field.automaticApprovalStates.noResult"
								@update:checked="handleCheckboxChange(field, 'noResult')" />
						</div>

						<div
							v-if="
								field.automaticApprovalStates.words ||
								field.automaticApprovalStates.noWords
							"
							class="mt-3">
							<span class="description-keyword">{{
								$t('searchProfile.workflow.addKeywords')
							}}</span>
							<div>
								<UpInput
									:props="{
										type: 'text',
										placeholder: $t(
											'searchProfile.workflow.addKeywordPlaceholder'
										),
										icon: {
											name: 'add',
										},
									}"
									v-model="field.keyword"
									@click="() => addKeyword(field)"
									@enter="() => addKeyword(field)" />
							</div>
							<div class="keywords-container">
								<div
									v-for="(word, i) in field.keywords"
									:key="i"
									class="keyword">
									{{ word }}
									<button @click="removeKeyword(field, i)">
										<NewInlineSvg
											name="close_small.svg"
											width="16"
											height="16"
											fill="#666666" />
									</button>
								</div>
							</div>
						</div>

						<div>
							<p class="description-status">
								{{ $t('searchProfile.workflow.statusDescription') }}
							</p>

							<div
								v-if="homonymIds.includes(field.sourceSelected?.id)"
								style="margin: 16px 0px 24px 0px"
								class="flex align-center gap-2">
								<UpCheckbox
									:class="{ 'checked-checkbox': field.isMatchHomonym }"
									:label="$t('searchProfile.workflow.homonyms')"
									v-model:checked="field.isMatchHomonym" />
								<UpTooltip
									v-bind="{
										side: 'top',
										delay: 0,
										tooltipText: $t('searchProfile.workflow.homonymsMessage'),
									}">
									<template #trigger>
										<NewInlineSvg
											name="error.svg"
											width="16"
											height="16"
											fill="#B8B8B8" />
									</template>
								</UpTooltip>
							</div>

							<div class="flex justify-between items-center">
								<div class="flex gap-2">
									<button
										:class="[
											'failed',
											field.status === 'reprovar' ? 'active-failed' : '',
										]"
										@click="setStatus(field, 'reprovar')">
										{{ $t('searchProfile.workflow.rejected') }}
									</button>
									<button
										:class="[
											'under-analysis',
											field.status === 'em_analise' ? 'active-analysis' : '',
										]"
										@click="setStatus(field, 'em_analise')">
										{{ $t('searchProfile.workflow.inAnalysis') }}
									</button>
								</div>
								<UpButton
									class="border-solid"
									type="tertiary"
									@click="removeField(index)">
									<NewInlineSvg
										name="delete.svg"
										width="16"
										height="16"
										fill="#7922B9" />
									{{ $t('searchProfile.workflow.deleteButton') }}
								</UpButton>
							</div>
						</div>
					</section>

					<p class="flex justify-end my-6">
						<UpButton
							class="border-solid"
							type="secondary"
							@click="addNewField">
							<NewInlineSvg
								name="more.svg"
								width="16"
								height="16"
								fill="#7922B9" />
							{{ $t('searchProfile.workflow.addFieldButton') }}
						</UpButton>
					</p>
				</article>
			</div>

			<footer>
				<UpButton class="border-solid" type="secondary" @click="handleCancel">
					{{ $t('searchProfile.workflow.backButton') }}
				</UpButton>

				<UpButton
					:disabled="!isButtonEnabled || isButtonLoading"
					@click="createWorkflowProfile"
					class="flex items-center">
					<Loading v-if="isButtonLoading" :size="20" color="#fff" />
					<span v-if="!isButtonLoading">
						{{ $t('searchProfile.workflow.saveButton') }}
					</span>
				</UpButton>
			</footer>
		</section>
	</main>
</template>

<script setup>
import Accordion from '../../../../components/Accordion/Accordion.vue'
import { ref, watch, onMounted, computed, reactive } from 'vue'
import {
	getUserGroup,
	createWorkflow,
	getWorkflow,
	getSourceWithHomonyms,
} from '../services/profiles/index'
import { mapGetWorkflow } from '../mappers/profiles/index'
import NewInlineSvg from '@/components/NewInlineSvg/NewInlineSvg.vue'
import { useToast } from '@aplicativo/uplexis-ui'
import Loading from '@/components/Loading/Loading.vue'

const props = defineProps({
	idProfile: { type: Number, required: true },
	idGroup: { type: Number, required: true },
	isEdit: { type: Boolean, required: true },
	sourcesProfile: { type: Object, required: true },
})

const toast = useToast()

const emit = defineEmits(['save-workflow', 'cancel-workflow'])

const isLoading = ref(true)
const isButtonLoading = ref(false)

const selectedOptionLevel1 = ref([])
const selectedOptionLevel2 = ref([])
const selectedOptionLevel3 = ref([])
const selectedOptionLevel4 = ref([])

const groupUsers = ref([])

const automaticApproval = ref(false)
const searchSourceTerm = ref('')
const homonymIds = ref([])

const additionalFields = ref([])

const isButtonEnabled = computed(() => {
	const hasSelectedUsers =
		selectedOptionLevel1.value.length > 0 ||
		selectedOptionLevel2.value.length > 0 ||
		selectedOptionLevel3.value.length > 0 ||
		selectedOptionLevel4.value.length > 0

	const hasValidAutomaticApproval =
		automaticApproval.value &&
		additionalFields.value.some((field) => field.sourceSelected !== null)

	return hasSelectedUsers || hasValidAutomaticApproval
})

const filteredOptionsSources = computed(() => {
	return props.sourcesProfile.filter((source) =>
		source.label.toLowerCase().includes(searchSourceTerm.value.toLowerCase())
	)
})

function handleSearchSource(searchTerm) {
	searchSourceTerm.value = searchTerm
}

const levels = ref([
	{
		title: 'searchProfile.workflow.level1Title',
		user: 'searchProfile.workflow.level1User',
		selectedOption: selectedOptionLevel1,
		filteredOptions: [...groupUsers.value],
	},
	{
		title: 'searchProfile.workflow.level2Title',
		user: 'searchProfile.workflow.level2User',
		selectedOption: selectedOptionLevel2,
		filteredOptions: [...groupUsers.value],
	},
	{
		title: 'searchProfile.workflow.level3Title',
		user: 'searchProfile.workflow.level3User',
		selectedOption: selectedOptionLevel3,
		filteredOptions: [...groupUsers.value],
	},
	{
		title: 'searchProfile.workflow.level4Title',
		user: 'searchProfile.workflow.level4User',
		selectedOption: selectedOptionLevel4,
		filteredOptions: [...groupUsers.value],
	},
])

const addNewField = () => {
	additionalFields.value.push({
		sourceSelected: null,
		automaticApprovalStates: {
			words: false,
			noWords: false,
			hasResult: false,
			noResult: false,
		},
		keyword: '',
		keywords: [],
		status: null,
		isMatchHomonym: false,
	})
}

const removeField = (index) => {
	additionalFields.value.splice(index, 1)
	if (additionalFields.value.length === 0) {
		automaticApproval.value = false
	}
}

const handleCancel = () => {
	emit('cancel-workflow')
}

const handleCheckboxChange = (field, option) => {
	field.automaticApprovalStates.words = false
	field.automaticApprovalStates.noWords = false
	field.automaticApprovalStates.hasResult = false
	field.automaticApprovalStates.noResult = false

	field.automaticApprovalStates[option] = true
}

watch(automaticApproval, (newValue) => {
	if (newValue && additionalFields.value.length === 0) {
		addNewField()
	}
})

const addKeyword = (field) => {
	if (field.keyword.trim() !== '') {
		field.keywords.push(field.keyword.trim())
		field.keyword = ''
	}
}

const removeKeyword = (field, index) => {
	field.keywords.splice(index, 1)
}

function setStatus(field, statusSelected) {
	field.status = statusSelected
}

async function getUser() {
	const response = await getUserGroup()

	if (response && response.data && response.data.usuarios) {
		groupUsers.value = response.data.usuarios.map((user) => ({
			label: user.nome_completo.trim(),
			id: user.id,
		}))

		levels.value.forEach((level) => {
			level.filteredOptionsUsers = [...groupUsers.value]
		})
	}
}

async function loadHomonyms() {
	try {
		const idProfile = props.idProfile

		const homonymsResponse = await getSourceWithHomonyms(idProfile)
		homonymIds.value = homonymsResponse.data

		props.sourcesProfile.some((source) => homonymIds.value.includes(source.id))
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
	}
}

function handleSearch(searchTerm, index) {
	if (Array.isArray(levels.value[index].filteredOptionsUsers)) {
		levels.value[index].filteredOptionsUsers = groupUsers.value.filter((user) =>
			user.label.toLowerCase().includes(searchTerm.toLowerCase())
		)
	} else {
		levels.value[index].filteredOptionsUsers = []
	}
}

function removeUser(id, level) {
	if (levels.value && levels.value.length >= level) {
		const levelIndex = levels.value[level - 1]
		const userIndex = levelIndex.selectedOption.findIndex((user) => {
			return String(user.id) === String(id)
		})

		if (userIndex !== -1) {
			const updatedOptions = [...levelIndex.selectedOption]
			updatedOptions.splice(userIndex, 1)

			levelIndex.selectedOption = reactive(updatedOptions)
		}
	}
}

async function getWorkflowProfile() {
	const data = await getWorkflow(props.idProfile, props.idGroup)

	const {
		selectedOptionLevels,
		additionalFields: mappedAdditionalFields,
		automaticApprovals,
	} = mapGetWorkflow(data.data, props.sourcesProfile)

	levels.value.forEach((level, index) => {
		const selectedOptions = selectedOptionLevels[`level${index + 1}`] || []

		level.selectedOption = reactive([...selectedOptions])
	})

	additionalFields.value = mappedAdditionalFields.map((field) => {
		field.isMatchHomonym = field.filterDossier === 'com-homonimo'
		return field
	})
	automaticApproval.value = automaticApprovals
}

async function createWorkflowProfile() {
	const niveisWorkflow = [
		{ level: '1', users: selectedOptionLevel1.value },
		{ level: '2', users: selectedOptionLevel2.value },
		{ level: '3', users: selectedOptionLevel3.value },
		{ level: '4', users: selectedOptionLevel4.value },
	]
		.filter((nivel) => nivel.users.length > 0)
		.map((nivel) => ({
			level: nivel.level,
			users: nivel.users.map((user) => ({ id: user.id, name: user.label })),
		}))

	const parametrosAprovacao = additionalFields.value.map((field) => {
		let tipo = null
		if (field.automaticApprovalStates.noWords) {
			tipo = 'sem-palavra'
		} else if (field.automaticApprovalStates.words) {
			tipo = 'palavra'
		} else if (field.automaticApprovalStates.noResult) {
			tipo = 'nao-consta'
		} else if (field.automaticApprovalStates.hasResult) {
			tipo = 'consta'
		}

		let filtersHomonymsDossier = null
		if (homonymIds.value.includes(field.sourceSelected?.id)) {
			filtersHomonymsDossier = field.isMatchHomonym
				? 'com-homonimo'
				: 'sem-homonimo'
		}

		return {
			id: field.sourceSelected?.id,
			nome: field.sourceSelected?.label,
			tipo: tipo,
			filtro_dossie: filtersHomonymsDossier,
			palavras: field.keywords,
			status: field.status,
		}
	})

	const payload = {
		aprovacao_automatica: automaticApproval.value,
		parametros_aprovacao: parametrosAprovacao,
		niveis_workflow: niveisWorkflow,
		qt_niveis: niveisWorkflow.length,
		id_perfil: props.idProfile,
		id_grupo_consulta: props.idGroup,
	}

	try {
		isButtonLoading.value = true
		await createWorkflow(payload.id_perfil, payload)
		emit('save-workflow')
	} catch (error) {
		if (error.response) {
			toast.addToast({
				type: 'error',
				content: error.response.data.message,
			})
		}
		isButtonLoading.value = false
	}
}

onMounted(async () => {
	try {
		await getUser()
		loadHomonyms()
		if (props.isEdit) {
			await getWorkflowProfile()
		}
	} catch (error) {
		console.error(error)
	} finally {
		isLoading.value = false
	}
})
</script>

<style scoped>
.workflow-description {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
	margin: 16px 0px;
}

.user-select {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-01, #b8b8b8);
	background: var(--Light-neutral-light-pure, #fff);
}

.user-level {
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;
	margin: 8px 0px 12px 0px;
}

.user-list {
	display: flex;
	justify-content: left;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 8px;
	margin: 8px 0px 16px 0px;
	align-items: center;
}

.user {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-02, #ccc);
	background: var(--Light-neutral-light-pure, #fff);
	padding: 4px 8px;
	color: var(--Dark-neutral-dark-03, #666);
	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;

	display: flex;
	align-items: end;
}

.main-container {
	display: flex;
	flex-direction: column;
	min-height: 89vh;
}

.content-container {
	flex: 1;
	padding: 16px;
}

footer {
	display: flex;
	justify-content: flex-end;
	gap: 8px;
	padding: 16px;
	background-color: #fff;
}

.description-keyword {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

.keywords-container {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: center;
	margin-top: 8px;
	gap: 8px;
}

.keyword {
	border-radius: 4px;
	border: 1px solid var(--Light-neutral-light-02, #ccc);
	background: var(--Light-neutral-light-pure, #fff);

	padding: 4px 16px;

	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;

	display: flex;
	align-items: end;
}

.failed {
	border-radius: 4px;
	border: 1px solid var(--Feedback-feedback-error, #c33);

	color: var(--Feedback-feedback-error, #c33);
	text-align: center;

	font-size: 14px;
	font-style: normal;
	font-weight: 600;
	line-height: 20px; /* 142.857% */

	padding: 5px 16px;
}

.under-analysis {
	border-radius: 4px;
	border: 1px solid var(--Feedback-feedback-info, #505aff);

	color: var(--Feedback-feedback-info, #505aff);
	text-align: center;
	padding: 5px 16px;
	font-size: 14px;
	font-style: normal;
	font-weight: 600;
	line-height: 20px; /* 142.857% */
}

.description-status {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	line-height: 20px;

	margin: 24px 0px 8px 0px;
}

button {
	transition: background-color 0.3s ease, color 0.3s ease;
}

.active-failed {
	border-radius: 4px;
	border: 1px solid var(--Feedback-feedback-error, #c33);
	background: var(--Feedback-feedback-error, #c33);
	color: var(--Light-neutral-light-pure, #fff);
}

.active-analysis {
	border-radius: 4px;
	border: 1px solid var(--Feedback-feedback-info, #505aff);
	background: var(--Feedback-feedback-info, #505aff);
	color: var(--Light-neutral-light-pure, #fff);
}

.automatic-options {
	background-color: #f0f0f0;
	padding: 16px;
	border-radius: 4px;
	margin-top: 16px;
}

.label {
	color: var(--Dark-neutral-dark-03, #666);

	font-size: 10px;
	font-style: normal;
	font-weight: 400;
	line-height: 14px;
}

:deep(.up-checkbox__text) {
	color: #666666 !important;
}

.checked-checkbox :deep(.up-checkbox__text) {
	color: #7922b9 !important;
}
</style>
