import { mapProfiles } from '@/modules/profile/list/mappers/profiles'

import { createHttpClientInstance } from '@/external/httpClient/httpClient'

const dossieApi = createHttpClientInstance(
	import.meta.env.VITE_APP_DOSSIE_API_URL
)
const toQueryString = (filters) => {
	const urlParams = new URLSearchParams()

	Object.entries(filters).forEach(([key, value]) => {
		if (Array.isArray(value)) {
			value.forEach((unit) => {
				urlParams.append(`${key}[]`, unit)
			})
		} else {
			if (typeof value === 'boolean') {
				urlParams.append(key, value)
			} else if (value) {
				urlParams.append(key, value)
			}
		}
	})

	return urlParams.toString()
}

export const getProfiles = ({ pagination, perPage, filters, type, all = false }) => {
	const apiModel = {
		nome: filters?.profileName,
		id_fonte: filters?.source?.id,
		id_grupo: [filters?.group?.id],
		page: pagination.page,
		per_page: perPage,
		ativo: type === 'default' ? true : filters.active,
		all,
	}

	const profileType = type === 'default' ? 'perfil-padrao' : 'perfil'

	const queryString = toQueryString(apiModel)

	return dossieApi
		.get(
			`/api/dossie/configuration/${profileType}?${queryString}&orderBy[column]=id&orderBy[direction]=desc`
		)
		.then((response) => response.data)
		.then((response) => mapProfiles(response.data))
}

export const getProfile = ({ id, isActive, type }) => {
	const apiModel = {
		basic: false,
		inativo: type === 'default' ? false : !isActive,
	}

	const profileType = type === 'default' ? 'perfil-padrao' : 'perfil'

	const queryString = toQueryString(apiModel)

	return dossieApi
		.get(`/api/dossie/configuration/${profileType}/get/${id}?${queryString}`)
		.then((response) => response.data)
}

export const deactivateProfile = ({ id }) => {
	return dossieApi
		.post('/api/dossie/configuration/perfil/inactivate', {
			id_perfil: id,
		})
		.then((response) => response.data)
}

export const duplicateProfileInServer = ({ baseProfileId, name, groups }) => {
	return dossieApi
		.post('/api/dossie/configuration/perfil/duplicate', {
			id_perfil: baseProfileId,
			grupos: groups.map((group) => group.id),
			nome: name,
		})
		.then((response) => response.data)
}

export const exportProfileInServer = ({ id }) => {
	return dossieApi
		.get(`/api/dossie/configuration/perfil/export/excel/${id}`, {
			responseType: 'blob',
		})
		.then((response) => response.data)
}

export const deleteProfileInServer = ({ id }) => {
	return dossieApi
		.delete(`/api/dossie/configuration/perfil/${id}`)
		.then((response) => response.data)
}

export const getProfileAttachedMonitoring = ({ profileId }) => {
	return dossieApi
		.get(`/api/dossie/configuration/perfil/list/batch-monitoring/${profileId}`)
		.then((response) => response.data)
}

export const getUserGroup = () => {
	return dossieApi.get('api/user/group').then((response) => response.data)
}

export const getSourcesProfile = (profileId) => {
	return dossieApi
		.get(`api/dossie/configuration/perfil/sources/${profileId}`)
		.then((response) => response.data)
}

export const createWorkflow = (profileId, payload) => {
	return dossieApi
		.post(`api/dossie/configuration/perfil/workflow/${profileId}`, payload)
		.then((response) => response.data)
}

export const getWorkflow = (profileId, groupId) => {
	return dossieApi
		.get(
			`/api/dossie/configuration/perfil/workflow/${profileId}?id_grupo_consulta=${groupId}`
		)
		.then((response) => response.data)
}

export const getSourceWithHomonyms = (profileId) => {
	return dossieApi
		.get(`api/dossie/configuration/perfil/sources/homonyms/${profileId}`)
		.then((response) => response.data)
}
