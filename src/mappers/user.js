export const mapUser = (user) => {
	return {
		alias: user.user<PERSON>lia<PERSON>,
		img: user.userImgOrIcon,
		name: user.userName,
		id: user?.id,
		id_plano: user.id_plano,
		group_id: user.group_id,
		admin: user.admin,
		isMaster: user.master,
		isAdmin: user.admin,
		permissions: Object.keys(user.masterPermissions || {}),
		simulate: user.simulate,
		hubspot_visitor_key: user.hubspot_visitor_key,
		email: user.email
	}
}
