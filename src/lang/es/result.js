/* eslint-disable no-dupe-keys */
export const layoutResult = {
	common: {
		attention: 'Atención',
		cancel: 'Cancelar',
		back: 'Volver',
		continue: 'Continuar',
		import: 'Importar',
		save: 'Guardar',
		exportation: 'Exportación',
		asInPt: 'como en Pt',
		close: '<PERSON><PERSON><PERSON>',
		name: 'Nombre',
		criteria: '<PERSON>rite<PERSON>',
		searchProfile: '<PERSON><PERSON><PERSON> de <PERSON>ta',
		homonyms: 'Homónimos',
		actions: 'Acciones',
		created_at: 'Creado En',
		high: 'Alto',
		low: 'Bajo',
		medium: 'Medio',
		informations: 'Informaciones',
		analysisAndComment: 'An<PERSON><PERSON><PERSON> y Comentario',
		reason: 'Motivo',
		requiredField:
			'* Campo obligatorio, ingrese al menos 5 caracteres para registrar el motivo',
		characters: '/ 200 caracteres',
		save: 'Guardar',
		cancel: 'Cancelar',
		titleAnalysis: 'Análisis de Workflow',
		level: 'Nivel',
		oldVersionDossier: 'Versión antigua del Dossier ',
	},
	dossierHistory: {
		batch: 'Lote',
		state: 'Estado',
		criteria: 'Criterio',
		creationDate: '<PERSON><PERSON> Creación',
		title: 'Historial de Dossier',
		dossierCreation: 'Dossier creado',
		dossierProcessing: 'Dossier en procesamiento',
		processed: 'Procesado',
		withExceptions: 'C/ excepciones',
		inLine: 'Con excepciones',
		unknownStatus: 'Estado desconocido',
	},

	exportationsHistory: {
		title: 'Exportaciones de Dossier',
		dossierId: 'ID Dossier',
		tableDescription: 'Exportaciones del usuario de los últimos 7 días',

		statuses: {
			queue: 'En cola',
			processing: 'En procesamiento',
			error: 'Error',
			success: 'Completado',
		},
		tooltips: {
			queue: 'Esperando',
			processing: 'En procesamiento',
			error:
				'Error en el procesamiento del PDF: Lamentamos lo ocurrido, nuestro equipo de especialistas ya ha sido notificado sobre este error y estamos trabajando para resolverlo lo antes posible.',
			success: 'Completado',
		},
	},
	viewNavigation: {
		title: 'Resultados',
		buttons: {
			back: 'Volver',
			nextDossie: 'Próximo Dossier',
			previousDossie: 'Dossier Anterior',
			queryInOtherApp: 'Consultar en otras apps',
		},
	},
	viewSettings: {
		tabs: {
			resume: 'Resumen',
			detailedResult: 'Resultado Detallado',
		},
		buttons: {
			exportResume: 'Exportar Resumen',
			exportDetailed: 'Exportar Detallado',
		},
		exportOptions: {
			allContent: 'Todo Contenido',
			chartAndMap: 'Gráficos y Mapas',
			comments: 'Comentarios',
			mostRecentComments: 'Más Recientes',
			mostRelevantComments: 'Relevantes',
			commentsFromWorkflow: 'del Workflow',
			commentsFromSources: 'de las Fuentes',
			anexos: 'Anexos (certificados, etc.)',
			resume: 'Resúmenes FOXY IA',
		},

		exportModal: {
			configurationText:
				'Puede configurar los elementos a continuación para que se muestren en los Dossiers exportados como PDF',
			fileHint: `Se aceptan archivos en formatos <strong>.jpg, .jpeg o .png</strong> con un tamaño máximo de <strong>200kb</strong>. Las dimensiones recomendadas son de 92x92.`,
			addYourLogo: 'Agregue su Logo en el PDF',
			addPdfTitle: 'Título del PDF',
			input: {
				placeholder: 'Ingrese un título',
				maximumCharacters: '20 caracteres',
			},
			logo: {
				errors: {
					invalidSize: 'El tamaño del archivo es mayor al esperado',
					invalidExtension: 'Extensión de archivo inválida',
				},
			},
			exporting: 'Exportando PDF...',
		},
	},
	filters: {
		contentVisualization: {
			title: 'Visualización de Contenido',
			subtitle: {
				chartsAndMaps: 'Gráficos y Mapas',
				categories: 'Categorías',
				others: 'Otros',
			},
			options: {
				displayAll: 'Mostrar todo',
				map: 'Mapa',
				charts: 'Gráficos',
				legends: 'Leyendas',
				unhideAll: 'Mostrar todos ocultos',
			},
			actions: {
				save: 'Guardar',
				clear: 'Limpiar',
			},
		},
	},
	cardResume: {
		labels: {
			lot: 'Lote',
			profile: 'Perfil',
			criteria: 'Criterio',
			responsible: 'Responsable',
			dossier: 'Dossier',
			created: 'Creado',
			updateDate: 'Última Actualización',
			homonyms: 'Homónimos',
		},

		derivations: {
			isDerived:
				'Este dossier fue generado como una derivación del Dossier {parentDossier}',
			dossiersGenerated:
				'Este dossier tiene derivaciones y generó {amountOfDerivations} dossiers derivados.',
			modal: {
				title: 'Derivaciones',
				informationFirstPart: 'El dossier {dossier}, lote {lot} del criterio',
				informationSecondPart: 'tiene Derivaciones y generó',
				informationThirdPart: '{amountOfDerivations} dossiers derivados.',
				seeBelow:
					'Puede verificar y acceder a los dossiers derivados a través de la lista a continuación.',
			},
		},
	},
	cardResult: {
		labels: {
			withResult: 'Con Resultado',
			withoutResult: 'Sin Resultado',
			unavailable: 'No Disponibles',
			dontFindAnyResult: 'Ninguna fuente no disponible',
		},
	},
	sections: {
		information: {
			title: 'Información General',
		},
	},
	analysis: {
		emptyAnalysis: {
			title: '¿Desea agregar un parecer al dossier?',
			description: `Después de analizar el dossier puede agregar un estado entre Aprobado o Reprobado e ingresar el motivo de su decisión. El estado estará fijado al principio del dossier y podrá ser editado si es necesario.`,
			action: 'Agregar análisis',
		},
		newAnalysis: {
			title: 'Análisis del dossier',
			description: 'Analice el dossier y dé un breve parecer sobre él.',
			note: 'Está editando',
			label: 'Seleccione un estado y el motivo de su parecer',
			action: 'Guardar',
			status: {
				approved: 'Aprobado',
				reproved: 'Reprobado',
			},
		},
		historyAnalysis: {
			title: 'Historial de ediciones',
			description:
				'A continuación puede verificar las ediciones del estado de análisis de este dossier.',
		},
		updateAnalysis: {
			title: 'Análisis del dossier',
			description: 'Analice el dossier y dé un breve parecer sobre él.',
			note: 'Está editando',
			label: 'Seleccione un estado y el motivo de su parecer',
			action: 'Guardar',
			lastUpdate: 'Último Estado definido por: {name} en {date}',
			status: {
				approved: 'Aprobado',
				reproved: 'Reprobado',
			},
		},
		viewAnalysis: {
			title: 'Análisis del dossier',
			description: 'Analice el dossier y dé un breve parecer sobre él.',
			note: 'Está editando',
			label: 'Seleccione un estado y el motivo de su parecer',
			action: 'Guardar',
			lastUpdate: 'El estado fue editado por: {name} en {date}',
			showHistory: 'Ver historial',
			status: {
				approved: 'Aprobado',
				reproved: 'Reprobado',
			},
		},
		writeAnalysis: 'Escriba su análisis sobre el Dossier',
		typeHere: 'Escriba aquí...',
		lastStatusDefinedBy: 'Últimos estado definido por: {name} {date}',
		approve: 'Aprobar',
		disapprove: 'Reprobar',
		editAnalysis: 'Editar análisis',
		writeAnalysis: 'Escriba el Análisis',
		typeHere: 'Escriba aquí',
		lastStatusDefinedBy: 'Último estado definido por',
		approve: 'Aprobar',
		disapprove: 'Reprobar',
		underReview: 'En análisis',
		analysisOfDossier: 'Análisis conclusivo del dossier ',
	},
	upFlag: {
		reason: 'Motivo',
		reasonNotFound: 'Motivo no encontrado',
	},
	workflow: {
		status: {
			approved: 'Aprobado',
			reproved: 'Reprobado',
			wait: 'En análisis',
		},
		types: {
			auto: 'Por análisis automático',
			human: 'Por análisis humana',
			auto: {
				headers: {
					fonts: 'Fuentes',
					rules: 'Reglas',
					occurency: 'Ocurrencias',
					date: 'Fecha',
					status: 'Estado',
				},
			},
		},
		newComment: 'Agregar',
		comment: {
			label: 'Comentario',
			notes: '* Ingrese al menos 5 caracteres para registrar el motivo.',
			placeholder: 'escriba el comentario...',
		},
		messages: {
			success: 'Análisis del workflow guardado con éxito',
			error: 'Error al guardar el análisis del workflow',
		},
		approver: 'Aprobador',
		registeredReason: 'Motivo registrado',
		evaluation: 'Evaluación',
		evaluationDate: 'Fecha de la evaluación',
		exportPDF: 'Exportar en PDF',
		approvedAuto: 'Aprobación automática',
		rejectedAuto: 'Reprobación automática',
		approved: 'Aprobado',
		rejected: 'Reprobado',
		approvalLevels: 'Niveles de aprobación',
		writeReason: 'Escriba un motivo y evalúe este Dossier!',
		reject: 'Reprobar',
		approve: 'Aprobar',
		underAnalysis: 'En análisis',
		pendingEvaluation: 'El Dossier está con la evaluación pendiente',
	},
	tags: {
		title: 'Etiquetas',
		description: 'Etiquetas más utilizadas en este dossier',
		emptyState: {
			title: 'Cree y use etiquetas para agilizar su análisis.',
			subtitle: 'Este dossier aún no tiene ninguna etiqueta.',
			description:
				'Puede crear sus etiquetas en los resultados de las categorías en la pestaña Resumen, o en fuentes específicas en la pestaña de resultado detallado, a través del icono #.',
		},
	},
	upScore: {
		title: 'upScore',
		description:
			'La información generada en este score se da de manera informativa basada en los resultados configurados en el registro del score y en la fecha de consulta del día',

		risk: 'Riesgo',

		scoreNotDefined: {
			message: 'Riesgo no definido',
			reason:
				'No obtuvimos respuesta de las fuentes consultadas y por eso no pudimos clasificar un riesgo para este dossier!',
		},

		invalidScore: 'Error en el procesamiento de las fuentes',
		tabs: {
			all: 'General',
		},
		columns: {
			param: 'Parámetro',
			results: 'Resultados',
			risk: 'Riesgo',
			origin: 'Origen',
		},

		unprocessedSourcesWarning:
			'Dado las fuentes que fueron procesadas, tenemos la nota del score abajo.',
		unprocessedSourcesAmount:
			'Una fuente no procesada | {count} fuentes no procesadas',

		seeDetail: 'Ver detalles',
		resultsFinding:
			'Se encontraron {x} parámetros de riesgo {risk} para este dossier',
		attention: 'Atención',
		emptyError:
			'No obtuvimos resultados de las fuentes consultadas, por lo tanto, no pudimos clasificar un riesgo para este Dossier',
		processError:
			'Algunas fuentes no pudieron ser procesadas correctamente, resultando en una visualización parcial. Para obtener el resultado completo, recomendamos el reprocesamiento del dossier.',
		description:
			'La información de este score se basa en los resultados del registro y en la fecha de consulta.',
		attention: 'Atención',
		alto: 'Alto',
		medio: 'Medio',
		baixo: 'Bajo',
		geral: 'General',
		download: 'Descargar',
		partialView:
			'Algunas fuentes no pudieron ser procesadas correctamente, resultando en una visualización parcial. Para obtener el resultado completo, recomendamos el reprocesamiento del dossier.',
		columns: {
			param: 'Parámetro',
			results: 'Resultados',
			risk: 'Riesgo',
		},
		error: 'Error',
		noRisk: 'Sin riesgo',
		foundParams:
			'Se encontraron {amount} parámetro(s) de riesgo en este dossier.',
		view: 'Ver',
		no_configuration:
			'Analizamos la configuración de la puntuación y <br /> no encontramos ningún resultado con el parámetro configurado',
	},
	summaryCompany: {
		header: {
			title: 'Descriptivo de la empresa',
			callToAction:
				'Información generada por FOXY: La inteligencia artificial de upLexis',
		},
		edit: {
			save: 'Guardar',
			cancel: 'Cancelar',
		},
		delete: 'Eliminar',
		modalDelete: {
			title: 'Atención!',
			description:
				'Está a punto de eliminar el resumen generado por FOXY IA. Después de eliminarlo, para activarlo nuevamente, tendrá que generar un nuevo dossier.',
			note: '¿Está seguro de que desea continuar?',
			save: 'Guardar',
			cancel: 'Cancelar',
		},
		actions: {
			byChatGPT:
				'La información aquí proporcionada fue generada por FOXY IA y se basa en datos de internet. Puede ser necesaria una revisión del contenido.',
			byUser: 'Información editada en {date} por {user}',
		},
		toastSuccessfullyRemoved: 'Descriptivo eliminado con éxito!',
		toastErrorRemoved: 'Error al eliminar descriptivo',
		toastUpdatedSuccessfully: 'Descriptivo actualizado',
		toastUpdatedError: 'Error al actualizar descriptivo',
	},
	comments: {
		sourceWithComments: 'Fuente con comentarios',
		sourceWithoutComments: 'Fuente sin comentarios',
		openSourceComments: 'Abrir comentarios de la fuente',
		closeSourceComments: 'Cerrar comentarios de la fuente',
		title: 'Comentarios',
		mentions: {
			emptyState: 'Sin menciones hasta el momento.',
		},
		emptyState: 'Sin comentarios hasta el momento.',
		newComment: 'Agregar',
		cancelComment: 'Cancelar',
		answerComment: 'Responder',
		editComment: 'Editar',
		placeholder: 'escriba el comentario...',
		tooltip: 'Marcar como relevante',
		noResult: 'Sin resultados',
		editDate: 'Editado en {date}',
		return: 'Volver',
		original: 'Original',
		markAsRelevant: 'Marcar como relevante',
		unmarkAsRelevant: 'Desmarcar como relevante',
		markAllRelevant: 'Marcar todos como relevante',
		unmarkAllRelevant: 'Desmarcar todos como relevante',
		showNextComment: 'Mostrar siguiente comentario',
		showPreviousComment: 'Mostrar comentario anterior',
		closeCommentListing: 'Cerrar lista de comentarios',
		deleteComment: 'Eliminar comentario',
		showAll: 'Mostrar Todos',
		replyingTo: 'Respondiendo a',
		edditedIn: 'Editado en',

		title: 'Comentarios finales',
		writeComment: 'Escriba un comentario',
		typeHere: 'Escriba aquí...',
		send: 'Enviar',
		commentCreated: 'Comentario creado con éxito!',
		commentEdited: 'Comentario editado con éxito!',
		fetchError: 'Ocurrió un error al buscar los datos.',
		creationError: 'Fallo al crear el comentario.',
		editError: 'Fallo al editar el comentario.',

		madeComments: 'Comentarios hechos',
		name: 'Nombre',
		commentDate: 'Fecha de comentario',
		edit: 'Editar',

		modals: {
			deleteModal: {
				warning:
					'Está a punto de <strong>eliminar</strong> su comentario, si tiene alguna interacción, también serán eliminadas.',
				confirmation: '¿Está seguro de que desea continuar?',
			},
		},
		history: {
			title: 'Historial de Edición',
		},
		settings: {
			title: 'Configuraciones',
			notification_by_upminer: 'Recibir notificaciones vía upminer',
			notifications_by_email: 'Recibir notificaciones por correo electrónico',
			only_special: 'Mostrar solo relevantes',
		},
	},
	error: {
		internalError: 'Error interno',
	},
	information: {
		logo: 'Logo de Uplexis',
		tag: 'Etiqueta',
		criterion: 'Criterio',
		name: 'Nombre',
		dossierNumber: 'Número del Dossier',
		consultationProfile: 'Perfil de Consulta',
		responsible: 'Responsable',
		creationDate: 'Fecha de Creación',
		lastUpdate: 'Último procesamiento',
		homonyms: 'Homónimos',
		sourcesWithResults: 'Fuentes con Resultados',
		sourcesWithoutResults: 'Fuentes sin Resultados',
		attention: 'Atención',
		download: 'Descargar',
		flags: 'Banderas',
		amount: 'Cantidad',
		workflowApproved: 'Workflow Aprobado',
		workflowRejected: 'Workflow Rechazado',
		workflowUnderReview: 'Workflow en Análisis',
		riskLow: 'Riesgo Bajo',
		riskMedium: 'Riesgo Medio',
		riskHigh: 'Riesgo Alto',
		scoreProcessingError: 'Error en el Procesamiento de Puntuación',
		undefinedRisk: 'Riesgo No Definido',
		sourcesWithResultsTitle: 'Fuentes con Resultados',
		sourcesWithoutResultsTitle: 'Fuentes sin Resultados',
		user: 'Usuario',
		workflowDisabled: 'Workflow no habilitado',
		workflowApproved: 'Workflow Aprobado',
		workflowDisapproved: 'Workflow Reprobado',
		workflowInAnalysis: 'Workflow en Análisis',
		upFlagDisabled: 'upFlag no habilitado',
		upFlag: 'upFlag',
		scoreDisabled: 'Score no habilitado',
		processingError: 'Error de procesamiento',
		mediumRisk: 'Riesgo Medio',
		lowRisk: 'Riesgo Bajo',
		highRisk: 'Riesgo Alto',
		undefinedRisk: 'Riesgo no definido',
		insufficientData: 'Datos insuficientes',
	},
	detailedResult: 'Detallado',
	summaryResult: 'Analítico',
	export: 'Exportar',
	start: 'Inicio',
	derivations: 'Derivaciones',
	dossierHistory: 'Historial de Dossiers',
	AnalysisComments: 'Análisis y comentarios',
	description: 'Siga el historial de dossiers creados con este mismo criterio',
	dossierId: 'ID del Dossier',
	creationDate: 'Fecha de creación',
	cpfCnpj: 'CPF/CNPJ',
	name: 'Nombre',
	consultationProfile: 'Perfil de consulta',
	creationDate: 'Fecha de creación',

	analytics: {
		filtersTitle: 'Filtros',
		buttonFilter: 'Filtrar',
		courtsTitle: 'Tribunales',
		superiorCouncils: 'Tribunales Superiores y Consejos',
		regionalFederalCourts: 'Tribunales Regionales Federales',
		courtsOfJustice: 'Tribunales de Justicia',
		regionalElectoralCourts: 'Tribunales Regionales Electorales',
		regionalLaborCourts: 'Tribunales Regionales del Trabajo',
		militaryCourtsOfJustice: 'Tribunales de Justicia Militar',
		federalCourtsOfJustice: 'Tribunales de Justicia Federal',
		caseStatusTitle: 'Estado de procesos',
		inProgress: 'En tramitación',
		underAppeal: 'En grado de recurso',
		suspended: 'Suspendido',
		finalArchive: 'Archivo definitivo',
		provisionalArchive: 'Archivo provisional',
		administrativelyArchived: 'Archivado administrativamente',
		archive: 'Archivo',
		judged: 'Juzgado',
		extinct: 'Extinguido',
		author: 'Autor',
		defendant: 'Demandado',
		involved: 'Involucrado',
		participationInProcesses: 'Participación en los procesos',
	},
	headerTitle: 'Dossier',
	foxy_insights: {
		title: 'Foxy AI Insights',
		company_name: 'Nombre de la Empresa',
		opening_date: 'Fecha de Apertura',
		domains: 'Dominios',
		resume_activity: 'Resumen de Actividad',
		resume: 'Resumen',
		see_more: 'Ver Más',
		social_medias: 'Redes Sociales',
		marks_and_products: 'Marcas y Productos',
		technologies_business_processes: 'Tecnologías y Procesos de Negocio',
		competitivity_and_positioning: 'Competitividad y Posicionamiento',
		trends_and_insights: 'Tendencias e Insights de la Industria',
		address: 'Dirección',
		contacts: 'Contactos',
		close: 'Cerrar',
		registered_domains: 'Dominios Registrados',
		address_not_available: 'Dirección no disponible',
		contacts_not_find: 'Contactos no encontrados',
		status: 'Estado',
		cnpj: 'CNPJ',
		fantasy_name: 'Nombre de Fantasía',
		domains_registered: 'Dominios Registrados',
		view_more: 'Ver Más',
		business_size: 'Tamaño de la Empresa',
		legal_nature: 'Naturaleza Jurídica',
		initial_capital: 'Capital Inicial',
		main_activity: 'Actividad Principal',
		secondary_activities: 'Actividades Secundarias',
		board_of_directors: 'Junta Directiva',
		shareholders: 'Accionistas',
	},
	totalResults: 'noticias',
	totalResult: 'noticia',
	amountWords: 'palabras',
	amountCountry: 'país',
	amountWord: 'Palabra',
	quantityCountries: 'países',
	data_news: 'Noticias del mes',
	media_vehicles: 'Medios de comunicación',
	words_key: 'Palabras citadas',
	highlights: 'Destacados',
	quantidadePalavrasCitadas: 'Cantidad de palabras citadas',
	verMais: 'ver más',
	noticiasPorMes: 'Noticias por mes',
	selectYear: 'Seleccione el año',
	months: {
		Janeiro: 'Enero',
		Fevereiro: 'Febrero',
		Março: 'Marzo',
		Abril: 'Abril',
		Maio: 'Mayo',
		Junho: 'Junio',
		Julho: 'Julio',
		Agosto: 'Agosto',
		Setembro: 'Septiembre',
		Outubro: 'Octubre',
		Novembro: 'Noviembre',
		Dezembro: 'Diciembre',
	},
	noResult: {
		line1: 'No obtuvimos ningún resultado',
		line2: 'sobre este asunto',
	},
	mediaVehiclesTitle: 'Medios de comunicación',
	percentage: 'Porcentaje',
	mediaVehicles: 'Medios de comunicación',
	mediaVehiclesName: 'Nombre del medio',
	citedWords: 'Palabras',
	name: 'Nombre',
	news_without_image: 'Noticia sin imagen',
	date: 'Consulta realizada en {date}',
	criteria: 'con el criterio {criteria}',
	classeDeProcessos: 'Clase de procesos',
	verMais: 'Ver más',
	classeDeProcessosTitle: 'Clase de procesos',
	others: 'Otros',
	principaisAssuntos: 'Principales asuntos',
	varaDosProcessos: 'Vara de los Procesos',
	processosTribunais: 'Cantidad de procesos por tribunales',
	download: 'Descargar',
	details: 'Detalle de procesos',
	sourceResults: 'fuentes con resultados',
	sourceResult: 'fuente con resultado',
	sourceAnalyticResult: 'resultado',
	sourceAnalyticResults: 'resultados',
	StatusProcessosTitle: 'Estado de procesos',
	processingError: 'Error de procesamiento',
	insufficientData: 'Datos insuficientes para calcular el score',
	convertDossier: {
		title: 'Ocurrió un error',
		message: 'Inténtelo de nuevo más tarde',
		message1: 'Descubra las mejoras en el nuevo dossier...',
		message2: 'Explore las nuevas funcionalidades del dossier...',
		message3: '¡Ahora puede agregar comentarios y marcaciones!',
		message4:
			'Estamos convirtiendo su dossier al nuevo formato, mucho más simple y limpio',
		completedMessage:
			"<span style='color: #7922B9;'>¡Migración completada! Disfrute de las mejoras.</span> Será redirigido al nuevo dossier en {count} segundo(s)",
		goBack: 'Volver al historial',
	},
	totalProcesses: 'Total de procesos',
	totalProcessValue: 'Valor total de procesos',
	processesAsAuthor: 'Procesos como autor',
	processesAsDefendant: 'Procesos como demandado',
	processesAsInvolved: 'Procesos como involucrado',
	processDetail: 'Detalle de los procesos',
	processesAsAuthorTitle: 'Procesos como autor',
	processesAsDefendantTitle: 'Procesos como demandado',
	processesAsInvolvedTitle: 'Procesos como involucrado',
	authorDescription: '¡Revise los procesos como autor que filtró!',
	defendantDescription: '¡Revise los procesos como demandado que filtró!',
	involvedDescription: '¡Revise los procesos como involucrado que filtró!',
	processesAsDefendant: 'Procesos como demandado',
	processesAsInvolved: 'Procesos como involucrado',
	uf: 'UF',
	processTitle: 'Título del proceso',
	processNumber: 'Número del proceso',
	processValue: 'Valor del proceso',
	participation: 'Participación',
	downloadAll: 'Descargar todos',
	details: 'Detalles',
	processLike: 'Proceso como',
	notInformed: 'No informado',
	checkProcesses: '¡Revise los procesos que filtró!',
	pdfProcessing:
		'Su PDF está en procesamiento. Revise en su lista de Dossiers.',
	pdfProcessed:
		'Su PDF ya ha sido procesado. Revise en su lista de Dossiers.',
	email: 'Correo electrónico',
	site: 'Sitio web',
	phone: 'Teléfono',
	expandAllSources: 'Expandir todas las fuentes',
	collapseAllSources: 'Contraer todas las fuentes'
}
