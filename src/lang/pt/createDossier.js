export const createDossier = {
	title: 'Criação de dossiês',
	breadcrumb: {
		profileType: 'Tipo de perfil',
		queryProfile: 'Perfil de consulta',
		criteria: 'Critérios',
		tag: 'Tag',
		parameters: 'Parâmetros',
		summary: 'Resumo',
		parametrizations: 'Parâmetros',
	},
	common: {
		physicalPerson: 'Pessoa física',
		legalPerson: 'Pessoa jurídica',
		name: 'Nome',
		cpf: 'CPF',
		corporateReason: 'Razão social',
		cnpj: 'CNPJ',
		profileTypeMessage: 'Escolha o tipo de perfil da sua pesquisa:',
		criteriaModelMessage:
			'Qual modelo de critério você utilizará na sua busca?',
		nextStep: 'Próximo passo',
		toBack: 'Voltar',
	},
	criteria: {
		addTemplate: 'Adicione aqui a sua planilha',
		spreadsheetTemplate: 'Modelo de planilha',
		viewMore: 'Ver mais',
		searchOfCnpj: 'Procure por CNPJ',
		corporateReason: 'Procure por Razão Social',
		insertCriteriaMessage:
			'Insira os critérios que você deseja utilizar em sua busca',
		spreadsheetError: 'Erro na planilha',
		criteria: 'critério(s)',
		spreadsheetErrorDescription:
			'Erro na sua planilha! Recomendamos o uso do nosso modelo de planilha',
		insertCriteriaMessage: 'Insira Critérios',
		addTemplate: 'Adicionar Modelo',
		criteria: 'Critérios',
		searchCPF: 'Procure por CPF',
		searchCNPJ: 'Procure por CNPJ',
		searchName: 'Procure por nome',
		searchCorporateName: 'Procure por razão social',
		errorSpreadsheet:
			'Erro na sua planilha! Recomendamos o uso do nosso modelo de planilha',
	},

	queryProfile: {
		startByChoosingProfile:
			'Comece escolhendo um perfil de consulta. Você pode optar por uma alternativa padronizada ou uma opção personalizada da sua empresa:',
		standardizedQueryProfiles: 'Perfis de consulta padronizados',
		customQueryProfiles: 'Perfis de consulta personalizados',
		searchProfile: 'Pesquise o nome do seu perfil',
		noProfilesFound: 'Nenhum perfil encontrado',
		checkDetails: 'Confira detalhes',
		quickView:
			'Oferece uma configuração pré-definida, ideal para obter uma visão rápida e abrangente dos dados',
		specificSearch:
			'Dispõe de fontes escolhidas pela sua empresa, alternativa apropriada para buscas específicas',
		customQueryProfiles: 'Perfis de Consulta Personalizados',
		specificSearch: 'Pesquisa Específica',
		noProfilesFound: 'Nenhum perfil encontrado',
		customQueryProfiles: 'Perfis de Consulta Personalizados',
		specificSearch: 'Pesquisa Específica',
		noProfilesFound: 'Nenhum perfil encontrado',
		standardizedQueryProfiles: 'Perfis de Consulta Padronizados',
		quickView: 'Visualização Rápida',
		checkDetails: 'Ver Detalhes',
		noProfilesMatch: 'Nenhum dos perfis atende sua necessidade?',
		createNewProfile: 'Clique aqui para criar um novo!',
		searchPlaceholder: 'Pesquise o nome do seu perfil',
	},

	parametrizations: {
		addParameters:
			'Adicione parâmetros para enriquecer sua busca e encontrar resultados mais precisos',
		previousStep: 'Voltar',
		nextStepCreateDossier: 'Criar dossiê',
		createAndProcessAutomatically: 'Criar e processar Dossiês automaticamente',
		consultCriteriaByName: 'Consultar critério por nome exato',
		bancoCentralTitle: 'Banco Central - CRSFN - Ementas e Acórdãos',
		mandatory: 'Obrigatória',
		buscadorGoogleTitle: 'Buscador google',
		autoMarkAsRelevant: 'Marcar automaticamente como relevantes',
		certidaoAcoesExecucoesTile:
			'Certidão de Ações e Execuções Cíveis e Criminais - TRF2',
		certidaoCeatTitle: 'Certidão CEAT TRT15 - Campinas e região',
		certidaoCguTitle: 'Certidões da Controladoria-Geral da União',
		certidaoDistribuicoesAcoesTitle:
			'Certidão de Distribuição Ações Cíveis, Fiscais e Criminais - TRF 3',
		certificateType: 'Tipo de certidão',
		certidaoJudicialTitle: 'Certidão Judicial TRF4',
		comprotTitle: 'Comprot - Comunicação e Protocolo',
		consultaSocioTitle: 'Consulta sócio',
		proximityCriterion: 'Proximidade do critério em %',
		autoMarkAsRelevant: 'Marcar automaticamente como relevantes',
		googleSearcherTitle: 'Buscador Google (Não específica)',
		ministerioTurismoTitle: 'Ministério do Turismo Cadastrur',
		state: 'Estado',
		pessoaGoldBoaVistaTitle: 'Pessoa Gold Boa Vista',
		ppeTitlutarPagaTitle: 'PPE Titular paga',
		buscarPessoasRelacionadas: 'Buscar Pessoas Relacionadas:',
		ppeTitlutarPrivadaTitle: 'PPE Titular privada',
		savedParameter: 'Parâmetros Salvos',
		spcMixMaisTitle: 'SPC MIX MAIS (SPC BRASIL)',
		participationInCompanies: 'Participação nas empresas',
		societalControl: 'Controle societário',
		action: 'Ação',
		addName: 'Adicione um nome para essa parametrização',
		textPlaceholder: 'Digite o nome',
		cancel: 'Cancelar',
		save: 'Salvar',
		titleDiarioOficial: 'Diários oficiais',
		titleDiarioOficialPay: 'Diários Oficiais (Paga)',
		relevanceCheckboxDiarioOficial:
			'Marcar consultas automaticamente como relevantes',
		diarioLabel: 'Diário',
		placeholder: 'Selecione',
		keywordPlaceholderDiarioOficial: 'Palavras-chave (Opcional)',
		cadeTitle: 'Cade - CONSELHO ADMINISTRATIVO DE DEFESA ECONÔMICA',
		relevanceCheckboxCade: 'Marcar consultas automaticamente como relevantes',
		searchInLabel: 'Pesquisar em',
		processesLabel: 'Processos',
		generatedDocumentsLabel: 'Documentos gerados',
		externalDocumentsLabel: 'Documentos externos',
		exactSearchLabel: 'Consulta exata',
		exactNameSearch: 'Consulta critério nome exato',
		BaseJuridicaOfflineEscavador: 'Base Jurídica Offline Escavador',
		BaseJuridicaOfflineEscavadorDoc: 'Base Jurídica Offline - Documento',
		offlineLegalDatabaseNotice:
			'Bases jurídicas offline trazem dados de modo rápido e sem instabilidades. Porém, essas bases possuem foco nos diários oficiais e podem apresentar divergências em relação aos dados disponíveis em tribunais online.',
		uf: 'UF',
		tribunais: 'Tribunais',
		attention: 'Atenção!',
		select: 'Selecione',
		allSelected: 'Todos selecionados',
		fullName: 'Nome completo',
		motherFullName: 'Nome completo da mãe',
		parts: 'Partes',
		justice: 'Justiça',
		djenTitle: 'Djen',
		processosJuridicosTitle: 'Processos Jurídicos',
		fontesJuridicasTitle: 'Processos Judiciais (Nova)',
		mpfRelevanciaTitle: 'MPF (Relevância)'
	},

	tag: {
		tagDescription:
			'Defina uma tag para categorizar seu dossiê e encontrá-lo facilmente quando necessário.',
		selectExistingTag:
			'Selecione uma tag existente para classificar seu dossiê ou crie uma nova.',
		newTag: 'Nova Tag',
		selectColor: 'Selecione a cor',
		save: 'Salvar',
		toBack: 'Voltar',
		nextStep: 'Próxima Etapa',
		skip: 'Pular',
		select: 'Selecione',
		tagCreated: 'Tag criadas',
		tagDescription: 'Descrição da Tag',
		selectExistingTag: 'Selecione uma Tag existente',
		tagCreated: 'Tags Criadas',
		select: 'Selecione',
		doNotAssignTag: 'Não atribuir tag a esse dossiê',
		createAndProcessAutomatically: 'Criar e processar Dossiês automaticamente',
	},
	dialog: {
		attention: 'Atenção',
		creatingDossiers: 'Serão criados {amount} dossiês em {countdown} segundos.',
		cancel: 'Cancelar',
		addName: 'Adicione um nome para essa configuração',
		textPlaceholder: 'Nome',
		save: 'Salvar',
	},

	toastDossierCreatedSuccess: 'Dossiê criado com sucesso!',
	buttonBackToDossiersHistory: 'Voltar para histórico de Dossiês',
	profileTypePf: 'Pessoa Fisica',
	profileTypePJ: 'Pessoa Juridica',
	spreadsheetTemplate: 'Modelo de Planilha',
	autoCreateAndProcessDossiers: 'Criar e processar Dossiês automaticamente',
	duplicityAlert: 'Alerta duplicidade',
	AdditionalAlertTitleSingular: 'Será criado {count} dossiê extra!',
	AdditionalAlertTitlePlural: 'Serão criados {count} dossiês extras!',
	AdditionalAlertSubTitle: 'Deseja criar mesmo assim?',
	Additionalbutton: 'Criar mesmo assim',
	criteriosProcessados:
		'Os seguintes critérios já foram processados nos últimos {days} dias',
	criteriosBaseados:
		'O alerta é baseado apenas no critério, não sendo considerado o perfil de consulta.',
	numeroDossie: 'Nº dossiê',
	criacao: 'Criação',
	grupo: 'Grupo',
	usuario: 'Usuário',
	voltarRevisar: 'Voltar e revisar',
	criarDossie: 'Criar o dossiê mesmo assim',
}
