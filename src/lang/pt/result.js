/* eslint-disable no-dupe-keys */
export const layoutResult = {
	common: {
		attention: 'Atenção',
		cancel: 'Cancelar',
		back: 'Voltar',
		continue: 'Continuar',
		import: 'Importar',
		save: '<PERSON><PERSON>',
		exportation: 'Exportação',
		asInPt: 'ás',
		close: '<PERSON><PERSON><PERSON>',
		name: 'No<PERSON>',
		criteria: 'Crt<PERSON><PERSON><PERSON>',
		searchProfile: '<PERSON><PERSON><PERSON>',
		homonyms: 'Homôni<PERSON>',
		actions: 'A<PERSON>õ<PERSON>',
		created_at: 'Criado Em',
		high: 'Alto',
		low: 'Baixo',
		medium: 'Medio',
		informations: 'Informações',
		analysisAndComment: 'An<PERSON><PERSON><PERSON> e Comentário',
		reason: 'Motivo',
		requiredField:
			'* Campo obrigatório, insira pelo menos 5 caracteres para registrar o motivo',
		characters: '/ 200 caracteres',
		save: 'Salvar',
		cancel: 'Cancelar',
		titleAnalysis: 'Análise de Workflow',
		level: 'Nível',
		oldVersionDossier: 'Versão antiga do Dossiê ',
	},
	dossierHistory: {
		batch: 'Lote',
		state: 'Estado',
		criteria: 'Crit<PERSON><PERSON>',
		creationDate: 'Data de Criação',
		title: 'Histórico de Dossie',
		dossierCreation: 'Dossie criado',
		dossierProcessing: 'Dossie em processamento',
		processed: 'Processado',
		withExceptions: 'C/ exceções',
		inLine: 'Com exceções',
		unknownStatus: 'Status desconhecido',
	},

	exportationsHistory: {
		title: 'Exportações de Dossiê',
		dossierId: 'ID Dossiê',
		tableDescription: 'Exportações do usuário dos últimos 7 dias',

		statuses: {
			queue: 'Na fila',
			processing: 'Em processamento',
			error: 'Erro',
			success: 'Concluído',
		},
		tooltips: {
			queue: 'Aguardando',
			processing: 'Em processamento',
			error:
				'Erro no processamento do PDF: Lamentamos o ocorrido, nosso time de especialistas já foi avisado sobre este erro e estamos trabalhando para resolvê-lo o mais breve possível.',
			success: 'Concluído',
		},
	},
	viewNavigation: {
		title: 'Resultados',
		buttons: {
			back: 'Voltar',
			nextDossie: 'Próximo Dossiê',
			previousDossie: 'Dossiê Anterior',
			queryInOtherApp: 'Consultar em outros apps',
		},
	},
	viewSettings: {
		tabs: {
			resume: 'Resumo',
			detailedResult: 'Resultado Detalhado',
		},
		buttons: {
			exportResume: 'Exportar Resumo',
			exportDetailed: 'Exportar Detalhado',
		},
		exportOptions: {
			allContent: 'Todo Conteúdo',
			chartAndMap: 'Gráficos e Mapas',
			comments: 'Comentários',
			mostRecentComments: 'Mais Recentes',
			mostRelevantComments: 'Relevantes',
			commentsFromWorkflow: 'do Workflow',
			commentsFromSources: 'das Fontes',
			anexos: 'Anexos (certidões, etc.)',
			resume: 'Resumos FOXY IA',
		},

		exportModal: {
			configurationText:
				'Você pode configurar os elementos abaixo para serem exibidos nos de Dossiês exportados como PDF',
			fileHint: `São aceitos arquivos nos formatos
			<strong>.jpg, .jpeg ou .png</strong> com tamanho máximo de
			<strong>200kb</strong>. As dimensões recomendadas são de 92x92.`,
			addYourLogo: 'Adicione seu Logo no PDF',
			addPdfTitle: 'Título do PDF',
			input: {
				placeholder: 'Insira um título',
				maximumCharacters: '20 caracteres',
			},
			logo: {
				errors: {
					invalidSize: 'Tamanho do arquivo é maior que o esperado',
					invalidExtension: 'Extensão de arquivo é inválida',
				},
			},
			exporting: 'Exportando PDF...',
		},
	},
	filters: {
		contentVisualization: {
			title: 'Visualização de Conteúdo',
			subtitle: {
				chartsAndMaps: 'Gráficos e Mapas',
				categories: 'Categorias',
				others: 'Outros',
			},
			options: {
				displayAll: 'Exibir tudo',
				map: 'Mapa',
				charts: 'Gráficos',
				legends: 'Legendas',
				unhideAll: 'Reexibir todos ocultados',
			},
			actions: {
				save: 'Salvar',
				clear: 'Limpar',
			},
		},
	},
	cardResume: {
		labels: {
			lot: 'Lote',
			profile: 'Perfil',
			criteria: 'Critério',
			responsible: 'Responsável',
			dossier: 'Dossiê',
			created: 'Criado',
			updateDate: 'Última Atualização',
			homonyms: 'Homônimos',
		},

		derivations: {
			isDerived:
				'Este dossiê foi gerado como uma derivação do Dossiê {parentDossier}',
			dossiersGenerated:
				'Este dossiê possuí derivações e gerou {amountOfDerivations} dossiês derivados.',
			modal: {
				title: 'Derivações',
				informationFirstPart: 'O dossiê {dossier}, lote {lot} do critério',
				informationSecondPart: 'possuí Derivações e gerou',
				informationThirdPart: '{amountOfDerivations} dossiês derivados.',
				seeBelow:
					'Você pode conferir e acessar os dossiês derivados através da lista abaixo.',
			},
		},
	},
	cardResult: {
		labels: {
			withResult: 'Com Resultado',
			withoutResult: 'Sem Resultado',
			unavailable: 'Indisponíveis',
			dontFindAnyResult: 'Nenhuma fonte indisponível',
		},
	},
	sections: {
		information: {
			title: 'Informações Gerais',
		},
	},
	analysis: {
		emptyAnalysis: {
			title: 'Deseja adicionar um parecer ao dossiê?',
			description: `Após analisar o dossiê você pode adicionar um status para ele entre provado ou Reprovado e inserir o motivo da sua decisão.
			O status ficará	fixado no início do dossiê e poderá ser editado caso necessário.`,
			action: 'Adicionar análise',
		},
		newAnalysis: {
			title: 'Análise do dossiê',
			description: 'Analise o dossiê e de um breve parecer sobre ele.',
			note: 'Você está editando',
			label: 'Selecione um status e o motivo do seu parecer',
			action: 'Salvar',
			status: {
				approved: 'Aprovado',
				reproved: 'Reprovado',
			},
		},
		historyAnalysis: {
			title: 'Histórico de edições',
			description:
				'Abaixo você pode conferir as edições de status de análise deste dossiê.',
		},
		updateAnalysis: {
			title: 'Análise do dossiê',
			description: 'Analise o dossiê e de um breve parecer sobre ele.',
			note: 'Você está editando',
			label: 'Selecione um status e o motivo do seu parecer',
			action: 'Salvar',
			lastUpdate: 'Último Status definido por: {name} em {date}',
			status: {
				approved: 'Aprovado',
				reproved: 'Reprovado',
			},
		},
		viewAnalysis: {
			title: 'Análise do dossiê',
			description: 'Analise o dossiê e de um breve parecer sobre ele.',
			note: 'Você está editando',
			label: 'Selecione um status e o motivo do seu parecer',
			action: 'Salvar',
			lastUpdate: 'O status foi editado por: {name} em {date} ',
			showHistory: 'Ver histórico',
			status: {
				approved: 'Aprovado',
				reproved: 'Reprovado',
			},
		},
		writeAnalysis: 'Escreva a sua análise sobre o Dossiê',
		typeHere: 'Digite aqui...',
		lastStatusDefinedBy: 'Últimos status definido por: {name} {date}',
		approve: 'Aprovar',
		disapprove: 'Reprovar',
		editAnalysis: 'Editar análise',
		writeAnalysis: 'Análise',
		typeHere: 'Escreva sua análise aqui...',
		lastStatusDefinedBy: 'Último status definido por',
		approved: 'Aprovado',
		disapproved: 'Reprovado',
		underReview: 'Em análise',
		analysisOfDossier: 'Análise conclusiva do dossiê ',
	},
	upFlag: {
		reason: 'Motivo',
		reasonNotFound: 'Motivo não encontrado',
	},
	workflow: {
		status: {
			approved: 'Aprovado',
			reproved: 'Reprovado',
			wait: 'Em análise',
		},
		types: {
			auto: 'Por análise automática',
			human: 'Por análise humana',
		},
		auto: {
			headers: {
				fonts: 'Fontes',
				rules: 'Regras',
				occurency: 'Ocorrências',
				date: 'Data',
				status: 'Status',
			},
		},
		newComment: 'Adicionar',
		comment: {
			label: 'Comentário',
			notes: '* Insira pelo menos 5 caracteres para registrar o motivo.',
			placeholder: 'digite o comentário...',
		},
		messages: {
			success: 'Análise do workflow salva com sucesso',
			error: 'Erro ao salvar análise do workflow',
		},
		approver: 'Aprovador',
		registeredReason: 'Motivo cadastrado',
		evaluation: 'Avaliação',
		evaluationDate: 'Data da avaliação',
		exportPDF: 'Exportar no PDF',
		approvedAuto: 'Aprovação automática',
		rejectedAuto: 'Reprovação automática',
		approved: 'Aprovado',
		rejected: 'Reprovado',
		approvalLevels: 'Níveis de aprovação',
		writeReason: 'Escreva um motivo e avalie este Dossiê!',
		reject: 'Reprovar',
		approve: 'Aprovar',
		underAnalysis: 'Em análise',
		pendingEvaluation: 'O Dossiê está com a avaliação pendente',
	},
	tags: {
		title: 'Tags',
		description: 'Tags mais utilizadas nesse dossiê',
		emptyState: {
			title: 'Crie e utilize tags para agilizar sua análise.',
			subtitle: 'Este dossiê ainda não possui nenhuma tag.',
			description: `Você pode criar suas tags nos resultados das categorias na aba de Resumo, ou em fontes específicas na Aba de resultado detalhado, através do ícone #.`,
		},
	},
	upScore: {
		title: 'upScore',
		description:
			'As informações geradas nesse score são dadas de forma informativa com base nos resultados configurados no cadastro do score e na data de consulta do dia',

		risk: 'Risco',

		scoreNotDefined: {
			message: 'Risco não definido',
			reason:
				'Não obtivemos resposta das fontes pesquisadas e por isso não conseguimos classificar um risco para esse dossiê!',
		},

		invalidScore: 'Erro no processamento das fontes',
		tabs: {
			all: 'Geral',
		},
		columns: {
			param: 'Parâmetro',
			results: 'Resultados',
			risk: 'Risco',
			origin: 'Origem',
		},

		unprocessedSourcesWarning:
			'Diante das fontes que foram processadas, temos a nota do score abaixo.',
		unprocessedSourcesAmount:
			'Uma fonte não processada | {count} fontes não processadas',

		seeDetail: 'Ver detalhes',
		resultsFinding:
			'Foram encontrados {x} parâmetros de risco {risk} para este dossiê',
		attention: 'Atenção',
		emptyError:
			'Não obtivemos resultados das fontes pesquisadas, sendo assim, não conseguimos classificar um risco para esse Dossiê',
		processError:
			'Algumas fontes não puderam ser processadas corretamente, resultando em uma visualização parcial. Para obter o resultado completo, recomendamos o reprocessamento do dossiê.',
		description:
			'As informações deste score são baseadas nos resultados do cadastro e na data de consulta.',
		attention: 'Atenção',
		alto: 'Alto',
		medio: 'Médio',
		baixo: 'Baixo',
		geral: 'Geral',
		download: 'Download',
		partialView:
			'Algumas fontes não puderam ser processadas corretamente, resultando em uma visualização parcial. Para obter o resultado completo, recomendamos o reprocessamento do dossiê.',
		columns: {
			param: 'Parâmetro',
			results: 'Resultados',
			risk: 'Risco',
		},
		error: 'Erro',
		noRisk: 'Sem risco',
		error: 'Erro',
		noRisk: 'Sem risco',
		foundParams:
			'Foram encontrados {amount} parâmetro(s) de risco nesse dossiê.',
		view: 'Visualizar',
		no_configuration:
			'Analisamos as configurações do score e <br /> não encontramos nenhum resultado com o parâmetro configurado.',
	},
	summaryCompany: {
		header: {
			title: 'Descritivo da empresa',
			callToAction:
				'Informações geradas pela FOXY: A inteligência artificial da upLexis',
		},
		edit: {
			save: 'Salvar',
			cancel: 'Cancelar',
		},
		delete: 'Remover',
		modalDelete: {
			title: 'Atenção!',
			description:
				'Você está prestes a deletar o resumo gerado pela FOXY IA. Após remover, para ativá-lo novamente, terá que gerar um novo dossiê.',
			note: 'Tem certeza que deseja continuar?',
			save: 'Salvar',
			cancel: 'Cancelar',
		},
		actions: {
			byChatGPT:
				'As informações aqui fornecidas foram geradas pela FOXY IA, e está embasada em dados da internet. Uma revisão do conteúdo pode ser necessária.',
			byUser: 'Informação editada em {date} por {user}',
		},
		toastSuccessfullyRemoved: 'Descritivo removido com sucesso!',
		toastErrorRemoved: 'Erro ao remover descritivo',
		toastUpdatedSuccessfully: 'Descritivo atualizado',
		toastUpdatedError: 'Erro ao atualizar descritivo',
	},
	comments: {
		sourceWithComments: 'Fonte com comentários',
		sourceWithoutComments: 'Fonte sem comentários',
		openSourceComments: 'Abrir comentários da fonte',
		closeSourceComments: 'Fechar comentários da fonte',
		title: 'Comentários',
		mentions: {
			emptyState: 'Sem menções até o momento.',
		},
		emptyState: 'Sem comentários até o momento.',
		newComment: 'Adicionar',
		cancelComment: 'Cancelar',
		answerComment: 'Responder',
		editComment: 'Editar',
		placeholder: 'digite o comentário...',
		tooltip: 'Marcar como relevante',
		noResult: 'Sem resultados',
		editDate: 'Editado em {date}',
		return: 'Voltar',
		original: 'Original',
		markAsRelevant: 'Marcar como relevante',
		unmarkAsRelevant: 'Desmarcar como relevante',
		markAllRelevant: 'Marcar todos como relevante',
		unmarkAllRelevant: 'Desmarcar todos como relevante',
		showNextComment: 'Mostrar próximo comentário',
		showPreviousComment: 'Mostrar comentário anterior',
		closeCommentListing: 'Fechar listagem de comentários',
		deleteComment: 'Deletar comentário',
		showAll: 'Mostrar Todos',
		replyingTo: 'Respondendo',
		edditedIn: 'Editado em',

		title: 'Comentários finais',
		writeComment: 'Escreva um comentário',
		typeHere: 'Digite aqui...',
		send: 'Enviar',
		commentCreated: 'Comentário criado com sucesso!',
		commentEdited: 'Comentário editado com sucesso!',
		fetchError: 'Ocorreu um erro ao buscar os dados.',
		creationError: 'Falha ao criar o comentário.',
		editError: 'Falha ao editar o comentário.',

		madeComments: 'Comentários feitos',
		name: 'Nome',
		commentDate: 'Data do comentário',
		edit: 'Editar',

		modals: {
			deleteModal: {
				warning: `Você está prestes a <strong>deletar</strong> o seu comentário, caso tenha alguma interação, elas também serão.`,
				confirmation: `Tem certeza que deseja continuar?`,
			},
		},
		history: {
			title: 'Histórico de Edição',
		},
		settings: {
			title: 'Configurações',
			notification_by_upminer: 'Receber notificações via upminer',
			notifications_by_email: 'Receber notificações via e-mail',
			only_special: 'Exibir apenas relevantes',
		},
	},
	error: {
		internalError: 'Erro interno',
	},
	information: {
		logo: 'Logo da Uplexis',
		tag: 'Etiqueta',
		criterion: 'Critério',
		name: 'Nome',
		dossierNumber: 'Número do Dossiê',
		consultationProfile: 'Perfil de Consulta',
		responsible: 'Responsável',
		creationDate: 'Data de Criação',
		lastUpdate: 'Último processamento ',
		homonyms: 'Homônimos',
		sourcesWithResults: 'Fontes com Resultados',
		sourcesWithoutResults: 'Fontes sem Resultados',
		attention: 'Atenção',
		download: 'Baixar',
		flags: 'Bandeiras',
		amount: 'Quantidade',
		workflowApproved: 'Fluxo de Trabalho Aprovado',
		workflowRejected: 'Fluxo de Trabalho Rejeitado',
		workflowUnderReview: 'Fluxo de Trabalho em Análise',
		riskLow: 'Risco Baixo',
		riskMedium: 'Risco Médio',
		riskHigh: 'Risco Alto',
		scoreProcessingError: 'Erro de Processamento de Pontuação',
		undefinedRisk: 'Risco Não Definido',
		sourcesWithResultsTitle: 'Fontes com Resultados',
		sourcesWithoutResultsTitle: 'Fontes sem Resultados',
		user: 'Usuário',
		workflowDisabled: 'Workflow não habilitado',
		workflowApproved: 'Workflow Aprovado',
		workflowDisapproved: 'Workflow Reprovado',
		workflowInAnalysis: 'Workflow em Análise',
		upFlagDisabled: 'upFlag não habilitado',
		upFlag: 'upFlag',
		scoreDisabled: 'Score não habilitado',
		processingError: 'Erro de processamento',
		mediumRisk: 'Risco Médio',
		lowRisk: 'Risco Baixo',
		highRisk: 'Risco Alto',
		undefinedRisk: 'Risco não definido',
		insufficientData: 'Dados insuficientes',
	},
	detailedResult: 'Detalhado',
	summaryResult: 'Analítico',
	export: 'Exportar',
	start: 'Início',
	derivations: 'Derivações',
	dossierHistory: 'Histórico de Dossiês',
	AnalysisComments: 'Análise e comentários',
	description:
		'Acompanhe o histórico de dossiês criados com esse mesmo critério',
	dossierId: 'ID do Dossiê',
	creationDate: 'Data da criação',
	cpfCnpj: 'CPF/CNPJ',
	name: 'Nome',
	consultationProfile: 'Perfil de consulta',
	creationDate: 'Data de criação',

	analytics: {
		filtersTitle: 'Filtros',
		buttonFilter: 'Filtrar',
		courtsTitle: 'Tribunais',
		superiorCouncils: 'Tribunais Superiores e Conselhos',
		regionalFederalCourts: 'Tribunais Regionais Federais',
		courtsOfJustice: 'Tribunais de Justiça',
		regionalElectoralCourts: 'Tribunais Regionais Eleitorais',
		regionalLaborCourts: 'Tribunais Regionais do Trabalho',
		militaryCourtsOfJustice: 'Tribunais de Justiça Militar',
		federalCourtsOfJustice: 'Tribunais de Justiça Federal',
		caseStatusTitle: 'Status de processos',
		inProgress: 'Em tramitação',
		underAppeal: 'Em grau de recurso',
		suspended: 'Suspenso',
		finalArchive: 'Arquivamento definitivo',
		provisionalArchive: 'Arquivamento provisório',
		administrativelyArchived: 'Arquivado administrativamente',
		archive: 'Arquivamento',
		judged: 'Julgado',
		extinct: 'Extinto',
		author: 'Autor',
		defendant: 'Réu',
		involved: 'Envolvido',
		participationInProcesses: 'Participação no processos',
	},
	headerTitle: 'Dossiê',
	foxy_insights: {
		title: 'Insights da Foxy IA',
		company_name: 'Razão social',
		opening_date: 'Data de abertura',
		domains: 'Domínios',
		resume_activity: 'Resumo de atuação',
		resume: 'Resumo',
		see_more: 'Ver mais',
		social_medias: 'Redes sociais',
		marks_and_products: 'Marcas e produtos',
		technologies_business_processes: 'Tecnologias e processos empresariais',
		competitivity_and_positioning: 'Competitividade e posicionamento',
		trends_and_insights: 'Tendências e insights da indústria',
		address: 'Endereço',
		contacts: 'Contatos',
		close: 'Fechar',
		registered_domains: 'Domínios registrados',
		address_not_available: 'Endereço não disponível',
		contacts_not_find: 'Contatos não localizados',
		status: 'Status',
		cnpj: 'CNPJ',
		fantasy_name: 'Nome Fantasia',
		domains_registered: 'Domínios registrados',
		view_more: 'Ver mais',
		business_size: 'Porte da Empresa',
		legal_nature: 'Natureza Jurídica',
		initial_capital: 'Capital Inicial',
		main_activity: 'Atividade Principal',
		secondary_activities: 'Atividades Secundárias',
		board_of_directors: 'Diretoria',
		shareholders: 'Acionistas',
	},
	totalResults: 'notícias',
	totalResult: 'notícia',
	amountWords: 'palavras',
	amountCountry: 'país',
	amountWord: 'Palavra',
	quantityCountries: 'países',
	data_news: 'Notícias do mês',
	media_vehicles: 'Veículos de mídia',
	words_key: 'Palavras citadas',
	highlights: 'Destaques',
	quantidadePalavrasCitadas: 'Quantidade de palavras citadas',
	verMais: 'ver mais',
	noticiasPorMes: 'Notícias por mês',
	selectYear: 'Selecione o ano',
	months: {
		Janeiro: 'Janeiro',
		Fevereiro: 'Fevereiro',
		Março: 'Março',
		Abril: 'Abril',
		Maio: 'Maio',
		Junho: 'Junho',
		Julho: 'Julho',
		Agosto: 'Agosto',
		Setembro: 'Setembro',
		Outubro: 'Outubro',
		Novembro: 'Novembro',
		Dezembro: 'Dezembro',
	},
	noResult: {
		line1: 'Não obtivemos nenhum resultado',
		line2: 'sobre esse assunto'
	},
	mediaVehiclesTitle: 'Veículos de mídia',
	percentage: 'Porcentagem',
	mediaVehicles: 'Veículos de mídia',
	mediaVehiclesName: 'Nome do veículo',
	citedWords: 'Palavras',
	name: 'Nome',
	news_without_image: 'Notícia sem imagem',
	date: 'Consulta realizada em {date}',
	criteria: 'com o critério {criteria}',
	classeDeProcessos: 'Classe de processos',
	verMais: 'Ver mais',
	classeDeProcessosTitle: 'Classe de processos',
	others: 'Outros',
	principaisAssuntos: 'Principais assuntos',
	varaDosProcessos: 'Vara dos Processos',
	processosTribunais: 'Quantidade de processos por tribunais',
	download: 'Download',
	details: 'Detalhe de processos',
	sourceResults: 'fontes com resultados',
	sourceResult: 'fonte com resultado',
	sourceAnalyticResult: 'resultado',
	sourceAnalyticResults: 'resultados',
	StatusProcessosTitle: 'Status de processos',
	processingError: 'Erro de processamento',
	insufficientData: 'Dados insuficientes para calcular o score',
	convertDossier: {
		title: 'Ocorreu um erro',
		message: 'Tente novamente mais tarde',
		message1: 'Descubra as melhorias no novo dossiê...',
		message2: 'Explore as novas funcionalidades do dossiê...',
		message3: 'Agora você pode adicionar comentários e marcações!',
		message4:
			'Estamos convertendo o seu dossiê para o novo formato, muito mais simples e clean',
		completedMessage:
			"<span style='color: #7922B9;'>Migração concluída! Aproveite as melhorias.</span> Você será redirecionado para o novo dossiê em {count} segundo(s)",
		goBack: 'Voltar para o histórico',
	},
	totalProcesses: 'Total de processos',
	totalProcessValue: 'Valor total de processos',
	processesAsAuthor: 'Processos como autor',
	processesAsDefendant: 'Processos como réu',
	processesAsInvolved: 'Processos como envolvido',
	processDetail: 'Detalhe dos processos',
	processesAsAuthorTitle: 'Processos como autor',
	processesAsDefendantTitle: 'Processos como réu',
	processesAsInvolvedTitle: 'Processos como envolvido',
	authorDescription: 'Confira os processos como autor que você filtrou!',
	defendantDescription: 'Confira os processos como réu que você filtrou!',
	involvedDescription: 'Confira os processos como envolvido que você filtrou!',
	processesAsDefendant: 'Processos como réu',
	processesAsInvolved: 'Processos como envolvido',
	uf: 'UF',
	processTitle: 'Assunto do processo',
	processNumber: 'Número do processo',
	processValue: 'Valor do processo',
	participation: 'Participação',
	downloadAll: 'Baixar todos',
	details: 'Detalhes',
	processLike: 'Processo como',
	notInformed: 'Não informado',
	checkProcesses: 'Confira os processos que você filtrou!',
	pdfProcessing:
		'Seu PDF está em processamento. Confira na sua lista de Dossiês.',
	pdfProcessed:
		'Seu PDF já processou. Confira na sua lista de Dossiês.',
	email: 'E-mail',
	site: 'Site',
	phone: 'Telefone',
	expandAllSources: 'Expandir todas as fontes',
	collapseAllSources: 'Recolher todas as fontes'
}
