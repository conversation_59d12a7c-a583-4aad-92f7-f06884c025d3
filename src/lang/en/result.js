/* eslint-disable no-dupe-keys */
export const layoutResult = {
	common: {
		attention: 'Attention',
		cancel: 'Cancel',
		back: 'Back',
		continue: 'Continue',
		import: 'Import',
		save: 'Save',
		exportation: 'Export',
		asInPt: 'as',
		close: 'Close',
		name: 'Name',
		criteria: 'Criteria',
		searchProfile: 'Search Profile',
		homonyms: 'Homonyms',
		actions: 'Actions',
		created_at: 'Created On',
		high: 'High',
		low: 'Low',
		medium: 'Middle',
		informations: 'Information',
		analysisAndComment: 'Analysis and Comment',
		reason: 'Reason',
		requiredField:
			'* Required field, please enter at least 5 characters to record the reason',
		characters: '/ 200 characters',
		save: 'Save',
		cancel: 'Cancel',
		titleAnalysis: 'Workflow Analysis',
		level: 'Level',
		oldVersionDossier: 'Old Version of the Dossier',
	},
	dossierHistory: {
		batch: 'Batch',
		state: 'State',
		criteria: 'Criteria',
		creationDate: 'Creation Date',
		title: 'Dossier History',
		dossierCreation: 'Dossier Created',
		dossierProcessing: 'Dossier Processing',
		processed: 'Processed',
		withExceptions: 'With Exceptions',
		inLine: 'In Line',
		unknownStatus: 'Unknown Status',
	},

	exportationsHistory: {
		title: 'Dossier Exportations',
		dossierId: 'Dossier ID',
		tableDescription: 'User exports from the last 7 days',

		statuses: {
			queue: 'In Queue',
			processing: 'Processing',
			error: 'Error',
			success: 'Completed',
		},
		tooltips: {
			queue: 'Waiting',
			processing: 'Processing',
			error:
				'PDF Processing Error: We apologize for the inconvenience. Our team of experts has been notified and is working to resolve it as soon as possible.',
			success: 'Completed',
		},
	},
	viewNavigation: {
		title: 'Results',
		buttons: {
			back: 'Back',
			nextDossie: 'Next Dossier',
			previousDossie: 'Previous Dossier',
			queryInOtherApp: 'Query in Other Apps',
		},
	},
	viewSettings: {
		tabs: {
			resume: 'Summary',
			detailedResult: 'Detailed Result',
		},
		buttons: {
			exportResume: 'Export Summary',
			exportDetailed: 'Export Detailed',
		},
		exportOptions: {
			allContent: 'All Content',
			chartAndMap: 'Charts and Maps',
			comments: 'Comments',
			mostRecentComments: 'Most Recent',
			mostRelevantComments: 'Relevant',
			commentsFromWorkflow: 'From Workflow',
			commentsFromSources: 'From Sources',
			anexos: 'Attachments (certificates, etc.)',
			resume: 'FOXY AI Summaries',
		},

		exportModal: {
			configurationText:
				'You can configure the elements below to be displayed in the Dossiers exported as PDF',
			fileHint: `Accepted file formats are
			<strong>.jpg, .jpeg, or .png</strong> with a maximum size of
			<strong>200kb</strong>. Recommended dimensions are 92x92.`,
			addYourLogo: 'Add Your Logo to the PDF',
			addPdfTitle: 'PDF Title',
			input: {
				placeholder: 'Enter a title',
				maximumCharacters: '20 characters',
			},
			logo: {
				errors: {
					invalidSize: 'File size is larger than expected',
					invalidExtension: 'Invalid file extension',
				},
			},
			exporting: 'Exporting PDF...',
		},
	},
	filters: {
		contentVisualization: {
			title: 'Content Visualization',
			subtitle: {
				chartsAndMaps: 'Charts and Maps',
				categories: 'Categories',
				others: 'Others',
			},
			options: {
				displayAll: 'Display All',
				map: 'Map',
				charts: 'Charts',
				legends: 'Legends',
				unhideAll: 'Unhide All',
			},
			actions: {
				save: 'Save',
				clear: 'Clear',
			},
		},
	},
	cardResume: {
		labels: {
			lot: 'Batch',
			profile: 'Profile',
			criteria: 'Criteria',
			responsible: 'Responsible',
			dossier: 'Dossier',
			created: 'Created',
			updateDate: 'Last Update',
			homonyms: 'Homonyms',
		},

		derivations: {
			isDerived:
				'This dossier was generated as a derivation of Dossier {parentDossier}',
			dossiersGenerated:
				'This dossier has derivations and generated {amountOfDerivations} derived dossiers.',
			modal: {
				title: 'Derivations',
				informationFirstPart:
					'The dossier {dossier}, batch {lot} of the criteria',
				informationSecondPart: 'has Derivations and generated',
				informationThirdPart: '{amountOfDerivations} derived dossiers.',
				seeBelow:
					'You can check and access the derived dossiers through the list below.',
			},
		},
	},
	cardResult: {
		labels: {
			withResult: 'With Result',
			withoutResult: 'Without Result',
			unavailable: 'Unavailable',
			dontFindAnyResult: 'No source unavailable',
		},
	},
	sections: {
		information: {
			title: 'General Information',
		},
	},
	analysis: {
		emptyAnalysis: {
			title: 'Would you like to add an opinion to the dossier?',
			description: `After analyzing the dossier, you can add a status for it between approved or disapproved and enter the reason for your decision.
			The status will be fixed at the beginning of the dossier and can be edited if necessary.`,
			action: 'Add Analysis',
		},
		newAnalysis: {
			title: 'Dossier Analysis',
			description: 'Analyze the dossier and give a brief opinion on it.',
			note: 'You are editing',
			label: 'Select a status and the reason for your opinion',
			action: 'Save',
			status: {
				approved: 'Approved',
				reproved: 'Disapproved',
			},
		},
		historyAnalysis: {
			title: 'Edit History',
			description:
				'Below you can check the edit history of the analysis status of this dossier.',
		},
		updateAnalysis: {
			title: 'Dossier Analysis',
			description: 'Analyze the dossier and give a brief opinion on it.',
			note: 'You are editing',
			label: 'Select a status and the reason for your opinion',
			action: 'Save',
			lastUpdate: 'Last Status defined by: {name} on {date}',
			status: {
				approved: 'Approved',
				reproved: 'Disapproved',
			},
		},
		viewAnalysis: {
			title: 'Dossier Analysis',
			description: 'Analyze the dossier and give a brief opinion on it.',
			note: 'You are editing',
			label: 'Select a status and the reason for your opinion',
			action: 'Save',
			lastUpdate: 'The status was edited by: {name} on {date} ',
			showHistory: 'View History',
			status: {
				approved: 'Approved',
				reproved: 'Disapproved',
			},
		},
		writeAnalysis: 'Write your analysis of the Dossier',
		typeHere: 'Type here...',
		lastStatusDefinedBy: 'Last status defined by: {name} {date}',
		approve: 'Approve',
		disapprove: 'Disapprove',
		editAnalysis: 'Edit Analysis',
		writeAnalysis: 'Write the Analysis',
		typeHere: 'Type here',
		lastStatusDefinedBy: 'Last status defined by',
		approve: 'Approve',
		disapprove: 'Disapprove',
		underReview: 'Under Review',
		analysisOfDossier: 'Conclusive analysis of the dossier',
	},
	upFlag: {
		reason: 'Reason',
		reasonNotFound: 'Reason not found',
	},
	workflow: {
		status: {
			approved: 'Approved',
			reproved: 'Disapproved',
			wait: 'Under Review',
		},
		types: {
			auto: 'By Automatic Analysis',
			human: 'By Human Analysis',
		},
		auto: {
			headers: {
				fonts: 'Sources',
				rules: 'Rules',
				occurency: 'Occurrences',
				date: 'Date',
				status: 'Status',
			},
		},
		newComment: 'Add',
		comment: {
			label: 'Comment',
			notes: '* Enter at least 5 characters to record the reason.',
			placeholder: 'type the comment...',
		},
		messages: {
			success: 'Workflow analysis saved successfully',
			error: 'Error saving workflow analysis',
		},
		approver: 'Approver',
		registeredReason: 'Registered Reason',
		evaluation: 'Evaluation',
		evaluationDate: 'Evaluation Date',
		exportPDF: 'Export to PDF',
		approvedAuto: 'Automatic Approval',
		rejectedAuto: 'Automatic Disapproval',
		approved: 'Approved',
		rejected: 'Disapproved',
		approvalLevels: 'Approval Levels',
		writeReason: 'Write a reason and evaluate this Dossier!',
		reject: 'Disapprove',
		approve: 'Approve',
		underAnalysis: 'Under Analysis',
		pendingEvaluation: 'The Dossier is pending evaluation',
	},
	tags: {
		title: 'Tags',
		description: 'Most used tags in this dossier',
		emptyState: {
			title: 'Create and use tags to speed up your analysis.',
			subtitle: 'This dossier does not yet have any tags.',
			description: `You can create your tags in the category results tab in the Summary, or in specific sources in the Detailed Results tab, through the # icon.`,
		},
	},
	upScore: {
		title: 'upScore',
		description:
			'The information generated in this score is provided for informational purposes based on the results configured in the score registration and the consultation date of the day',

		risk: 'Risk',

		scoreNotDefined: {
			message: 'Risk not defined',
			reason:
				'We did not get a response from the sources searched and therefore could not classify a risk for this dossier!',
		},

		invalidScore: 'Error processing sources',
		tabs: {
			all: 'General',
		},
		columns: {
			param: 'Parameter',
			results: 'Results',
			risk: 'Risk',
			origin: 'Origin',
		},

		unprocessedSourcesWarning:
			'Given the sources that were processed, we have the score below.',
		unprocessedSourcesAmount:
			'One unprocessed source | {count} unprocessed sources',

		seeDetail: 'View Details',
		resultsFinding: '{x} risk parameters {risk} were found for this dossier',
		attention: 'Attention',
		emptyError:
			'We did not obtain results from the sources searched, so we could not classify a risk for this Dossier',
		processError:
			'Some sources could not be processed correctly, resulting in a partial view. To get the complete result, we recommend reprocessing the dossier.',
		description:
			'The information in this score is based on the registration results and the consultation date.',
		attention: 'Attention',
		alto: 'High',
		medio: 'Medium',
		baixo: 'Low',
		geral: 'General',
		download: 'Download',
		partialView:
			'Some sources could not be processed correctly, resulting in a partial view. To get the complete result, we recommend reprocessing the dossier.',
		columns: {
			param: 'Parameter',
			results: 'Results',
			risk: 'Risk',
		},
		error: 'Error',
		noRisk: 'No Risk',
		error: 'Error',
		noRisk: 'No Risk',
		foundParams: '{amount} risk parameter(s) were found in this dossier.',
		view: 'View',
		no_configuration:
			'We analyzed the score settings and <br /> did not find any results with the configured parameter.',
	},
	summaryCompany: {
		header: {
			title: 'Company Description',
			callToAction:
				'Information generated by FOXY: The artificial intelligence of upLexis',
		},
		edit: {
			save: 'Save',
			cancel: 'Cancel',
		},
		delete: 'Delete',
		modalDelete: {
			title: 'Attention!',
			description:
				'You are about to delete the summary generated by FOXY AI. After removing, to activate it again, you will have to generate a new dossier.',
			note: 'Are you sure you want to continue?',
			save: 'Save',
			cancel: 'Cancel',
		},
		actions: {
			byChatGPT:
				'The information provided here was generated by FOXY AI and is based on internet data. A review of the content may be necessary.',
			byUser: 'Information edited on {date} by {user}',
		},
		toastSuccessfullyRemoved: 'Description successfully removed!',
		toastErrorRemoved: 'Error removing description',
		toastUpdatedSuccessfully: 'Description updated',
		toastUpdatedError: 'Error updating description',
	},
	comments: {
		sourceWithComments: 'Source with comments',
		sourceWithoutComments: 'Source without comments',
		openSourceComments: 'Open source comments',
		closeSourceComments: 'Close source comments',
		title: 'Comments',
		mentions: {
			emptyState: 'No mentions at this time.',
		},
		emptyState: 'No comments at this time.',
		newComment: 'Add',
		cancelComment: 'Cancel',
		answerComment: 'Reply',
		editComment: 'Edit',
		placeholder: 'type the comment...',
		tooltip: 'Mark as relevant',
		noResult: 'No results',
		editDate: 'Edited on {date}',
		return: 'Return',
		original: 'Original',
		markAsRelevant: 'Mark as relevant',
		unmarkAsRelevant: 'Unmark as relevant',
		markAllRelevant: 'Mark all as relevant',
		unmarkAllRelevant: 'Unmark all as relevant',
		showNextComment: 'Show next comment',
		showPreviousComment: 'Show previous comment',
		closeCommentListing: 'Close comment listing',
		deleteComment: 'Delete comment',
		showAll: 'Show All',
		replyingTo: 'Replying to',
		edditedIn: 'Edited on',

		title: 'Final Comments',
		writeComment: 'Write a comment',
		typeHere: 'Type here...',
		send: 'Send',
		commentCreated: 'Comment created successfully!',
		commentEdited: 'Comment edited successfully!',
		fetchError: 'An error occurred while fetching the data.',
		creationError: 'Failed to create the comment.',
		editError: 'Failed to edit the comment.',

		madeComments: 'Made Comments',
		name: 'Name',
		commentDate: 'Comment Date',
		edit: 'Edit',

		modals: {
			deleteModal: {
				warning: `You are about to <strong>delete</strong> your comment, if there are any interactions, they will also be deleted.`,
				confirmation: `Are you sure you want to continue?`,
			},
		},
		history: {
			title: 'Edit History',
		},
		settings: {
			title: 'Settings',
			notification_by_upminer: 'Receive notifications via upminer',
			notifications_by_email: 'Receive notifications via email',
			only_special: 'Display only relevant',
		},
	},
	error: {
		internalError: 'Internal Error',
	},
	information: {
		logo: 'Uplexis Logo',
		tag: 'Tag',
		criterion: 'Criterion',
		name: 'Name',
		dossierNumber: 'Dossier Number',
		consultationProfile: 'Consultation Profile',
		responsible: 'Responsible',
		creationDate: 'Creation Date',
		lastUpdate: 'Last Processing',
		homonyms: 'Homonyms',
		sourcesWithResults: 'Sources with Results',
		sourcesWithoutResults: 'Sources without Results',
		attention: 'Attention',
		download: 'Download',
		flags: 'Flags',
		amount: 'Amount',
		workflowApproved: 'Approved Workflow',
		workflowRejected: 'Rejected Workflow',
		workflowUnderReview: 'Workflow Under Review',
		riskLow: 'Low Risk',
		riskMedium: 'Medium Risk',
		riskHigh: 'High Risk',
		scoreProcessingError: 'Score Processing Error',
		undefinedRisk: 'Undefined Risk',
		sourcesWithResultsTitle: 'Sources with Results',
		sourcesWithoutResultsTitle: 'Sources without Results',
		user: 'User',
		workflowDisabled: 'Workflow not enabled',
		workflowApproved: 'Workflow Approved',
		workflowDisapproved: 'Workflow Disapproved',
		workflowInAnalysis: 'Workflow Under Analysis',
		upFlagDisabled: 'upFlag not enabled',
		upFlag: 'upFlag',
		scoreDisabled: 'Score not enabled',
		processingError: 'Processing Error',
		mediumRisk: 'Medium Risk',
		lowRisk: 'Low Risk',
		highRisk: 'High Risk',
		undefinedRisk: 'Undefined Risk',
		insufficientData: 'Insufficient Data',
	},
	detailedResult: 'Detailed',
	summaryResult: 'Analytical',
	export: 'Export',
	start: 'Start',
	derivations: 'Derivations',
	dossierHistory: 'Dossier History',
	AnalysisComments: 'Analysis and Comments',
	description: 'Follow the history of dossiers created with the same criteria',
	dossierId: 'Dossier ID',
	creationDate: 'Creation Date',
	cpfCnpj: 'CPF/CNPJ',
	name: 'Name',
	consultationProfile: 'Consultation Profile',
	creationDate: 'Creation Date',

	analytics: {
		filtersTitle: 'Filters',
		buttonFilter: 'Filter',
		courtsTitle: 'Courts',
		superiorCouncils: 'Superior Courts and Councils',
		regionalFederalCourts: 'Regional Federal Courts',
		courtsOfJustice: 'Courts of Justice',
		regionalElectoralCourts: 'Regional Electoral Courts',
		regionalLaborCourts: 'Regional Labor Courts',
		militaryCourtsOfJustice: 'Military Courts of Justice',
		federalCourtsOfJustice: 'Federal Courts of Justice',
		caseStatusTitle: 'Case Status',
		inProgress: 'In Progress',
		underAppeal: 'Under Appeal',
		suspended: 'Suspended',
		finalArchive: 'Final Archive',
		provisionalArchive: 'Provisional Archive',
		administrativelyArchived: 'Administratively Archived',
		archive: 'Archive',
		judged: 'Judged',
		extinct: 'Extinct',
		author: 'Author',
		defendant: 'Defendant',
		involved: 'Involved',
		participationInProcesses: 'Participation in Processes',
	},
	headerTitle: 'Dossier',
	foxy_insights: {
		title: 'Foxy AI Insights',
		company_name: 'Company Name',
		opening_date: 'Opening Date',
		domains: 'Domains',
		resume_activity: 'Activity Summary',
		resume: 'Summary',
		see_more: 'See More',
		social_medias: 'Social Media',
		marks_and_products: 'Marks and Products',
		technologies_business_processes: 'Technologies and Business Processes',
		competitivity_and_positioning: 'Competitiveness and Positioning',
		trends_and_insights: 'Industry Trends and Insights',
		address: 'Address',
		contacts: 'Contacts',
		close: 'Close',
		registered_domains: 'Registered Domains',
		address_not_available: 'Address not available',
		contacts_not_find: 'Contacts not found',
		status: 'Status',
		cnpj: 'CNPJ',
		fantasy_name: 'Fantasy Name',
		domains_registered: 'Registered Domains',
		view_more: 'View More',
		business_size: 'Company Size',
		legal_nature: 'Legal Nature',
		initial_capital: 'Initial Capital',
		main_activity: 'Main Activity',
		secondary_activities: 'Secondary Activities',
		board_of_directors: 'Board of Directors',
		shareholders: 'Shareholders',
	},
	totalResults: 'news',
	totalResult: 'news',
	amountWords: 'words',
	amountCountry: 'country',
	amountWord: 'Word',
	quantityCountries: 'countries',
	data_news: 'News of the month',
	media_vehicles: 'Media vehicles',
	words_key: 'Cited words',
	highlights: 'Highlights',
	quantidadePalavrasCitadas: 'Number of cited words',
	verMais: 'see more',
	noticiasPorMes: 'News per month',
	selectYear: 'Select the year',
	months: {
		Janeiro: 'January',
		Fevereiro: 'February',
		Março: 'March',
		Abril: 'April',
		Maio: 'May',
		Junho: 'June',
		Julho: 'July',
		Agosto: 'August',
		Setembro: 'September',
		Outubro: 'October',
		Novembro: 'November',
		Dezembro: 'December',
	},
	noResult: {
		line1: 'We did not obtain any results',
		line2: 'on this subject',
	},
	mediaVehiclesTitle: 'Media vehicles',
	percentage: 'Percentage',
	mediaVehicles: 'Media vehicles',
	mediaVehiclesName: 'Vehicle name',
	citedWords: 'Words',
	name: 'Name',
	news_without_image: 'News without image',
	date: 'Consultation held on {date}',
	criteria: 'with the criteria {criteria}',
	classeDeProcessos: 'Case Class',
	verMais: 'See more',
	classeDeProcessosTitle: 'Case Class',
	others: 'Others',
	principaisAssuntos: 'Main topics',
	varaDosProcessos: 'Court Processes',
	processosTribunais: 'Number of cases by courts',
	download: 'Download',
	details: 'Case Details',
	sourceResults: 'sources with results',
	sourceResult: 'source with result',
	sourceAnalyticResult: 'result',
	sourceAnalyticResults: 'results',
	StatusProcessosTitle: 'Case Status',
	processingError: 'Processing Error',
	insufficientData: 'Insufficient data to calculate the score',
	convertDossier: {
		title: 'An error occurred',
		message: 'Please try again later',
		message1: 'Discover the improvements in the new dossier...',
		message2: 'Explore the new features of the dossier...',
		message3: 'You can now add comments and tags!',
		message4:
			'We are converting your dossier to the new format, much simpler and cleaner',
		completedMessage:
			"<span style='color: #7922B9;'>Migration completed! Enjoy the improvements.</span> You will be redirected to the new dossier in {count} second(s)",
		goBack: 'Back to history',
	},
	totalProcesses: 'Total cases',
	totalProcessValue: 'Total case value',
	processesAsAuthor: 'Cases as author',
	processesAsDefendant: 'Cases as defendant',
	processesAsInvolved: 'Cases as involved',
	processDetail: 'Case details',
	processesAsAuthorTitle: 'Cases as author',
	processesAsDefendantTitle: 'Cases as defendant',
	processesAsInvolvedTitle: 'Cases as involved',
	authorDescription: 'Check out the cases as author you filtered!',
	defendantDescription: 'Check out the cases as defendant you filtered!',
	involvedDescription: 'Check out the cases as involved you filtered!',
	processesAsDefendant: 'Cases as defendant',
	processesAsInvolved: 'Cases as involved',
	uf: 'UF',
	processTitle: 'Case title',
	processNumber: 'Case number',
	processValue: 'Case value',
	participation: 'Participation',
	downloadAll: 'Download all',
	details: 'Details',
	processLike: 'Case as',
	notInformed: 'Not informed',
	pdfProcessing:
		'Your PDF is being processed. Check it in your list of Dossiers.',
	pdfProcessed:
		'Your PDF has already been processed. Check it in your list of Dossiers.',

	checkProcesses: 'Check the cases you filtered!',
	pdfProcessing:
		'Your PDF is being processed. Check it in your list of Dossiers.',
	email: 'E-mail',
	site: 'Site',
	phone: 'Phone',
	expandAllSources: 'Expand all sources',
	collapseAllSources: 'Collapse all sources'
}
