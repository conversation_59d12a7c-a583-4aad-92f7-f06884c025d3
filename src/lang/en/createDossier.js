export const createDossier = {
	title: 'Dossier Creation',
	breadcrumb: {
		profileType: 'Profile Type',
		queryProfile: 'Query Profile',
		criteria: 'Criteria',
		tag: 'Tag',
		parameters: 'Parameters',
		summary: 'Summary',
		parametrizations: 'Parameters',
	},
	common: {
		physicalPerson: 'Individual',
		legalPerson: 'Legal Entity',
		name: 'Name',
		cpf: 'CPF',
		corporateReason: 'Corporate Name',
		cnpj: 'CNPJ',
		profileTypeMessage: 'Choose the profile type for your search:',
		criteriaModelMessage: 'Which criteria model will you use in your search?',
		nextStep: 'Next Step',
		toBack: 'Go Back',
	},
	criteria: {
		addTemplate: 'Add your spreadsheet here',
		spreadsheetTemplate: 'Spreadsheet Template',
		viewMore: 'View More',
		searchOfCnpj: 'Search by CNPJ',
		corporateReason: 'Search by Corporate Name',
		insertCriteriaMessage: 'Enter the criteria you want to use in your search',
		spreadsheetError: 'Spreadsheet Error',
		criteria: 'criteria',
		spreadsheetErrorDescription:
			'Error in your spreadsheet! We recommend using our spreadsheet template',
		insertCriteriaMessage: 'Enter Criteria',
		addTemplate: 'Add Template',
		criteria: 'Criteria',
		searchCPF: 'Search by CPF',
		searchCNPJ: 'Search by CNPJ',
		searchName: 'Search by Name',
		searchCorporateName: 'Search by Corporate Name',
		errorSpreadsheet:
			'Error in your spreadsheet! We recommend using our spreadsheet template',
	},

	queryProfile: {
		startByChoosingProfile:
			'Start by choosing a query profile. You can opt for a standardized alternative or a custom option from your company:',
		standardizedQueryProfiles: 'Standardized Query Profiles',
		customQueryProfiles: 'Custom Query Profiles',
		searchProfile: 'Search for your profile name',
		noProfilesFound: 'No profiles found',
		checkDetails: 'Check Details',
		quickView:
			'Offers a predefined configuration, ideal for getting a quick and comprehensive view of the data',
		specificSearch:
			'Provides sources chosen by your company, an appropriate alternative for specific searches',
		customQueryProfiles: 'Custom Query Profiles',
		specificSearch: 'Specific Search',
		noProfilesFound: 'No profiles found',
		customQueryProfiles: 'Custom Query Profiles',
		specificSearch: 'Specific Search',
		noProfilesFound: 'No profiles found',
		standardizedQueryProfiles: 'Standardized Query Profiles',
		quickView: 'Quick View',
		checkDetails: 'Check Details',
		noProfilesMatch: 'None of the profiles meet your needs?',
		createNewProfile: 'Click here to create a new one!',
		searchPlaceholder: 'Search for your profile name',
	},

	parametrizations: {
		addParameters:
			'Add parameters to enrich your search and find more accurate results',
		previousStep: 'Go Back',
		nextStepCreateDossier: 'Create Dossier',
		createAndProcessAutomatically: 'Create and process Dossiers automatically',
		consultCriteriaByName: 'Consult criteria by exact name',
		bancoCentralTitle: 'Central Bank - CRSFN - Summaries and Decisions',
		mandatory: 'Mandatory',
		buscadorGoogleTitle: 'Google Search',
		autoMarkAsRelevant: 'Automatically mark as relevant',
		certidaoAcoesExecucoesTile:
			'Certificate of Civil and Criminal Actions and Executions - TRF2',
		certidaoCeatTitle: 'CEAT Certificate TRT15 - Campinas and region',
		certidaoCguTitle:
			'Certificates from the Office of the Comptroller General of the Union',
		certidaoDistribuicoesAcoesTitle:
			'Certificate of Distribution of Civil, Tax, and Criminal Actions - TRF 3',
		certificateType: 'Certificate Type',
		certidaoJudicialTitle: 'Judicial Certificate TRF4',
		comprotTitle: 'Comprot - Communication and Protocol',
		consultaSocioTitle: 'Partner Consultation',
		proximityCriterion: 'Criterion proximity in %',
		autoMarkAsRelevant: 'Automatically mark as relevant',
		googleSearcherTitle: 'Google Search (Non-specific)',
		ministerioTurismoTitle: 'Ministry of Tourism Cadastrur',
		state: 'State',
		pessoaGoldBoaVistaTitle: 'Gold Person Boa Vista',
		ppeTitlutarPagaTitle: 'PPE Paid Holder',
		buscarPessoasRelacionadas: 'Search for Related People:',
		ppeTitlutarPrivadaTitle: 'PPE Private Holder',
		savedParameter: 'Saved Parameters',
		spcMixMaisTitle: 'SPC MIX MAIS (SPC BRAZIL)',
		participationInCompanies: 'Participation in companies',
		societalControl: 'Corporate Control',
		action: 'Action',
		addName: 'Add a name for this parametrization',
		textPlaceholder: 'Enter the name',
		cancel: 'Cancel',
		save: 'Save',
		titleDiarioOficial: 'Official Gazettes',
		titleDiarioOficialPay: 'Official Gazettes (Paid)',
		relevanceCheckboxDiarioOficial: 'Automatically mark queries as relevant',
		diarioLabel: 'Gazette',
		placeholder: 'Select',
		keywordPlaceholderDiarioOficial: 'Keywords (Optional)',
		cadeTitle: 'Cade - ADMINISTRATIVE COUNCIL FOR ECONOMIC DEFENSE',
		relevanceCheckboxCade: 'Automatically mark searches as relevant',
		searchInLabel: 'Search in',
		processesLabel: 'Processes',
		generatedDocumentsLabel: 'Generated Documents',
		externalDocumentsLabel: 'External Documents',
		exactSearchLabel: 'Exact Search',
		exactNameSearch: 'Search by exact name criterion',
		BaseJuridicaOfflineEscavador: 'Offline Legal Database Escavador',
		BaseJuridicaOfflineEscavadorDoc: 'Offline Legal Database - Document',
		offlineLegalDatabaseNotice:
			'Offline legal databases provide data quickly and without instability. However, these databases focus on official gazettes and may present discrepancies compared to data available in online courts.',
		uf: 'UF',
		tribunais: 'Courts',
		attention: 'Attention!',
		select: 'Select',
		allSelected: 'All selected',
		fullName: 'Full Name',
		motherFullName: 'Mother Full Name',
		parts: 'Parts',
		justice: 'Justice',
		djenTitle: 'Djen',
		processosJuridicosTitle: 'Legal Proceedings',
		fontesJuridicasTitle: 'Legal Proceedings (New)',
		mpfRelevanciaTitle: 'MPF (Relevance)'
	},

	tag: {
		tagDescription:
			'Define a tag to categorize your dossier and find it easily when needed.',
		selectExistingTag:
			'Select an existing tag to classify your dossier or create a new one.',
		newTag: 'New Tag',
		selectColor: 'Select Color',
		save: 'Save',
		toBack: 'Go Back',
		nextStep: 'Next Step',
		skip: 'Skip',
		select: 'Select',
		tagCreated: 'Tags Created',
		tagDescription: 'Tag Description',
		selectExistingTag: 'Select an Existing Tag',
		tagCreated: 'Tags Created',
		select: 'Select',
		doNotAssignTag: 'Do not assign tag to this dossier',
		createAndProcessAutomatically: 'Create and process Dossiers automatically',
	},
	dialog: {
		attention: 'Attention',
		creatingDossiers:
			'{amount} dossiers will be created in {countdown} seconds.',
		cancel: 'Cancel',
		addName: 'Add a name for this configuration',
		textPlaceholder: 'Name',
		save: 'Save',
	},

	toastDossierCreatedSuccess: 'Dossier created successfully!',
	buttonBackToDossiersHistory: 'Back to Dossiers History',
	profileTypePf: 'Individual',
	profileTypePJ: 'Legal Entity',
	spreadsheetTemplate: 'Spreadsheet Template',
	autoCreateAndProcessDossiers: 'Automatically create and process Dossiers',
	duplicityAlert: 'Duplicity Alert',
	AdditionalAlertTitleSingular: 'An extra file will be created ({count})!',
	AdditionalAlertTitlePlural: '{count} extra files will be created!',
	AdditionalAlertSubTitle: 'Do you still want to create?',
	Additionalbutton: 'Create anyway',
	criteriosProcessados:
		'The following criteria have already been processed in the last {days} days',
	criteriosBaseados:
		'The alert is based only on the criterion, not considering the query profile.',
	numeroDossie: 'Dossier No.',
	criacao: 'Creation',
	grupo: 'Group',
	usuario: 'User',
	voltarRevisar: 'Go back and review',
	criarDossie: 'Create the dossier anyway',
}
