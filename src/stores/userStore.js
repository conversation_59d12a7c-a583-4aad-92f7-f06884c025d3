import { useExportationsStore } from './exportationsStore'

import { setCookie } from '../external/storage/cookie'

import { getUserInfo } from '@/services/user.service'

import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore('user', () => {

	const saveUserToLocalStorage = (userData) => {
		const hubspotData = {
			email: userData.email, 
			hubspot_visitor_key: userData.hubspot_visitor_key,
		}
		localStorage.setItem('user-dossie-data-persist', JSON.stringify(hubspotData))
	}

	const user = ref(null)
	const exportationsStore = useExportationsStore()

	const authenticate = (token) => {
		const tomorrowDate = new Date().setDate(new Date().getDate() + 1)

		setCookie(import.meta.env.VITE_APP_SESSION_DATA_KEY, token, tomorrowDate)
	}

	const setUser = () => {
		return getUserInfo().then((userData) => {
			user.value = userData

			saveUserToLocalStorage(userData)

			exportationsStore.updateUserExportationsStatus(1)
		})
	}

	const checkIfUserHasPermissionTo = (permissionToCheck) => {

		if(!user.value) {
			return false
		}

		if (user.value.isAdmin) {
			return true
		}

		const hasPermissions =
			user.value.permissions && user.value.permissions.length > 0
		if (!hasPermissions) {
			return false
		}

		const isMaster = user.value.isMaster

		return isMaster && user.value.permissions.some(
			(permision) => permision === permissionToCheck
		)
	}

	

	return {
		user,
		checkIfUserHasPermissionTo,
		setUser,
		authenticate,
	}
})
