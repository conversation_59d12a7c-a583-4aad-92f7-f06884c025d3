import { useUserStore } from './userStore'

import {
	getPDFExportationStatusFromUser,
	generateDossierPDF,
} from '@/services/pdf.service'

import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useExportationsStore = defineStore('exportations', () => {
	const exportations = ref([])
	const pagination = ref({
		total: 0,
		perPage: 6,
		currentPage: 1,
	})
	const showPdfExportationModal = ref(false)
	const setShowPdfExportationModal = (shouldShow) => {
		showPdfExportationModal.value = shouldShow
	}

	const userStore = useUserStore()

	const hasExportations = computed(() => exportations.value.length > 0)

	const getCurrentUserExportations = async (abortController) => {
		const { data: axiosResponse } = await getPDFExportationStatusFromUser({
			userId: userStore.user.id,
			page: pagination.value.currentPage,
			signal: abortController.signal,
		})
		const userExportations = axiosResponse.data.data

		exportations.value = userExportations
		pagination.value = {
			currentPage: pagination.value.currentPage,
			perPage: axiosResponse.data.per_page,
			total: axiosResponse.data.last_page,
		}
	}

	const checkIfShouldContinuePolling = () => {
		const stillNeedToProcess = exportations.value.some((exportation) =>
			['queue', 'processing'].includes(exportation.status)
		)
		const shouldContinuePolling = hasExportations.value && stillNeedToProcess

		return shouldContinuePolling
	}

	let timer
	let abortController

	const updateUserExportationsStatus = async (page) => {
		pagination.value.currentPage = page

		if (abortController) {
			abortController.abort()
		}

		if (timer) {
			clearInterval(timer)
			timer = null
		}

		abortController = new AbortController()

		try {
			await getCurrentUserExportations(abortController)
		} catch (error) {
			console.log(error)
			return
		}

		timer = setInterval(async () => {
			try {
				await getCurrentUserExportations(abortController)
			} catch (error) {
				if (timer) {
					clearInterval(timer)
					timer = null
				}

				if (abortController) {
					abortController.abort()
				}

				console.log(error)
			}
		}, 10000)
	}

	const downloadExportation = (exportationId) => {
		const a = document.createElement('a')
		const exportation = exportations.value.find((exportation) => {
			return exportation.id === exportationId
		})
		const url = exportation.url.link_s3
		a.href = url
		a.target = '_blank'
		a.download = url.split('/').pop()
		document.body.appendChild(a)
		a.click()
		document.body.removeChild(a)
	}

	const extractDocumentName = (url) => {

		// Extrair a última parte da URL após o último "/"
    const lastSegment = url.split('/').pop();

    // Encontrar o último caractere "_" e extrai até ele
    const lastUnderscoreIndex = lastSegment.lastIndexOf('_');
    const value = lastSegment.substring(0, lastUnderscoreIndex);

    return value;
	};

	const downloadExportationDirect = async (exportationId) => {
		const exportation = exportations.value.find((exportation) => exportation.id === exportationId);

		const url = exportation.url.link_s3;
		const documentName = extractDocumentName(url);

		try {
		  const response = await fetch(url);
		  const blob = await response.blob();

		  const a = document.createElement('a');
		  const objectURL = URL.createObjectURL(blob);
		  a.href = objectURL;
		  a.download = `${documentName}.pdf`;
		  document.body.appendChild(a);
		  a.click();
		  document.body.removeChild(a);

		  URL.revokeObjectURL(objectURL);
		} catch (error) {
		  console.error('Download error:', error);
		}
	  };

	const exportDirectly = async ({ dossier, isDetailed }) => {
		const baseConfigs = {
			id_lote: dossier.lote,
			id_dossie: dossier.id,
			download: 0,
			graficos_mapas: true,
			comentarios: true,
			foxy_ia: true,
			analyze: {
				relevantes: true
			}
		}

		let detailedConfig = {}

		if (isDetailed) {
			detailedConfig = {
				comentarios: {
					mais_recentes: false,
					relevantes: true,
					workflow: true,
					fontes: true,
				},
				categorias: [],
				anexos: true,
			}
		}

		const exportConfigs = {
			...baseConfigs,
			...detailedConfig,
		}

		await generateDossierPDF({
			isDetailed,
			exportConfigs: exportConfigs,
		})

		updateUserExportationsStatus(1)
		setShowPdfExportationModal(true)
	}

	return {
		pagination,
		exportations,
		hasExportations,
		updateUserExportationsStatus,
		downloadExportation,
		downloadExportationDirect,
		showPdfExportationModal,
		setShowPdfExportationModal,
		exportDirectly,
	}
})
