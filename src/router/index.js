import TestComponents from '../modules/dossier/new-result/TestComponents.vue'
import ListAllDossiersPage from '../modules/dossier/list/Page.vue'
import CreateDossie from '../modules/dossier/create/Page.vue'
import ResultDossie from '../modules/dossier/new-result/Page.vue'
import ConvertDossier from '../modules/dossier/new-result/ConvertDossier.vue'
import ListSearchProfiles from '@/modules/profile/list/Page.vue'
import CreateSearchProfile from '@/modules/profile/create/Page.vue'
import { useUserStore } from '@/stores/userStore'

import { createRouter, createWebHistory } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useCategoriesStore } from '@/modules/profile/stores/categories'
import { useGroupsStore } from '@/modules/profile/stores/groups'
import { useToast } from '@aplicativo/uplexis-ui'

const router = createRouter({
	history: createWebHistory(import.meta.env.BASE_URL),
	routes: [
		{
			path: '/',
			component: ListAllDossiersPage,
		},
		{
			path: '/search-profiles',
			beforeEnter: async (to, from, next) => {
				const toast = useToast()
				try {
					const categoriesWithSources = useCategoriesStore()
					const groupsStore = useGroupsStore()

					const promises = [
						groupsStore.populateGroups(),
						categoriesWithSources.populateCategories(),
					]

					await Promise.all(promises)

					return next()
				} catch (error) {
					if (error.response) {
						if (error.response.status === 403) {
							toast.addToast({
								content: error.response.data.message,
								type: 'warning',
							})
							return next('/dossiers')
						}
					}
					return false
				}
			},
			children: [
				{
					path: '',
					component: ListSearchProfiles,
				},
				{
					path: 'create',
					component: CreateSearchProfile,
					props: true,
				},
				{
					path: 'edit/:id',
					component: CreateSearchProfile,
					props: true,
				},
			],
		},

		{
			path: '/dossier-result/test-components/:sourceId',
			name: 'testComponents',
			component: TestComponents,
		},

		{
			path: '/dossiers/convert',
			name: 'convertDossier',
			component: ConvertDossier,
		},

		{
			path: '/dossiers/result/:dossierId',
			name: 'resultDossie',
			component: ResultDossie,
		},
		{
			path: '/dossiers',
			name: 'listAllDossiers',
			component: ListAllDossiersPage,
		},
		{
			path: '/dossiers/create',
			name: 'createDossie',
			component: CreateDossie,
		},
		{
			path: '/status',
			component: {
				template: '<div>OK</div>',
			},
		},
		{
			path: '/login/:token',
			name: 'login',
			component: ListAllDossiersPage,
			beforeEnter: async (to, _, next) => {
				let { token } = to.params
				const userStore = useUserStore()
				userStore.authenticate(token)
				const { user } = storeToRefs(userStore)
				const redirectMonitoriaIdDossier = to.query.redirectMonitoriaIdDossier
				if (!user.value) {
					await userStore.setUser()
				}

				if (!user.value) {
					await userStore.setUser()
				}

				if (redirectMonitoriaIdDossier) {
					next(`/dossiers/result/${redirectMonitoriaIdDossier}`)
				} else {
					next('/dossiers')
				}
			},
		},
		{
			// Faz login e derireciona para outra rota
			// o parametro redirect de ser ex:
			// __dossier-result__1 onde os 2 __ serão substituidos por /
			// para montar a rota e redirecionar, o exemplo acima
			// redireciona para /dossier-result/1
			path: '/login/:token/:redirect',
			name: 'loginRedirect',
			component: ListAllDossiersPage,
			beforeEnter: (to, _, next) => {
				let { token, redirect } = to.params
				redirect = redirect.replaceAll('__', '/')
				const userStore = useUserStore()
				userStore.authenticate(token)
				next(redirect)
			},
		},
	],
})
router.beforeEach(async (to) => {
	const isLoginRoute = to.path.includes('login')
	if (!isLoginRoute) {
		const userStore = useUserStore()
		const { user } = storeToRefs(userStore)
		if (!user.value) {
			await userStore.setUser()
		}
	}
})
export default router
