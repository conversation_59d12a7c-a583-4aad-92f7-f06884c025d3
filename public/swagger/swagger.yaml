openapi: 3.0.0
info:
  title: API Documentation
  version: 1.0.0

tags:
  - name: Resultado
  - name: Comenta<PERSON> Geral
  - name: Comentario
  - name: Source
  - name: Workflow
  - name: Upscore
  - name: Reaction
  - name: Tags
  - name: <PERSON><PERSON>
  - name: Upflag
  - name: <PERSON><PERSON><PERSON>
  - name: Dossie
  - name: <PERSON><PERSON><PERSON>
  - name: <PERSON><PERSON><PERSON>
  - name: User
  - name: <PERSON>ia
  - name: Arquivos Personalizados
  - name: Per<PERSON>l de Consulta
  - name: <PERSON><PERSON><PERSON> de Consulta Padrão
  - name: Configuração de Dossiê

components:
  securitySchemes:
    auth:
      type: apiKey
      in: header
      name: Authorization
      x-displayName: TEST

servers:
  - url: "{{ENV_SWAGGER_URL}}"
    description: "{{ENV_SWAGGER_DESCRIPTION}}"

paths:
  /api/dossie:
    get:
      summary: Get Dossie List Paginated
      tags:
        - Dossie
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
      parameters:
        - name: order
          in: query
          description: "Order list by table column. Example: (-id) - order by id descend"
          schema:
            type: string
        - name: per_page
          in: query
          description: "Number of itens returned by page"
          schema:
            type: integer
        - name: page
          in: query
          description: "Page"
          schema:
            type: integer
        - name: id_dossie
          in: query
          schema:
            type: integer
        - name: data_inicio
          in: query
          description: "Example: d/m/Y"
          schema:
            type: string
        - name: data_fim
          in: query
          description: "Example: d/m/Y"
          schema:
            type: string
        - name: id_perfil
          in: query
          description: "Filter by profile IDs (array)"
          schema:
            type: array
            items:
              type: integer
        - name: id_perfil_default
          in: query
          description: "Filter by default profile IDs (array)"
          schema:
            type: array
            items:
              type: integer
        - name: id_tag
          in: query
          description: "Filter by tags IDs (array)"
          schema:
            type: array
            items:
              type: integer
        - name: usuario
          in: query
          description: "Filter by users IDs (array)"
          schema:
            type: array
            items:
              type: integer
        - name: status
          in: query
          description: "Filter by status (status column) (array)"
          schema:
            type: array
            items:
              type: string
              enum: [ created, queue, processing, success, error ]
        - name: criterio
          in: query
          description: "Filter by crierion"
          schema:
            type: string
        - name: notsave_perpage
          in: query
          description: "Don't save per_page filter"
          schema:
            type: boolean
        - name: status_workflow
          in: query
          description: "Filter by workflow"
          schema:
            type: string
            example: "todos / sem_workflow / aprovado / reprovado / em_analise"
        - name: monitoria
          in: query
          description: "Filter by monitoria"
          schema:
            type: boolean
        - name: derivacao
          in: query
          description: "Filter by devirated"
          schema:
            type: boolean


  /api/foxy-ai/insights:
    post:
      summary: Get data of foxyAI
      tags:
        - Resultado
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                cnpj:
                  type: string
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/homonyms:
    post:
      summary: search homonyms
      tags:
        - Resultado
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  required: true
                birth_date:
                  type: string
                  nullable: true
                mother_name:
                  type: string
                  nullable: true
      responses:
        '200':
          description: Success
      security:
        - auth: [ ]
  
  /api/dossie/limit:
    get:
      summary: Gets users Dossie Limit list
      tags:
        - Configuração de Dossiê
      parameters:
        - name: search
          in: query
          description: name to search
          schema:
            -type: string
        - name: page
          in: query
          description: value page
        - name: per_page
          in: query
          description: quantity per page
      responses:
       '200':
          description: OK
      security:
        - auth: [ ]
    post:
      summary: Update user Dossie Limit
      tags:
        - Configuração de Dossiê
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                user_id:
                  type: integer
                  required: true
                limite_mensal:
                  type: integer
                  nullable: true
                limite_total:
                  type: integer
                  nullable: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/limit/limite-resultado-fontes:
    get:
      summary: Gets sources Dossie Limit list
      tags:
        - Configuração de Dossiê
      responses:
       '200':
          description: OK
      security:
        - auth: [ ]
    post:
      summary: Update user group Dossie Limit
      tags:
        - Configuração de Dossiê
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                grupo:
                  type: integer
                  description: ID group
                  required: true
                fonte:
                  type: string
                  description: method source
                  required: true
                qtd:
                  type: integer
                  required: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/export-pdf-details:
    post:
      summary: Send of queue export details pdf
      tags:
        - Resultado
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_lote:
                  type: integer
                  description: ID of batch
                  required: true
                id_dossie:
                  type: integer
                  description: ID of dossier
                  required: true
                download:
                  type: integer 
                  required: true
                  minimum: 0
                graficos_mapas:
                  type: boolean
                  description: export graphs and maps
                  required: true
                analyze:
                  type: object
                  properties:
                    relevantes:
                      type: boolean
                      description: export only relevance 
                comentarios:
                  type: object
                  properties:
                    mais_recentes:
                      type: boolean
                      required: true
                      description: export only recent comments 
                    relevantes:
                      type: boolean
                      required: true
                      description: export only relevance comments 
                    workflow:
                      type: boolean
                      required: true
                      description: export comments of workflow
                    fontes:
                      type: boolean
                      required: true
                      description: export comments of source
                  required: true
                foxy_ia:
                  type: boolean
                  description: export analytics foxyAI
                  required: true
                anexos:
                  type: boolean
                  description: export attachments from source result
                  required: true
                categorias:
                  required: false
                  type: array
                  description: categories to be exported
                titulo_pdf:
                  required: false
                  type: string
                  description: title of pdf exported 
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/export-pdf-resume:
    post:
      summary: Send of queue exported pdf
      tags:
        - Resultado
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_lote:
                  type: integer
                  description: ID of batch
                  required: true
                id_dossie:
                  type: integer
                  description: ID of dossier
                  required: true
                download:
                  type: boolean 
                  required: true
                graficos_mapas:
                  type: boolean
                  description: export graphs and maps
                  required: true
                comentarios:
                  type: boolean
                  required: true
                foxy_ia:
                  type: boolean
                  description: export analytics foxyAI
                  required: true
                apenas_tags:
                  required: false
                  type: boolean
                  description: export only result with tag
                titulo_pdf:
                  required: false
                  type: string
                  description: title of pdf exported 
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/export-pdf-lawsuits:
    post:
      summary: Send for export pdf
      tags:
        - Resultado
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_dossie:
                  type: integer
                  description: ID of dossier
                  required: true
                lawsuits_numbers:
                  type: array
                  items:
                    type: string
                  required: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/comentario/{dossieID}:
    get:
      summary: List Comments
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    post:
      summary: Create Comment
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 1090
                  description: text comment
                comment_id_replay:
                  type: integer
                  minimum: 1
                  description: ID of the comment being replied to
                is_relevante:
                  type: boolean
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    put:
      summary: Update Comment
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 1090
                  description: text comment
                comment_id:
                  type: integer
                  required: true
                  minimum: 1
                  description: ID of the comment
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/comentario/{dossieID}/{comentarioID}:
    delete:
      summary: Delete Comment
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: comentarioID
          in: path
          description: ID of the comment
          required: true
          schema:
            type: integer
  /api/dossie/get-status-pdf-request:
    get:
      summary: Get Status PDF Request
      tags:
        - Dossie
      parameters:
        - name: request_id
          in: query
          description: ID of the request
          schema:
            type: integer
        - name: dossie_id
          in: query
          description: ID of the dossie
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              examples:
                success:
                  value:
                    status_pdf: "O PDF foi gerado com sucesso"
                    url: "https://your-pdf-url.com/your-pdf.pdf"
                error:
                  value:
                    status_pdf: "Ocorreu um erro ao processar o pedido"
                    url: null
                queue:
                  value:
                    status_pdf: "O pedido está na fila para processamento"
                    url: null
                processing:
                  value:
                    status_pdf: "O pedido está sendo processado"
                    url: null
                not:
                  value:
                    status_pdf: "Nenhum PDF foi gerado para este Dossiê"
                    url: null
        '422':
          description: Unprocessable Entity
          content:
            application/json:
              example:
                message: The given data was invalid.
                errors:
                  dossie_id:
                    - Dossie ID is required
        '500':
          description: Internal Server Error
          content:
            application/json:
              example:
                message: An error occurred while processing the request
      security:
        - auth: [ ]

  /api/dossie/comentario/historico/{dossieID}/{comentarioID}:
    get:
      summary: Comment Historic
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: comentarioID
          in: path
          description: ID of the comentario
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/comentario/mencoes/{dossieID}:
    get:
      summary: List marcked Comments
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/comentario/configuracao/{dossieID}:
    get:
      summary: Get config of the Comments
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    post:
      summary: Add config of the Comments
      tags:
        - Comentario Geral
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                hasNotifyViaUpminer:
                  type: boolean
                  required: true
                hasNotifyViaEmail:
                  type: boolean
                  required: true
                seeOnlyRelevantComments:
                  type: boolean
                  required: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/comentario/reacao/{dossieID}/{commentID}/{reactionID}:
    post:
      summary: Create Comentario Reacao
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: commentID
          in: path
          description: ID of the comentario
          required: true
          schema:
            type: integer
        - name: reactionID
          in: path
          description: ID of the reaction
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

    put:
      summary: Update Comentario Reacao
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: commentID
          in: path
          description: ID of the comentario
          required: true
          schema:
            type: integer
        - name: reactionID
          in: path
          description: ID of the reaction
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

    delete:
      summary: Delete Comentario Reacao
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: commentID
          in: path
          description: ID of the comentario
          required: true
          schema:
            type: integer
        - name: reactionID
          in: path
          description: ID of the reaction
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/comentario/relevante/{dossieID}:
    put:
      summary: Update is_relevante in all comments of dossier
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                is_relevante:
                  type: boolean
                  required: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/comentario/relevante/{dossieID}/{commentID}:
    post:
      summary: Create Comentario Reacao
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: commentID
          in: path
          description: ID of the comentario
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

    delete:
      summary: Delete Comment Relevance
      tags:
        - Comentario
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: commentID
          in: path
          description: ID of the comentario
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/descritivo/{dossieID}:
    get:
      summary: Get Summary Company
      tags:
        - SummaryCompany
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

    post:
      summary: Edit Summary Company
      tags:
        - SummaryCompany
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                text:
                  type: string
                  minimum: 1
                  maximum: 2048
                  required: true
                summary_ref_id:
                  type: int
                criterio:
                  type: string
                  required: true
                  example: 00.000.000/0000-00
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

    delete:
      summary: Remove summary company
      tags:
        - SummaryCompany
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/reprocess:
    post:
      summary: Reprocess dossies
      tags:
        - Dossie
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                dossies:
                  type: array
                  required: true
                  example: [620076, 620077]

  /api/dossie/resultado/cards/{dossieID}:
    get:
      summary: Get Dossie Result Cards
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/user:
    get:
      summary: Get user session
      tags:
        - User
      responses:
        '200':
          description: OK
          content:
            application/json:
              example:
                status: 200
                data:
                  userAlias: "localhost_1"
                  userImgOrIcon: "https://dashboards.uplexis.com/img/foxy-avatar.af2a826e.svg"
                  userName: "Local Uplexis - 1 local-1"
      security:
        - auth: [ ]

  /api/user/group:
    get:
      summary: Get Users By Group
      tags:
        - Grupo de usuario
      parameters:
        - name: name_or_email
          in: query
          description: Parameter for filtering by email or name
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              example:
                status: 200
                data:
                  id: 1
                  name: "uplexis"
                  usuarios:
                    - id: 1
                      nome_completo: "Local Uplexis - 1 local-1"
                      email: "<EMAIL>"
                    - id: 2
                      nome_completo: "Local Uplexis - 2 local-2"
                      email: "<EMAIL>"
        '422':
          description: Unprocessable Content
          content:
            application/json:
              example:
                message: "Por favor, insira uma string com pelo menos 3 caracteres"
                errors:
                  name_or_email:
                    - "Por favor, insira uma string com pelo menos 3 caracteres"
        '500':
          description: Internal Server Error
          content:
            application/json:
              example:
                status: 500
                error: "internal server error"
      security:
        - auth: [ ]

  /api/user/appsDisponiveis:
    get:
      summary: Get Users By Group
      tags:
        - Dossie
      responses:
        '200':
          description: OK
          content:
            application/json:
              example:
                status: 200
                data:
                  apps:
                    sugeridor:
                      name: "Ficha Cadastral"
                      link: "https://upminer.qa.uplexis.com.br/aplicativos/app/criar-dossie?documento=@input@&tipo_documento=@tipo@"
        '500':
          description: Internal Server Error
          content:
            application/json:
              example:
                status: 500
                error: "internal server error"
      security:
        - auth: [ ]

  /api/dossie/resultado/resumo/grupo/{dossieID}/{grupoAlias}:
    get:
      summary: Get Dossie Resumo grupo
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: grupoAlias
          in: path
          description: alias  of the source group
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/resultado/analytics/{dossieID}/{grupoAlias}:
    get:
      summary: Get Dossie Analytics from a group by dossier id and group alias
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: Dossie ID
          required: true
          schema:
            type: integer
        - name: grupoAlias
          in: path
          description: alias of the source group
          example: juridico
          required: true
          schema:
            type: string
            enum: [juridico, midia_internet]
        # Parâmetros do grupo "tribunais"
        - name: tribunais[tribunais_superiores_conselhos]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "tribunais superiores e conselhos"
        - name: tribunais[tribunais_regionais_federais]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "tribunais regionais federais"
        - name: tribunais[tribunais_justica]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "tribunais de justiça"
        - name: tribunais[tribunais_regionais_eleitorais]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "tribunais regionais eleitorais"
        - name: tribunais[tribunais_regionais_trabalho]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "tribunais regionais do trabalho"
        - name: tribunais[tribunais_justica_militar]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "tribunais de justiça militar"
        - name: tribunais[justica_federal]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by court "justiça federal"

        # Parâmetros do grupo "status"
        - name: status[em_tramitacao]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by status "em tramitação"
        - name: status[em_grau_recurso]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by status "em grau de recurso"
        - name: status[suspenso]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by status "suspenso"
        - name: status[arquivamento_definitivo]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by "arquivamento definitivo"
        - name: status[arquivamento_provisorio]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by "arquivamento provisório"
        - name: status[arquivado_administrativamente]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by "arquivamento administrativo"
        - name: status[arquivamento]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by "arquivamento"
        - name: status[julgado]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by "julgado"
        - name: status[extinto]
          in: query
          schema:
            type: boolean
          required: false
          description: Filter by "extinto"
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/upscore/detalhes/{dossieID}:
    get:
      summary: Get Dossie Upscore
      tags:
        - Upscore
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/workflow/detalhes/{dossieID}:
    get:
      summary: Get Dossie Workflow Details
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/workflow/detalhes/analysis/{dossieID}:
    get:
      summary: Get Dossie Workflow analysis
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description:
            content:
            application/json:
              example:
                status: 200
                data:
                  - userAlias: "tteste"
                    userImgOrIcon: "https://dashboards.uplexis.com/img/foxy-avatar.af2a826e.svg"
                    userName: "Teste Teste"
                    is_relevante: false
                    motivo: "teste"
                    canEdit: true
                    data: "2024-01-04 16:21:54"
                  - userAlias: "suporte"
                    userImgOrIcon: "https://dashboards.uplexis.com/img/foxy-avatar.af2a826e.svg"
                    userName: "Aprovação Automática"
                    is_relevante: true
                    motivo: "Reprovado pela regra:\nConsta resultado na fonte Tribunal Regional Federal."
                    canEdit: false
                    data: "2024-01-04 16:20:22"
      security:
        - auth: [ ]
  /api/dossie/workflow/comentario/{dossieID}:
    get:
      summary: List Comments Dossie Workflow
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

    post:
      summary: Add Comments Dossie Workflow
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 1090
                  description: text comment
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    put:
      summary: Update Comment
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 1090
                  description: text comment
                comment_id:
                  type: integer
                  required: true
                  minimum: 1
                  description: ID of the comment
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    delete:
      summary: Delete Comment
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: comentarioID
          in: path
          description: ID of the comment
          required: true
          schema:
            type: integer

  /api/dossie/workflow/detalhes/aprovar/{dossieID}:
    post:
      summary: Add manual analysis Dossie Workflow
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                motivo:
                  type: string
                  nullable: true
                  minimum: 1
                  maximum: 1090
                status:
                  type: string
                  required: true
                  minimum: 1
                  enum: [ aprovado, reprovado ]
                  description: Status setter of the user
                nivel:
                  type: integer
                  required: true
                  minimum: 1
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/workflow/detalhes/analysis/{dossieID}/reason:
    put:
      summary: Edit the reason of the Dossie Workflow analysis
      tags:
        - Workflow
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                analise_id:
                  type: integer
                  required: true
                  minimum: 1
                motivo:
                  type: string
                  minimum: 1
                  maximum: 1090

      responses:
        '200':
          description:
            content:
            application/json:
              example:
                status: 200
                data: true
      security:
        - auth: [ ]
  /api/source/resultado/testar-template/{sourceID}:
    get:
      summary: Get Source Result Test Template
      tags:
        - Tests
      parameters:
        - name: sourceID
          in: path
          description: ID of the source
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/source/resultado/{dossieID}/{sourceID}:
    get:
      summary: Get Source Result
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: sourceID
          in: path
          description: ID of the source
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/source/comentario/{dossieID}/{sourceID}:
    get:
      summary: List Comments Source
      tags:
        - Source
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: sourceID
          in: path
          description: ID of the source
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    delete:
      summary: Delete Comment
      tags:
        - Source
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: comentarioID
          in: path
          description: ID of the comment
          required: true
          schema:
            type: integer

  /api/source/comentario/{dossieID}:
    post:
      summary: Add Comments Source
      tags:
        - Source
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 1090
                  description: text comment
                source_id:
                  type: integer
                  required: true
                  minimum: 1
                  description: ID of the source
                comment_id_replay:
                  type: integer
                  description: ID of the comment
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    put:
      summary: Update Comment
      tags:
        - Source
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                comment:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 1090
                  description: text comment
                comment_id:
                  type: integer
                  required: true
                  minimum: 1
                  description: ID of the comment
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/reacao/fonte/{dossieID}:
    post:
      summary: Add reaction source
      tags:
        - Reaction
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                source:
                  type: integer
                  required: true
                  minimum: 1
                  description: ID of the source
                reaction:
                  type: integer
                  required: true
                  minimum: 1
                  maximum: 8
                  description: ID of the reaction that will be linked
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/reacao/fonte/{dossieID}/{sourceID}:
    delete:
      summary: Remove reaction source
      tags:
        - Reaction
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: sourceID
          in: path
          description: ID of the source
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/reacao/category/{dossieID}:
    post:
      summary: Add reaction category
      tags:
        - Reaction
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                category:
                  type: string
                  required: true
                  minimum: 1
                  description: alias of the category
                reaction:
                  type: integer
                  required: true
                  minimum: 1
                  maximum: 8
                  description: ID of the reaction that will be linked
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/reacao/category/{dossieID}/{categoryAlias}:
    delete:
      summary: Remove reaction category
      tags:
        - Reaction
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: categoryAlias
          in: path
          description: alias of the category
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/tags/{dossieID}:
    get:
      summary: Get Tags
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    post:
      summary: Create Tag
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tag:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 15
                  description: text of the tag
                cor:
                  type: string
                  required: true
                  minimum: 1
                  description: text of the color
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    put:
      summary: Edit Tag
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tag_id:
                  type: integer
                  minimum: 1
                  required: true
                tag:
                  type: string
                  minimum: 1
                  maximum: 15
                  description: text of the tag
                cor:
                  type: string
                  minimum: 1
                  description: text of the color
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/tags/{dossieID}/{tagID}:
    delete:
      summary: Delete Tag
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: tagID
          in: path
          description: ID of the tag
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/tags/fonte/{dossieID}:
    post:
      summary: Add tag and set tag in source
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tag:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 15
                  description: text of the tag
                cor:
                  type: string
                  required: true
                  minimum: 1
                  description: text of the color
                source:
                  type: integer
                  required: true
                  minimum: 1
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    put:
      summary: edit tag and|or update tag in source
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tag:
                  type: string
                  minimum: 1
                  maximum: 15
                  description: text of the tag
                cor:
                  type: string
                  minimum: 1
                  description: text of the color
                source:
                  type: integer
                  required: true
                  minimum: 1
                tag_id:
                  type: integer
                  required: true
                  minimum: 1
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/tags/fonte/{dossieID}/{sourceID}:
    delete:
      summary: Remove Tag of the source
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: sourceID
          in: path
          description: ID of the source
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/tags/category/{dossieID}:
    post:
      summary: Add tag and set tag in category
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tag:
                  type: string
                  required: true
                  minimum: 1
                  maximum: 15
                  description: text of the tag
                cor:
                  type: string
                  minimum: 1
                  required: true
                  description: text of the color
                category:
                  type: string
                  required: true
                  minimum: 1
                  description: alias of the category
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    put:
      summary: edit tag and|or set tag in category
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                tag:
                  type: string
                  minimum: 1
                  maximum: 15
                  description: text of the tag
                cor:
                  type: string
                  minimum: 1
                  description: text of the color
                category:
                  type: string
                  required: true
                  minimum: 1
                  description: alias of the category
                tag_id:
                  type: integer
                  required: true
                  minimum: 1
                  description: ID of the tag
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/tags/category/{dossieID}/{categoryAlias}:
    delete:
      summary: Remove Tag of the category
      tags:
        - Marcador
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
        - name: categoryAlias
          in: path
          description: alias of the category
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/fonte/hidden/{dossieID}:
    post:
      summary: Set relevance in source result
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                source:
                  type: integer
                  required: true
                  minimum: 1
                sourceIndex:
                  type: integer
                  required: true
                status:
                  type: boolean
                  required: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/analise/{dossieID}:
    get:
      summary: Get analysis
      tags:
        - Analise de Dossie
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
    post:
      summary: Create analysis
      tags:
        - Analise de Dossie
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                motivo:
                  type: string
                  minimum: 1
                  maximum: 1090
                status:
                  type: string
                  required: true
                  minimum: 1
                  enum: [ aprovado, reprovado ]
                  description: Status setter of the user
                is_relevante:
                  type: boolean
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/analise/historico/{dossieID}:
    get:
      summary: List log analysis
      tags:
        - Analise de Dossie
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/analise/{dossieID}/relevance:
    put:
      summary: Update is_relevante in dossier analyze
      tags:
        - Analise de Dossie
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                is_relevante:
                  type: boolean
                  required: true
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/dossie/convertOldDossie/{dossieID}:
    get:
      summary: Migrar dossie antigo
      tags:
        - Migrar
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/upflag/detalhes/{dossieID}:
    get:
      summary: Get Dossie Upflag Details
      tags:
        - Upflag
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/status:
    get:
      summary: Get Status
      tags:
        - Outros
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]

  /api/tags:
    get:
      summary: List all tags
      tags:
        - Tags
      responses:
        '200':
          description: A list of tags
      security:
        - auth: [ ]
    post:
      summary: Create a new tag
      tags:
        - Tags
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                color:
                  type: string
                font_color:
                  type: string
      responses:
        '200':
          description: Tag created
      security:
        - auth: [ ]
  /api/tags/{id}:
    get:
      summary: Get tag details
      tags:
        - Tags
      parameters:
        - name: id
          in: path
          description: ID of the tag
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Tag details
      security:
        - auth: [ ]
    put:
      summary: Update a tag
      tags:
        - Tags
      parameters:
        - name: id
          in: path
          description: ID of the tag to update
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                description:
                  type: string
                color:
                  type: string
                font_color:
                  type: string
      responses:
        '200':
          description: Tag updated
      security:
        - auth: [ ]
    delete:
      summary: Delete a tag
      tags:
        - Tags
      parameters:
        - name: id
          in: path
          description: ID of the tag to delete
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Tag deleted
      security:
        - auth: [ ]

  /api/lote/monitoria/{monitoringId}:
    get:
      summary: Get monitoring by id
      tags:
        - Monitoria
      parameters:
        - name: monitoringId
          in: path
      responses:
        '200':
          description: Monitoring
      security:
        - auth: [ ]
  /api/lote/monitoria/lote/{batchId}:
    get:
      summary: Get monitoring by batch id
      tags:
        - Monitoria
      parameters:
        - name: batchId
          in: path
      responses:
        '200':
          description: Monitoring
      security:
        - auth: [ ]
    post:
      summary: Create new monitoring by batch id
      tags:
        - Monitoria
      parameters:
        - name: batchId
          in: path
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                end:
                  type: string
                periodicity:
                  type: integer
                day:
                  type: string
      responses:
        '200':
          description: Monitoring created
      security:
        - auth: [ ]
    put:
      summary: Update a monitoring by batch id
      tags:
        - Monitoria
      parameters:
        - name: batchId
          in: path
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                end:
                  type: date
                periodicity:
                  type: integer
                day:
                  type: string
      responses:
        '200':
          description: Monitoring updated
      security:
        - auth: [ ]
  /api/lote/monitoria/lote/{batchId}/stop:
    post:
      summary: Stop monitoring by batch ID
      tags:
        - Monitoria
      parameters:
        - name: batchId
          in: path
      responses:
        '200':
          description: Monitoring stopped
      security:
        - auth: [ ]
  /api/lote/monitoria/lote/{batchId}/restart:
    post:
      summary: Restart monitoring by batch ID
      tags:
        - Monitoria
      parameters:
        - name: batchId
          in: path
      responses:
        '200':
          description: Monitoring restarted
      security:
        - auth: [ ]

  /api/dossie/file/custom/list:
    get:
      summary: List of custom files
      tags:
        - Arquivos Personalizados
      parameters:
        - name: user
          in: query
          description: User Id.
          required: false
          schema:
            type: integer
        - name: per_page
          in: query
          description: Itens per page.
          required: false
          schema:
            type: integer
            example: 10
        - name: nome_arquivo
          in: query
          description: File name.
          required: false
          schema:
            type: string
            example: "criterios.csv"
        - name: page
          in: query
          description: Number of page
          required: false
          schema:
            type: integer
            example: 1
      responses:
        '200':
          description: List of custom files
      security:
        - auth: [ ]

  /api/dossie/file/custom/download/{fileId}:
    get:
      summary: Get/Download a custom file
      tags:
        - Arquivos Personalizados
      parameters:
        - name: fileId
          in: path
          description: File id
          required: true
          schema:
            type: integer
            example: 363
        - name: download
          in: query
          description: Get content in text/csv type to download.
          required: false
          schema:
            type: bool
            example: true
      responses:
        '200':
          description: Content of custom file
      security:
        - auth: [ ]

  /api/dossie/get-process/{dossieID}:
    get:
      summary: Get Process Details by Dossie ID
      tags:
        - Dossie
      parameters:
        - name: dossieID
          in: path
          required: true
          schema:
            type: integer
        - name: last
          description: "Return only last process"
          in: query
          required: false
          schema:
            type: boolean
      responses:
        '200':
          description: OK
          content:
            application/json:
              example:
                status: 200
                data: {
                      batch_id: 10,
                      dossier_id: 100,
                      status: "success",
                      process: 1,
                      progress: 100,
                      sources: [
                        {
                          id: 100,
                          source: 286,
                          source_name: "Dados Cadastrais PF (Nova)",
                          criterion: "00000000000",
                          status: "success",
                          message: "",
                          description: "Dados Cadastrais PF (Nova)[Critério : 00000000000] - Consultado",
                          metadata: {
                              details: "Consultado",
                              more: []
                          },
                          link: "/api/dossie/pdf/source/286?id_dossie=100&company=company"
                        }
                      ]
                    }
      security:
        - auth: [ ]

  /api/dossie/get-progress:
    get:
      summary: Get Progress by Dossie IDs
      tags:
        - Dossie
      parameters:
        - name: dossie
          in: query
          description: Array of dossiers ids
          required: true
          schema:
            type: array
      responses:
        '200':
          description: OK
          content:
            application/json:
              example:
                status: 200
                data:
                  dossie: 1,
                  progress: 100
      security:
        - auth: [ ]

  /api/dossie/check-errors:
    get:
      summary: Check dossier source erros which can be fixed by user.
      tags:
        - Dossie
      parameters:
        - in: query
          name: dossies[]
          required: true
          style: form
          explode: true
          schema:
            type: array
            items:
              type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/fix-errors:
    post:
      summary: Fix dossier sources errors
      tags:
        - Dossie
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: "DATA_DIVERGENTE"
                data:
                  type: object
                  additionalProperties:
                    type: string
                  example: { "99999999999": "18/11/2011" }
                dossies:
                  type: array
                  items:
                    type: integer
                  example: [999999, 999999]
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/get/{profileId}:
    get:
      summary: Get Profile by ID
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
        - name: basic
          in: query
          required: true
          schema:
            type: boolean
          description: Returns basic data of profile
        - name: inativo
          in: query
          required: true
          schema:
            type: boolean
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil:
    get:
      summary: Get All Profiles
      tags:
        - Perfil de Consulta
      parameters:
        - name: nome
          in: query
          required: false
        - name: id_perfil
          in: query
          required: false
          schema:
            type: integer
        - name: id_fonte
          in: query
          required: false
          schema:
            type: integer
        - name: id_grupo
          in: query
          required: false
          schema:
            type: array
            required: true
            items:
              type: integer
        - name: ativo
          in: query
          required: true
          schema:
            type: boolean
        - name: per_page
          in: query
          required: false
          schema:
            type: integer
        - name: page
          in: query
          required: false
          schema:
            type: integer
        - name: all
          in: query
          required: false
          description: 'Return all profiles'
          schema:
            type: boolean
        - name: orderBy
          in: query
          explode: true
          schema:
            type: object
            properties:
              column:
                type: string
                example: data_criacao
              direction:
                type: string
                enum: [asc, desc]
                example: desc
      responses:
        '200':
          description: Ok
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: New Profile
      tags:
        - Perfil de Consulta
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nome:
                  type: string
                  required: true
                tipo_perfil:
                  type: integer
                  required: true
                  minimum: 1
                objetivo:
                  type: string
                  required: false
                grupos:
                  type: array
                  required: true
                  items:
                    type: integer
                  description: Array with id of groups
                perfil:
                  type: array
                  required: true
                  items:
                    type: object
                    properties:
                      tipo_entrada:
                        type: string
                      captura:
                        type: string
                  example:
                    - tipo_entrada: "input"
                      captura: "receitaFederalPf"
                    - tipo_entrada: "input"
                      captura: "baseEmpresas"
      responses:
        '201':
          description: Created
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/{profileId}:
    put:
      summary: Update a Profile by ID
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nome:
                  type: string
                  required: true
                tipo_perfil:
                  type: integer
                  required: true
                  minimum: 1
                objetivo:
                  type: string
                  required: false
                grupos:
                  type: array
                  required: true
                  items:
                    type: integer
                  description: Array with id of groups
                perfil:
                  type: array
                  required: true
                  items:
                    type: object
                    properties:
                      tipo_entrada:
                        type: string
                      captura:
                        type: string
                  example:
                    - tipo_entrada: "input"
                      captura: "receitaFederalPf"
                    - tipo_entrada: "input"
                      captura: "baseEmpresas"
      responses:
        '200':
          description: Ok
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    delete:
      summary: Delete a Profile by ID
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/details:
    get:
      summary: Get Details from a Profile by ID
      tags:
        - Perfil de Consulta
      parameters:
        - name: idPerfil
          in: query
          required: true
          schema:
            type: integer
        - name: mode
          in: query
          required: true
          enum: [ custom, default ]
          example: custom / default
          schema:
            type: string
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/inactivate:
    post:
      summary: Inactivate profile by id
      tags:
        - Perfil de Consulta
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_perfil:
                  type: integer
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/duplicate:
    post:
      summary: Duplicate a profile by id
      tags:
        - Perfil de Consulta
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_perfil:
                  type: integer
                  required: true
                grupos:
                  type: array
                  required: true
                  items:
                    type: integer
                nome:
                  type: string
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/get-group-user-profile:
    get:
      summary: Get user groups
      tags:
        - Perfil de Consulta
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/export/excel/{profileId}:
    get:
      summary: Export profile in Excel
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/list/batch-monitoring/{profileId}:
    get:
      summary: Get batchs with monitoring by profile Id
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/sources:
    get:
      summary: Get all sources
      tags:
        - Perfil de Consulta
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/sources/available:
    get:
      summary: Get all sources available to use on profile
      tags:
        - Perfil de Consulta
      parameters:
        - name: type
          in: query
          example: pf
          required: false
          schema:
            type: string
        - name: nome_campo
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/sources/homonyms/{profileId}:
    get:
      summary: Get sources Ids which work with homonyms
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/sources/parameters/{type}:
    get:
      summary: Get sources parameters by type
      tags:
        - Perfil de Consulta
      parameters:
        - name: type
          in: path
          required: true
          enum: [pf, pj]
          example: pf / pj
          schema:
            type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/sources/{profileId}:
    get:
      summary: Get profile sources by profile Id
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/foxy-ia/{profileId}:
    get:
      summary: Get permissions to use ChatGPT (Foxy IA) by profile Id
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

    post:
      summary: Create new permission on profile to use ChatGPT (Foxy IA)
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                permissions:
                  type: array
                  items:
                    type: integer
                  required: true
      responses:
        '201':
          description: Created
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil/workflow/{profileId}:
    get:
      summary: Get configured worfklow by profile Id
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
        - name: id_grupo_consulta
          in: query
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

    post:
      summary: Create new workflow for profile
      tags:
        - Perfil de Consulta
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                aprovacao_automatica:
                  type: boolean
                parametros_aprovacao:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                      nome:
                        type: string
                      tipo:
                        type: string
                      filtro_dossie:
                        type: null  # A propriedade filtro_dossie é do tipo null
                      palavras:
                        type: array
                        items:
                          type: string
                      status:
                        type: string
                niveis_workflow:
                  type: array
                  items:
                    type: object
                    properties:
                      level:
                        type: string
                      users:
                        type: array
                        items:
                          type: object
                          properties:
                            id:
                              type: string
                            name:
                              type: string
                qt_niveis:
                  type: integer
                nome_workflow:
                  type: string
                id_grupo_consulta:
                  type: integer
      responses:
        '201':
          description: Created
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]


  /api/dossie/configuration/perfil-padrao/get/{profileId}:
    get:
      summary: Get Profile by ID
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
        - name: basic
          in: query
          required: true
          schema:
            type: boolean
          description: Returns basic data of profile
        - name: inativo
          in: query
          required: true
          schema:
            type: boolean
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao:
    get:
      summary: Get All Profiles
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: id_perfil
          in: query
          required: false
          schema:
            type: integer
        - name: id_fonte
          in: query
          required: false
          schema:
            type: integer
        - name: ativo
          in: query
          required: true
          schema:
            type: boolean
        - name: per_page
          in: query
          required: false
          schema:
            type: integer
        - name: page
          in: query
          required: false
          schema:
            type: integer
      responses:
        '200':
          description: Ok
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: New Profile
      tags:
        - Perfil de Consulta Padrão
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nome:
                  type: string
                  required: true
                tipo_perfil:
                  type: integer
                  required: true
                  minimum: 1
                objetivo:
                  type: string
                  required: false
                icon_path:
                  type: string
                  required: false
                perfil:
                  type: array
                  required: true
                  items:
                    type: object
                    properties:
                      tipo_entrada:
                        type: string
                      captura:
                        type: string
                  example:
                    - tipo_entrada: "input"
                      captura: "receitaFederalPf"
                    - tipo_entrada: "input"
                      captura: "baseEmpresas"
      responses:
        '201':
          description: Created
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao/{profileId}:
    put:
      summary: Update a Profile by ID
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                nome:
                  type: string
                  required: true
                tipo_perfil:
                  type: integer
                  required: true
                  minimum: 1
                objetivo:
                  type: string
                  required: false
                icon_path:
                  type: string
                  required: false
                perfil:
                  type: array
                  required: true
                  items:
                    type: object
                    properties:
                      tipo_entrada:
                        type: string
                      captura:
                        type: string
                  example:
                    - tipo_entrada: "input"
                      captura: "receitaFederalPf"
                    - tipo_entrada: "input"
                      captura: "baseEmpresas"
      responses:
        '200':
          description: Ok
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    delete:
      summary: Delete a Profile by ID
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
        - name: obs
          in: query
          required: true
          description: "Observation (minimal 15 characters)"
          schema:
            type: string
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao/inactivate:
    post:
      summary: Inactivate profile by id
      tags:
        - Perfil de Consulta Padrão
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_perfil:
                  type: integer
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao/duplicate:
    post:
      summary: Duplicate a profile by id
      tags:
        - Perfil de Consulta Padrão
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                id_perfil:
                  type: integer
                  required: true
                nome:
                  type: string
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao/export/excel/{profileId}:
    get:
      summary: Export profile in Excel
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao/sources/{profileId}:
    get:
      summary: Get profile sources by profile Id
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/perfil-padrao/foxy-ia/{profileId}:
    get:
      summary: Get permissions to use ChatGPT (Foxy IA) by profile Id
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Model Not found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

    post:
      summary: Create new permission on profile to use ChatGPT (Foxy IA)
      tags:
        - Perfil de Consulta Padrão
      parameters:
        - name: profileId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                permissions:
                  type: array
                  items:
                    type: integer
                  required: true
      responses:
        '201':
          description: Created
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/lote/dossie:
    post:
      summary: Create new lote/dossie
      tags:
        - Criar Dossie
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                criterios:
                  type: array
                  items:
                    type: object
                    properties:
                      value:
                        type: string
                    required:
                      - value
                ignoreDuplicityBlock:
                  type: boolean
                tipo:
                  type: integer
                perfil:
                  type: integer
                parametros:
                  type: object
                  minProperties: 0
                  properties:
                    MinisterioTurismoCadastur:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            uf:
                              type: array
                              items:
                                type: integer
                    kurrier:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            justica:
                              type: array
                              items:
                                type: integer
                            partes:
                              type: array
                              items:
                                type: integer
                            uf:
                              type: array
                              items:
                                type: integer
                    ppeTitularPaga:
                      type: object
                      properties:
                        parametros:
                          type: string
                          enum:
                            - N
                            - S
                    ppeTitularPrivada:
                      type: object
                      properties:
                        parametros:
                          type: string
                          enum:
                            - N
                            - S
                    BaseOffEscavador:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tribunais:
                              type: array
                              items:
                                type: integer
                            uf:
                              type: array
                              items:
                                type: integer
                            similaridade:
                              type: integer
                        entradas:
                          type: object
                          properties:
                            assunto:
                              type: string
                    BaseOffEscavadorV2:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tribunais:
                              type: array
                              items:
                                type: integer
                            uf:
                              type: array
                              items:
                                type: integer
                            similaridade:
                              type: integer
                        entradas:
                          type: object
                          properties:
                            assunto:
                              type: string
                    diariosOficiais:
                      type: object
                      properties:
                        parametros:
                          type: array
                        palavras:
                          type: string
                        auto_relevante:
                          type: boolean
                    diariosOficiaisPaga:
                      type: object
                      properties:
                        parametros:
                          type: array
                        palavras:
                          type: string
                        auto_relevante:
                          type: boolean
                    mpfRelevancia:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            auto_relevante:
                              type: boolean
                    cade:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            pesquisa:
                              type: array
                            target:
                              type: integer
                              enum: [ 0, 1 ]
                        auto_relevante:
                          type: boolean
                    googleGlobal:
                      type: object
                      properties:
                        parametros_entradas:
                          type: object
                          additionalProperties:
                            type: object
                            properties:
                              informacoes_adicionais:
                                type: string
                            example:
                              criteria: {
                                informacoes_adicionais : text
                              }
                        informacoes_adicionais_api:
                          type: string
                        auto_relevante:
                          type: boolean
                    googleGlobalNotSpecific:
                      type: object
                      properties:
                        parametros_entradas:
                          type: object
                          additionalProperties:
                            type: object
                            properties:
                              informacoes_adicionais:
                                type: string
                            example:
                              criteria: {
                                informacoes_adicionais : text
                              }
                        informacoes_adicionais_api:
                          type: string
                        auto_relevante:
                          type: boolean
                    spcLocalizaMix:
                      type: object
                      properties:
                        parametros:
                          type: array
                    CertidaoAcoesExecucoesCiveisCriminaisTrf:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tipo_certidao:
                              type: string
                    CertidaoCeatTrtCampinasRegiao:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            processos_arquivados:
                              type: string
                    CertidaoCgu:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tipo_certidao:
                              type: string
                    CertidaoDistribuicaoAcoesCiveisFiscaisCriminaisTrf:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tipo_certidao:
                              type: string
                    CertidaoJudicialTrf4:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tipo_certidao:
                              type: string
                    Comprot:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            data_inicial:
                              type: string
                              format: d/m/Y
                          additionalProperties: true
                    TcuCertidaoContasIrregulares:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            tipo_certidao:
                              type: string
                    bancoCentralCrsfnEmentasAcordaos:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            exato:
                              type: boolean
                    ProcessosJuridicos:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            auto_relevante:
                              type: boolean
                    FontesJuridicas:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            auto_relevante:
                              type: boolean
                    Djen:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            auto_relevante:
                              type: boolean
                    TjPrAntecedentesEleitorais:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            name:
                              type: string
                              required: true
                            motherName:
                              type: string
                              required: true
                    consultaSocio:
                      type: object
                      properties:
                        parametros:
                          type: object
                          properties:
                            similaridade:
                              type: integer
                              minimum: 1
                              maximum: 100
                tag:
                  type: integer
                mode:
                  type: string
                  enum:
                    - custom
                    - default
                process:
                  type: boolean
                  description: created and process (true) or only created(false)
              required:
                - criterios
                - ignoreDuplicityBlock
                - tipo
                - perfil
                - parametros
                - mode
                - process

      responses:
        '200':
          description: Created
          content:
            application/json:
              schema:
                type: object
                properties:
                  qtd:
                    type: integer
                    description: quantity created
                  dossies:
                    type: array
                    items:
                      type: object
                      properties:
                        dossieId:
                          type: integer
                          description: dossier ID
                        loteId:
                          type: integer
                          description: batch ID
                        criteria:
                          type: string
                          description: researched criteria
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '404':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message

        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  
  /api/lote/extra-dossiers-generated:
    post:
      summary: Amount extra dossiers generated
      tags:
        - Criar Dossie
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                criterios:
                  type: array
                  items:
                    type: string
                perfil:
                  type: integer
                mode:
                  type: string
                  enum:
                    - custom
                    - default
              required:
                - criterios
                - perfil
                - mode

      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  extra:
                    type: integer
                    description: quantity will create
        '422':
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                  message:
                    type: string
                required:
                  - code
                  - message
        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  
  /api/lote/perfil/list:
    get:
      summary: List profile
      tags:
        - Criar Dossie
      parameters:
        - name: tipo
          in: query
          description: define tipo
          required: true
          schema:
            type: integer
            enum:
              - 1
              - 2
              - 3
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "success"
                  code:
                    type: integer
                    example: 200
                  data:
                    type: object
                    properties:
                      personalizada:
                        type: array
                        description: lista de perfil do cliente
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: profile ID
                            nome:
                              type: string
                              description: profile name
                            icon:
                              type: string
                              nullable: true
                            versao:
                              type: integer
                            tipo_pessoa:
                              type: integer
                      default:
                        type: array
                        description: lista de perfil do cliente
                        items:
                          type: object
                          properties:
                            id:
                              type: integer
                              description: profile ID
                            nome:
                              type: string
                              description: profile name
                            icon:
                              type: string
                              nullable: true
                            versao:
                              type: integer
                            tipo_pessoa:
                              type: integer    
        '500':
          description: Internal Server Error
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/lote-duplicado:
    post:
      summary: Duplicar lote
      tags:
        - Criar Dossie
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                criterios:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              examples:
                apenasDuplicidade:
                  summary: Apenas duplicidade
                  value:
                    status: 200
                    data: {
                        "dias_duplicidade": 120,
                        "total": 20,
                        "dossies": [
                          {
                            "tag_id": 1,
                            "tag": {
                                "id": 1,
                                "name": "teste",
                                "description": null,
                                "color": "#E8D1FE",
                                "font_color": "#121212",
                                "created_at": "2024-08-07T20:48:48.000000Z",
                                "updated_at": "2024-08-07T20:48:48.000000Z",
                                "id_grupo": null
                            },
                            "id_dossie": 1,
                            "criterio": "criterio",
                            "data": "09/05/2025",
                            "estado": "4",
                            "loteType": 1,
                            "username": "Usuario",
                            "groupName": "Grupo"
                         }
                        ],
                        "upflags": []
                    }
                apenasFlag:
                  summary: Apenas flag
                  value:
                    status: 200
                    data: {
                        "dias_duplicidade": 120,
                        "total": 20,
                        "dossies": [],
                        "upflags": [
                          {
                            "id": 1,
                            "documento": "criterio",
                            "motivo": "motivo",
                            "id_flag_list": 1,
                            "created_at": "2022-08-24 11:12:03",
                            "get_list": [
                                {
                                    "id": 1,
                                    "nome": "nome"
                                }
                            ]
                          }
                        ]
                    }
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
                                
  /api/lote/perfil/details:
    get:
      summary: profile details
      tags:
        - Criar Dossie
      parameters:
        - name: idPerfil
          in: query
          description: profile ID
          required: true
          schema:
            type: integer
        - name: mode
          in: query
          description: define se é do cliente (custom) or do dossie (default)
          required: true
          schema:
            type: string
            enum:
              - custom
              - default
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: profile ID
                  name:
                    type: string
                    description: profile name
                  version:
                    type: integer
                  objective:
                    type: string
                    nullable: true
                  icon:
                    type: string
                    nullable: true
                  sources:
                    type: array
                    description: lista de perfil do cliente
                    items:
                      type: object
                      properties:
                        sourceName:
                          type: string
                        sourceType:
                          type: integer
                        sourceMethod:
                          type: string
                        sourceTypePeople:
                          type: integer
                          description: type input source 1(pf), 2(pj), 3 (pf|pj)
                        categoryAlias:
                          type: string
                          description: alias source category
                  groups:
                    type: array
                    description: lista de perfil do cliente
                    items:
                      type: object
                      properties:
                        groupId:
                          type: integer
                          description: group ID
                        groupName:
                          type: string
                          description: group name
                  hasUpscore:
                    type: boolean
                    description: exist upscore
                  hasWorkflow:
                    type: boolean
                    description: exist workflow
                  hasFoxy:
                    type: boolean
                    description: exist foxy
        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/listagem-perfis:
    get:
      summary: list sources of profile with parametros entradas
      tags:
        - Criar Dossie
      parameters:
        - name: type_profile
          in: query
          description: profile type
          required: true
          schema:
            type: string
            enum:
              - pj
              - pf
        - name: profile
          in: query
          description: profile ID
          required: true
          schema:
            type: integer
        - name: tipo
          in: query
          description: define se é do cliente (custom) or do dossie (default)
          required: true
          schema:
            type: string
            enum:
              - custom
              - default
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  saidas:
                    type: array
                    description: list sources of profile
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          description: source ID
                        nome:
                          type: string
                          description: source name
                        captura:
                          type: string
                          description: source method
                        tipo_pessoa:
                          type: integer
                          enum:
                            - 1
                            - 2
                            - 3
                          description: type input source 1(cpf)2(cnpj)3(cpf|cnpj|nome)
                        campo:
                          type: string
                          description: index in source result
                  tipo_pessoa:
                    type: integer
                    description: profile type
                    enum:
                      - 1
                      - 2
        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/valida/criterio:
    post:
      summary: validate crierion in file
      tags:
        - Criar Dossie
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                arquivo:
                  type: string
                  format: binary
                  description: file with crierion, type in txt,xls,csv
                type:
                  type: string
                  enum:
                    - pf
                    - pj
                  description: input type

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  criterios:
                    type: array
                    items:
                      type: object
                      properties:
                        value:
                          type: string
                        status:
                          type: integer
                        dtNas:
                          type: object
                          properties:
                            dtNas:
                              type: string
                            status:
                              type: integer
                        motherName:
                          type: string
                        drive:
                          type: string
                        city:
                          type: string
                        name:
                          type: string
                        rg:
                          type: string
                        fatherName:
                          type: string
                      required:
                        - value
        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/parameters/pre-parameters/source:
    post:
      summary: list pre-parameters of the source
      tags:
        - Criar Dossie
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                captura:
                  type: string
                  description: method of source

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  user:
                    type: array
                    items:
                      type: object
                      properties:
                        nome:
                          type: string
                        id:
                          type: integer
                  groupUser:
                    type: array
                    items:
                      type: object
                      properties:
                        nome:
                          type: string
                        id:
                          type: integer

        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/parameters/pre-parameters/{preParameterID}:
    get:
      summary: Get pre-parameter of the source
      tags:
        - Criar Dossie
      parameters:
        - name: preParameterID
          in: query
          description: pre-parameter ID
          schema:
            type: integer
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                  nome:
                    type: string
                  parametros:
                    type: object
                    additionalProperties: true

        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
    delete:
      summary: delete um pre-parameter of the source
      tags:
        - Criar Dossie
      parameters:
        - name: preParameterID
          in: query
          description: pre-parameter ID
          schema:
            type: integer
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: boolean
        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/parameters/pre-parameters:
    post:
      summary: create pre-parameter of the source
      tags:
        - Criar Dossie
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                captura:
                  type: string
                  description: method of source
                nome:
                  type: string
                  maximum: 30
                values:
                  type: array

      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: integer
                    description: ID of new pre-parameter

        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]
  /api/lote/parameters/{method}:
    get:
      summary: Get source parameters
      tags:
        - Criar Dossie
      parameters:
        - name: method
          in: query
          description: method of source
          schema:
            type: string
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                type: object
                properties:
                  parametro:
                    type: array
                    description: source parameter
        '500':
          description: Internal Server Error
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
        '422':
          type: object
          properties:
            code:
              type: integer
            message:
              type: string
          required:
            - code
            - message
      security:
        - auth: [ ]

  /api/dossie/resultado/cards/{dossieID}/info:
    get:
      summary: Get details Dossie
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/resultado/cards/{dossieID}/without-result-sources:
    get:
      summary: Get without  result sources
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  amount:
                    type: integer
                    description: quantity sources
                  fontes:
                    type: array
                    items:
                      type: string
      security:
        - auth: [ ]
  /api/dossie/resultado/cards/{dossieID}/with-result-sources:
    get:
      summary: Get with  result sources
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  amount:
                    type: integer
                    description: quantity sources
                  fontes:
                    type: array
                    items:
                      type: string
      security:
        - auth: [ ]
  /api/dossie/resultado/source-group/{dossieID}:
    get:
      summary: Get source group of Dossie
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
      security:
        - auth: [ ]
  /api/dossie/resultado/search-other-apps/{dossieID}:
    get:
      summary: Get liks of search other apps
      tags:
        - Resultado
      parameters:
        - name: dossieID
          in: path
          description: ID of the dossie
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: object
                properties:
                  apps:
                    type: object
                    additionalProperties:
                      type: object
                      properties:
                        name:
                          type: string
                        link:
                          type: string
      security:
        - auth: [ ]
  /api/dossie/resultado/reactions:
    get:
      summary: Get reactions
      tags:
        - Resultado
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string

      security:
        - auth: [ ]
  /api/user/group-users:
    get:
      summary: Get list users group (use coments)
      tags:
        - Resultado
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    userAlias:
                      type: string
                    userImgOrIcon:
                      type: string
                    userName:
                      type: string
      security:
        - auth: [ ]

  /api/dossie/configuration/duplicity-alert:
    get:
      summary: Get the duplicity alert (in days) for the company
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    put:
      summary: Update days of duplicity alert for the company
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                dias_aviso_duplicidade:
                  type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
  /api/dossie/configuration/siglas-exclusion:
    get:
      summary: Get acronyms (siglas) of user company
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    put:
      summary: Update acronyms (siglas) of user company
      tags:
        - Configuração de Dossiê
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                siglas:
                  type: array
                  required: true
                  items:
                    type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google:
    get:
      summary: Get all google profiles paginated
      tags:
        - Configuração de Dossiê
      parameters:
        - name: perfil
          in: query
          description: "Profile name"
          schema:
            type: string
        - name: per_page
          in: query
          description: "Results per page"
          schema:
            type: integer
        - name: page
          in: query
          description: "Page"
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
  /api/dossie/configuration/media/google/all:
    get:
      summary: Get all google profiles
      tags:
        - Configuração de Dossiê
      parameters:
        - name: perfil
          in: query
          description: "Profile name"
          schema:
            type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/replicate:
    post:
      summary: Replicate configuration from profile to another
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id_perfil:
                  type: integer
                  required: true
                id_perfis_selecionados:
                  type: array
                  description: "Array of ids of profiles which will receive the configuration from id_perfil"
                  items:
                    type: integer
                  required: true
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/perfil/{idPerfil}:
    parameters:
      - name: idPerfil
        in: path
        required: true
        description: "ID Profile"
        schema:
          type: integer
    get:
      summary: Get profile with google by id
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: Save Google configuration to a profile default
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                params:
                  type: array
                  items:
                    type: object
                    properties:
                      pais:
                        type: string
                        required: true
                      lista_palavras:
                        type: array
                        items:
                          type: string
                      lista_inclusao:
                        type: array
                        items:
                          type: string
                      lsta_exclusao:
                        type: array
                        items:
                          type: string
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/perfil/{idPerfil}/delete-country:
    delete:
      summary: Delete countries from google configuration by profile id
      tags:
        - Configuração de Dossiê
      parameters:
        - name: idPerfil
          in: path
          required: true
          description: "ID Profile"
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                paises:
                  type: array
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/perfil-padrao:
    get:
      summary: Get all google profiles default paginated
      tags:
        - Configuração de Dossiê
      parameters:
        - name: perfil
          in: query
          description: "Profile name"
          schema:
            type: string
        - name: per_page
          in: query
          description: "Results per page"
          schema:
            type: integer
        - name: page
          in: query
          description: "Page"
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
  /api/dossie/configuration/media/google/perfil-padrao/all:
    get:
      summary: Get all google profiles default
      tags:
        - Configuração de Dossiê
      parameters:
        - name: perfil
          in: query
          description: "Profile name"
          schema:
            type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/perfil-padrao/replicate:
    post:
      summary: Replicate configuration from profile default to another
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id_perfil:
                  type: integer
                  required: true
                id_perfis_selecionados:
                  type: array
                  description: "Array of ids of profiles which will receive the configuration from id_perfil"
                  items:
                    type: integer
                  required: true
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/perfil-padrao/{idPerfil}:
    parameters:
      - name: idPerfil
        in: path
        required: true
        description: "ID Profile"
        schema:
          type: integer
    get:
      summary: Get profile default with google by id
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: Save Google configuration to a profile
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                params:
                  type: array
                  items:
                    type: object
                    properties:
                      pais:
                        type: string
                        required: true
                      lista_palavras:
                        type: array
                        items:
                          type: string
                      lista_inclusao:
                        type: array
                        items:
                          type: string
                      lsta_exclusao:
                        type: array
                        items:
                          type: string
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/google/perfil-padrao/{idPerfil}/delete-country:
    delete:
      summary: Delete country from google configuration by profile default id
      tags:
        - Configuração de Dossiê
      parameters:
        - name: idPerfil
          in: path
          required: true
          description: "ID Profile"
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                pais:
                  type: string
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/bing:
    get:
      summary: Get all bing configurations paginated
      tags:
        - Configuração de Dossiê
      parameters:
        - name: nome
          in: query
          description: "Bing list name"
          schema:
            type: string
        - name: per_page
          in: query
          description: "Results per page"
          schema:
            type: integer
        - name: page
          in: query
          description: "Page"
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: Create new configuration list for bing
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id_grupo:
                  type: integer
                  description: "User group ID"
                  required: true
                search_code:
                  type: integer
                  description: "Bing search code: 1 = All / 6 = Bing Brasil / 7 = Buscador Bing"
                  required: true
                tipo:
                  type: integer
                  description: "Type of configuration: 1 = Exclusion / 2 = Inclusion / 3 = Keywords"
                nome:
                  type: string
                  description: "Name of list"
                lista:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/bing/lista-palavras/{idLista}/status:
    put:
      summary: Update status from keywords configuration list
      tags:
        - Configuração de Dossiê
      parameters:
        - name: idLista
          in: path
          required: true
          description: "ID List"
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: boolean
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/bing/lista-exclusao/{idLista}/status:
    put:
      summary: Update status from exclusion/inclusion configuration list
      tags:
        - Configuração de Dossiê
      parameters:
        - name: idLista
          in: path
          required: true
          description: "ID List"
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  type: boolean
                  required: true
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/bing/options/get:
    get:
      summary: Get all user groups from company and all bing searchers available for configuration
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/media/bing/{idLista}:
    get:
      summary: Get configuration by id list
      tags:
        - Configuração de Dossiê
      parameters:
        - name: idLista
          in: path
          required: true
          description: "ID List"
          schema:
            type: integer
        - name: tipo
          in: query
          required: true
          description: "Type of configuration: 1 = Exclusion / 2 = Inclusion / 3 = Keywords"
          schema:
            type: integer
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    put:
      summary: Update configuration list for bing
      tags:
        - Configuração de Dossiê
      parameters:
        - name: idLista
          in: path
          required: true
          description: "ID List"
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id_grupo:
                  type: integer
                  description: "User group ID"
                  required: true
                search_code:
                  type: integer
                  description: "Bing search code: 1 = All / 6 = Bing Brasil / 7 = Buscador Bing"
                  required: true
                tipo:
                  type: integer
                  description: "Type of configuration: 1 = Exclusion / 2 = Inclusion / 3 = Keywords"
                nome:
                  type: string
                  description: "Name of list"
                lista:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: OK
        '404':
          description: Not Found
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/custom-files:
    get:
      summary: Get custom files configurations
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    put:
      summary: Update custom files configurations
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                automatico:
                  type: boolean
                  description: "If custom files batches/dossiers must be processed automatically"
                  required: true
                colunaDtNascimento:
                  type: integer
                  description: "Column which is the birth date"
                  required: true
                coluna:
                  type: integer
                  description: "Criterion Column"
                saidas:
                  type: array
                  descriptions: "Fields which the file will have"
                  example: ["originalData", "duplicityDate", "statusWorkflow", "sourcesData", "qtdHomonimos", "statusWorkflowDetalhesFonte"]
                  items:
                    type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/api/gupy:
    get:
      summary: Get API Gupy configuration
      tags:
        - Configuração de Dossiê
      parameters:
        - name: perfil
          in: query
          description: "Profile name"
          schema:
            type: string
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: Create API Gupy configuration
      tags:
        - Configuração de Dossiê
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                perfil_id:
                  type: integer
                  description: "Profile ID"
                  required: true
                token:
                  type: string
                  required: true
                usuario_id_to_create_dossie:
                  type: integer
                  description: "User ID"
      responses:
        '201':
          description: Created
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]

  /api/dossie/configuration/pdf:
    get:
      summary: Get PDF Company Configuration
      tags:
        - Configuração de Dossiê
      responses:
        '200':
          description: OK
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
    post:
      summary: Update PDF Company configuration
      tags:
        - Configuração de Dossiê
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                tituloPdf:
                  type: string
                rodapePdf:
                  type: string
                logo:
                  type: string
                  format: binary
                deletar:
                  type: boolean
                  description: Delete previous logo
                idLogo:
                  type: integer
                  description: Logo ID to be deleted (if "deletar" is true)
      responses:
        '201':
          description: Created
        '500':
          description: Internal Server Error
      security:
        - auth: [ ]
