# System Application Profile - upMiner API

## System Overview

### System Information
- **Op. Project Code (IRP)**: DOSSIE-API
- **System Name**: upMiner API
- **System Description**: API que gerencia autenticação, dossiês e backoffice da upLexis, permitindo monitoramento e geração automática de dossiês de acordo com a recorrência desejada.

### System Ownership
- **Division/RHQ**: upLexis
- **Ownership Dept. Name**: Service S/W Lab
- **Person In Charge(SFO)**: Equipe de Desenvolvimento upLexis

### System Operation(IT)
- **IT Service Provider**: AWS
- **Operation Dept. Name**: DX IT Operation Group
- **Operation PIC 1**: Equipe DevOps upLexis
- **Operation PIC 2**: Suporte Técnico upLexis

## Business Information

### System Importance
- **B2B/B2C/B2E**: B2B (Business to Business)

### Type
- **Description**: Business Intelligence Dashboard para monitoramento e geração de dossiês

### Area of use
- **General**: ✓
- **Sales**: ✓
- **Marketing**: ✓
- **HR**: ✓
- **Legal**: ✓
- **Finance**: ✓

### Process Level 1
- **General**: Monitoramento de Dossiês

### Function Level 2
- **Legal Mgmt.**: ✓

### Function Level 3
- **Compliance Risk Mgmt.**: ✓

## Technical Information

### Cloud
- **Type**: IaaS
- **Provider**: AWS
- **Server Location**: US-East-1
- **Application time**: 2018 (baseado nas informações encontradas)

### Development standard
- **Framework**: Laravel 10.0
- **WEB (HTML 5)**: Y
- **Cross browsing**: Y
- **Mobile Possibility**: Y

### Middle Ware
- **Type**:
  - WEB: ✓
  - WAS: ✓
  - DB: ✓
- **SW**:
  - WEB: Apache
  - WAS: Laravel/PHP 8.2
  - DB: PostgreSQL
- **SW Provider**:
  - WEB: Apache
  - WAS: Laravel/PHP
  - DB: PostgreSQL
- **Version**:
  - WEB: 2.4.6
  - WAS: 10.0
  - DB: 14.0
- **Personal information**: Y

## Detalhes Adicionais

### Ambientes
- **QA**: https://monitoria.qa.uplexis.com.br
- **PROD**: https://monitoria.uplexis.com

### Integrações
- AWS Lambda
- AWS S3
- AWS SQS
- AWS DynamoDB
- Elasticsearch
- Redis

### Funcionalidades Principais
- Autenticação de usuários
- Geração automática de dossiês
- Monitoramento de mudanças entre dossiês
- Exportação de relatórios em PDF
- Integração com serviços externos (Gupy, Foxy.IA)
- Análise de compliance e risco

### Requisitos Técnicos
- PHP 8.2+
- Composer
- Node.js 19+
- npm
- PostgreSQL 14
- Redis
- Docker (para ambiente de desenvolvimento)
