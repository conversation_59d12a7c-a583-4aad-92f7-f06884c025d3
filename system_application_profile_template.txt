System Application Profile

- System Overview
  - System Information
    Op. Project Code (IRP): DOSSIE-API
    System Name: upMiner API
    System Description: API que gerencia autenticação, dossiês e backoffice da upLexis, permitindo monitoramento e geração automática de dossiês de acordo com a recorrência desejada.

  - System Ownership
    Division/RHQ: upLexis
    Ownership Dept. Name: Service S/W Lab
    Person In Charge(SFO): Equipe de Desenvolvimento upLexis

  - System Operation(IT)
    IT Service Provider: AWS
    Operation Dept. Name: DX IT Operation Group
    Operation PIC 1: Equipe DevOps upLexis
    Operation PIC 2: Suporte Técnico upLexis

- Business Information
  - System Importance: B2B (Business to Business)
  - Type: Business Intelligence Dashboard para monitoramento e geração de dossiês
  - Description: Sistema para geração e monitoramento de dossiês automatizados
  - Area of use: General, Sales, Marketing, HR, Legal, Finance
  - Process Level 1: Monitoramento de Dossiês
  - Function Level 2: Legal Mgmt.
  - Function Level 3: Compliance Risk Mgmt.

- Technical Information
  - Cloud
    Type: IaaS
    Provider: AWS
    Server Location: US-East-1
    Application time: 2018

  - Development standard
    Framework: Laravel 10.0
    WEB (HTML 5): Y
    Cross browsing: Y
    Mobile Possibility: Y

  - Middle Ware
    Type: WEB/WAS/DB
    SW: Apache/Laravel/PostgreSQL
    SW Provider: Apache/Laravel/PostgreSQL
    Version: 2.4.6/10.0/14.0
    Personal information: Y
