stages:
  - sonarqube-check
  - ecr-login
  - pre_publish
  - publish
  - deploy
  - qa_reset

variables:
  AURORA_HOST: AURORA_HOST_${CI_ENVIRONMENT_NAME}
  AURORA_PORT: AURORA_PORT_${CI_ENVIRONMENT_NAME}
  AURORA_DATABASE: AURORA_DATABASE_${CI_ENVIRONMENT_NAME}
  AURORA_USERNAME: AURORA_USERNAME_${CI_ENVIRONMENT_NAME}
  AURORA_PASSWORD: AURORA_PASSWORD_${CI_ENVIRONMENT_NAME}
  SONAR_HOST_URL: ${SONAR_HOST_URL}
  SONAR_TOKEN: ${SONAR_TOKEN}
  SONAR_PROJECT_KEY: ${SONAR_PROJECT_KEY}
  AWS_SES_KEY: SES_KEY_${CI_ENVIRONMENT_NAME}
  AWS_SES_SECRET: SES_SECRET_${CI_ENVIRONMENT_NAME}

include:
  - project: 'aplicativo/ci_templates'
    ref: 'main'
    file: 'main-ecr.yml'

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
  - |
    sonar-scanner \
      -Dsonar.projectKey="${SONAR_PROJECT_KEY}" \
      -Dsonar.qualitygate.wait=true \
      -Dsonar.gitlab.project_id="${CI_PROJECT_ID}" \
      -Dsonar.host.url="${SONAR_HOST_URL}" \
      -Dsonar.login="${SONAR_TOKEN}"
  allow_failure: true
  rules:
    # this pipeline should run on merge request to develop branch only
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == 'develop'


login ecr on qa:
  extends: .ecr-login
  only:
    - qa
  environment:
    name: qa

login ecr on homolog:
  extends: .ecr-login
  only:
    - develop
  environment:
    name: hml

login ecr on production:
  extends: .ecr-login
  only:
    - master
  environment:
    name: prod

prepublish qa 1:
  extends: ".pre_publish 1/3"
  needs: ["login ecr on qa"]
  only:
    - qa
  environment:
    name: qa

prepublish homolog 1:
  extends: ".pre_publish 1/3"
  needs: ["login ecr on homolog"]
  only:
    - develop
  environment:
    name: hml

prepublish production 1:
  extends: ".pre_publish 1/3"
  needs: ["login ecr on production"]
  only:
    - master
  environment:
    name: prod

prepublish qa 2:
  extends: ".pre_publish 2/3"
  needs: ["login ecr on qa"]
  only:
    - qa
  environment:
    name: qa

prepublish homolog 2:
  extends: ".pre_publish 2/3"
  needs: ["login ecr on homolog"]
  only:
    - develop
  environment:
    name: hml

prepublish production 2:
  extends: ".pre_publish 2/3"
  needs: ["login ecr on production"]
  only:
    - master
  environment:
    name: prod

prepublish qa 3:
  extends: ".pre_publish 3/3"
  needs: ["login ecr on qa"]
  only:
    - qa
  environment:
    name: qa

prepublish homolog 3:
  extends: ".pre_publish 3/3"
  needs: ["login ecr on homolog"]
  only:
    - develop
  environment:
    name: hml

prepublish production 3:
  extends: ".pre_publish 3/3"
  needs: ["login ecr on production"]
  only:
    - master
  environment:
    name: prod

publish image to qa:
  extends: .publish
  needs: ["prepublish qa 1", "prepublish qa 2", "prepublish qa 3"]
  only:
    - qa
  environment:
    name: qa

publish image to homolog:
  extends: .publish
  needs: ["prepublish homolog 1", "prepublish homolog 2", "prepublish homolog 3"]
  only:
    - develop
  environment:
    name: hml

publish image to production:
  extends: .publish
  needs: ["prepublish production 1", "prepublish production 2", "prepublish production 3"]
  only:
    - master
  environment:
    name: prod

deploy to qa:
  extends: .deploy
  needs: ["publish image to qa"]
  only:
    - qa
  environment:
      name: qa

deploy to homolog:
  extends: .deploy
  needs: ["publish image to homolog"]
  only:
    - develop
  environment:
    name: hml

deploy to production:
  extends: .deploy
  needs: ["publish image to production"]
  only:
    - master
  environment:
      name: prod
