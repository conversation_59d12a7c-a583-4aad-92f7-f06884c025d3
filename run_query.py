#!/usr/bin/env python3
"""
Script para executar consultas SQL personalizadas no banco de dados PostgreSQL
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import argparse
import json

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()

def connect_to_db():
    """Conecta ao banco de dados PostgreSQL"""
    try:
        # Obtém parâmetros de conexão das variáveis de ambiente
        host = os.getenv('UPMINER_HOST', 'localhost')
        port = os.getenv('UPMINER_PORT', '5432')
        database = os.getenv('UPMINER_DATABASE', 'upminerdossie')
        user = os.getenv('UPMINER_USERNAME', 'postgres')
        password = os.getenv('UPMINER_PASSWORD', '')
        
        # Conecta ao banco de dados
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password
        )
        
        return conn
    except Exception as e:
        print(f"Erro ao conectar ao banco de dados: {e}")
        sys.exit(1)

def execute_query(conn, query, params=None, format_output='table'):
    """Executa uma consulta SQL e retorna os resultados"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(query, params or {})
            
            # Tenta obter resultados (pode não haver resultados para comandos como INSERT, UPDATE, etc.)
            try:
                results = cursor.fetchall()
                
                if not results:
                    print("A consulta foi executada com sucesso, mas não retornou resultados.")
                    return
                
                # Converte os resultados para uma lista de dicionários
                results_list = [dict(row) for row in results]
                
                # Formata a saída de acordo com o formato especificado
                if format_output == 'json':
                    print(json.dumps(results_list, indent=2, default=str))
                elif format_output == 'csv':
                    # Imprime cabeçalhos
                    headers = results_list[0].keys()
                    print(','.join(headers))
                    
                    # Imprime dados
                    for row in results_list:
                        values = [str(value).replace(',', '\\,') for value in row.values()]
                        print(','.join(values))
                else:  # format_output == 'table'
                    # Imprime cabeçalhos
                    headers = results_list[0].keys()
                    header_widths = {header: max(len(header), max([len(str(row[header])) for row in results_list])) for header in headers}
                    
                    for header in headers:
                        print(f"{header:{header_widths[header]}} ", end="")
                    print("\n" + "-" * (sum(header_widths.values()) + len(headers)))
                    
                    # Imprime dados
                    for row in results_list:
                        for header in headers:
                            print(f"{str(row[header]):{header_widths[header]}} ", end="")
                        print()
                
                print(f"\n{len(results_list)} registros encontrados.")
            except psycopg2.ProgrammingError:
                # Não há resultados para retornar (ex: INSERT, UPDATE, etc.)
                print(f"Comando executado com sucesso. Linhas afetadas: {cursor.rowcount}")
    except Exception as e:
        print(f"Erro ao executar consulta: {e}")

def main():
    parser = argparse.ArgumentParser(description='Executar consultas SQL no PostgreSQL')
    parser.add_argument('--query', help='Consulta SQL a ser executada')
    parser.add_argument('--file', help='Arquivo contendo a consulta SQL')
    parser.add_argument('--format', choices=['table', 'json', 'csv'], default='table', 
                        help='Formato de saída (padrão: table)')
    args = parser.parse_args()
    
    # Verifica se foi fornecida uma consulta ou um arquivo
    if not args.query and not args.file:
        parser.error("É necessário fornecer uma consulta (--query) ou um arquivo (--file)")
    
    # Se for fornecido um arquivo, lê a consulta do arquivo
    if args.file:
        try:
            with open(args.file, 'r') as f:
                query = f.read()
        except Exception as e:
            print(f"Erro ao ler arquivo: {e}")
            sys.exit(1)
    else:
        query = args.query
    
    # Conecta ao banco de dados
    conn = connect_to_db()
    
    try:
        # Executa a consulta
        execute_query(conn, query, format_output=args.format)
    finally:
        # Fecha a conexão
        conn.close()

if __name__ == "__main__":
    main()
