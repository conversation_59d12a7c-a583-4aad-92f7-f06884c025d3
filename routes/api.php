<?php

use App\Actions\Autocomplete\AutocompleteClient;
use App\Http\Controllers\ArquivosPersonalizadosController;
use App\Http\Controllers\CommentReactionController;
use App\Http\Controllers\ConfiguracaoApiGupyController;
use App\Http\Controllers\ConfiguracaoBingController;
use App\Http\Controllers\ConfiguracaoDossieController;
use App\Http\Controllers\ConfiguracaoGoogleController;
use App\Http\Controllers\ConfiguracaoGooglePerfilPadraoController;
use App\Http\Controllers\ConvertCapturasToNewDossie;
use App\Http\Controllers\DossieAnalysisController;
use App\Http\Controllers\DossieCommentConfigController;
use App\Http\Controllers\DossieCommentMarkedUsersController;
use App\Http\Controllers\DossieCommentsController;
use App\Http\Controllers\DossieController;
use App\Http\Controllers\DossieReactionCategoryController;
use App\Http\Controllers\DossieReactionFonteController;
use App\Http\Controllers\DossieSummaryCompanyController;
use App\Http\Controllers\DossieTagsController;
use App\Http\Controllers\DossieUpflagController;
use App\Http\Controllers\DossieUpscoreController;
use App\Http\Controllers\DossieWorkflowController;
use App\Http\Controllers\HomonymsController;
use App\Http\Controllers\LogProcessDossieController;
use App\Http\Controllers\MonitoriaController;
use App\Http\Controllers\ParameterController;
use App\Http\Controllers\PerfilConsultaController;
use App\Http\Controllers\PerfilConsultaPadraoController;
use App\Http\Controllers\PreParameterController;
use App\Http\Controllers\ResultController;
use App\Http\Controllers\SourceController;
use App\Http\Controllers\SourceDictionaryController;
use App\Http\Controllers\SourceRelevanceController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\UsuarioController;
use App\Http\Controllers\ConfiguracaoPdfController;
use App\Http\Controllers\ConvertCommentsToNewDossie;
use App\Http\Controllers\DossieAnalyticsController;
use App\Http\Controllers\FoxyAiController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
    | is assigned the "api" middleware group. Enjoy building your API!
|
 */
Route::prefix('{token}/dossie')->group(function () {
    Route::get('/captura/view', [DossieController::class, 'viewCapturaByDossie']);
});

Route::prefix('tags')->group(function() {
    Route::get('/', [TagController::class, 'index']);
    Route::get('/{id}', [TagController::class, 'show']);
    Route::post('/', [TagController::class, 'store']);
    Route::put('/{id}', [TagController::class, 'update']);
    Route::middleware(['master', 'permission:contents.master_permissions.delete-tags'])->group(function() {
        Route::delete('/{id}', [TagController::class, 'destroy']);
    });
    Route::post('/{tagId}/lote/{loteId}', [TagController::class, 'addTagToLote']);
    Route::delete('/lote/{loteId}', [TagController::class, 'removeTagFromLote']);
});

Route::prefix('foxy-ai')->group(function () {
    Route::post('/insights', [FoxyAiController::class, 'index']);
});

Route::post('/homonyms', [DossieController::class, 'searchHomonyms']);

Route::prefix('dossie')->group(function () {
    Route::get('/', [DossieController::class, 'index']);

    Route::prefix('limit')->middleware([
        'master',
        'permission:contents.master_permissions.access-general-config',
        'permission:contents.master_permissions.edit-dossie-general-config'
    ])->group(function () {
        Route::get('', [DossieController::class, 'getLimitDossie']);
        Route::post('', [DossieController::class, 'storeLimitDossie']);
        Route::get('/limite-resultado-fontes', [DossieController::class, 'getLimitResultSource']);
        Route::post('/limite-resultado-fonte', [DossieController::class, 'storeLimitResultSource']);
    });


    Route::post('/export-pdf-details', [DossieController::class, 'sendDetails']);
    Route::post('/export-pdf-resume', [DossieController::class, 'sendResume']);
    Route::post('/export-pdf-lawsuits', [DossieController::class, 'sendLawsuits']);
    Route::get('/get-status-pdf-request', [DossieController::class, 'getStatusPdfRequest']);

    Route::prefix('descritivo/{dossieID}')->group(function () {
        Route::get('/', [DossieSummaryCompanyController::class, 'getSummaryCompany']);
        Route::post('/', [DossieSummaryCompanyController::class, 'addSummaryClient']);
        Route::delete('/', [DossieSummaryCompanyController::class, 'removeSummaryClient']);
    });

    Route::prefix('comentario')->group(function () {
        Route::prefix('{dossieId}')->group(function () {
            Route::get('/', [DossieCommentsController::class, 'getComments']);
            Route::post('/', [DossieCommentsController::class, 'addComment']);
            Route::put('/', [DossieCommentsController::class, 'updateComment']);
            Route::delete('/{comentarioID}', [DossieCommentsController::class, 'deleteComment']);
        });
        Route::get('/historico/{dossieId}/{commentId}', [DossieCommentsController::class, 'getCommentHistoric']);
        Route::prefix('/relevante/{dossieId}')->group(function () {
            Route::put('/', [DossieCommentsController::class, 'updateAllCommentRelevance']);
            Route::prefix('/{commentId}')->group(function () {
                Route::post('/', [DossieCommentsController::class, 'addCommentRelevance']);
                Route::delete('/', [DossieCommentsController::class, 'removeCommentRelevance']);
            });
        });

        Route::prefix('/reacao/{dossieId}/{commentId}/{reactionId}')->group(function () {
            Route::post('/', [CommentReactionController::class, 'addCommentReaction']);
            Route::put('/', [CommentReactionController::class, 'updateCommentReaction']);
            Route::delete('/', [CommentReactionController::class, 'removeCommentReaction']);
        });

        Route::get('/mencoes/{dossieId}', [DossieCommentMarkedUsersController::class, 'getMarcked']);
        Route::prefix('/configuracao/{dossieId}')->group(function () {
            Route::get('/', [DossieCommentConfigController::class, 'getConfigComments']);
            Route::post('/', [DossieCommentConfigController::class, 'addConfigComments']);
        });
    });

    Route::prefix('reacao/fonte/{dossieID}')->group(function () {
        Route::post('/', [DossieReactionFonteController::class, 'addReacaoFonte']);
        Route::delete('/{sourceID}', [DossieReactionFonteController::class, 'removeReacaoFonte']);
    });

    Route::prefix('reacao/categoria/{dossieID}')->group(function () {
        Route::post('/', [DossieReactionCategoryController::class, 'addReactionCategory']);
        Route::delete('/{categoryAlias}', [DossieReactionCategoryController::class, 'removeReactionCategory']);
    });

    Route::prefix('tags')->group(function () {
        Route::prefix('/{dossieID}')->group(function () {
            Route::get('/', [DossieTagsController::class, 'getTags']);
            Route::post('/', [DossieTagsController::class, 'addTags']);
            Route::put('/', [DossieTagsController::class, 'editTags']);
            Route::delete('/{tagID}', [DossieTagsController::class, 'removeTags']);
        });

        Route::prefix('/fonte/{dossieID}')->group(function () {
            Route::post('/', [DossieTagsController::class, 'addTagsFonte']);
            Route::put('/', [DossieTagsController::class, 'editTagsFonte']);
            Route::delete('/{sourceID}', [DossieTagsController::class, 'removeTagsFonte']);
        });

        Route::prefix('/category/{dossieID}')->group(function () {
            Route::post('/', [DossieTagsController::class, 'addTagsCategory']);
            Route::put('/', [DossieTagsController::class, 'editTagsCategory']);
            Route::delete('/{categoryAlias}', [DossieTagsController::class, 'removeTagsCategory']);
        });
    });

    Route::post('/fonte/hidden/{dossieID}', [SourceRelevanceController::class, 'set']);

    Route::prefix('resultado')->group(function () {
        Route::prefix('/cards/{dossieID}')->group(function () {
            Route::get('/', [ResultController::class, 'getDossieCardsData']);
            Route::get('/info', [ResultController::class, 'getDossieCardsInfo']);
            Route::get('/without-result-sources', [ResultController::class, 'getDossieCardsWithoutResultSources']);
            Route::get('/with-result-sources', [ResultController::class, 'getDossieCardsWithResultSources']);
        });
        Route::get('/source-group/{dossieId}', [ResultController::class, 'getSourceGroup']);
        Route::get('/search-other-apps/{dossieId}', [ResultController::class, 'getAppsDisponiveis']);
        Route::get('/reactions', [ResultController::class, 'getReactions']);
        Route::get('/resumo/grupo/{dossieID}/{grupoAlias}', [ResultController::class, 'getSourcesGroupResume']);
        Route::prefix('/analytics/{dossieId}')->group(function() {
            Route::get('/juridico', [
                DossieAnalyticsController::class, 'getAnalyticsJuridicoByDossieId'
            ]);
            Route::get('/midia_internet', [
                DossieAnalyticsController::class, 'getAnalyticsMidiaInternetByDossieId'
            ]);
        });
    });

    Route::prefix('workflow')->group(function () {
        Route::get('/detalhes/{dossieID}', [DossieWorkflowController::class, 'getDetails']);
        Route::get('/detalhes/analysis/{dossieID}', [DossieWorkflowController::class, 'getAnalysis']);
        Route::post('/detalhes/aprovar/{dossieID}', [DossieWorkflowController::class, 'addApprove']);
        Route::put('/detalhes/analysis/{dossieID}/reason', [DossieWorkflowController::class, 'updateAnalysis']);

        Route::prefix('/comentario')->group(function () {
            Route::prefix('/{dossieId}')->group(function () {
                Route::get('/', [DossieCommentsController::class, 'getCommentsWorkflow']);
                Route::post('/', [DossieCommentsController::class, 'addCommentWorkflow']);
                Route::put('/', [DossieCommentsController::class, 'updateCommentWorkflow']);
                Route::delete('/{commentId}', [DossieCommentsController::class, 'deleteCommentWorkflow']);
            });

            Route::get(
                '/historico/{dossieId}/{comentarioId}',
                [DossieCommentsController::class, 'getCommentHistoric']
            );
            Route::prefix('/reacao/{dossieId}/{commentId}/{reactionID}')->group(function () {
                Route::post('/', [CommentReactionController::class, 'addCommentReactionWorkflow']);
                Route::put('/', [CommentReactionController::class, 'updateCommentReactionWorkflow']);
                Route::delete('/', [CommentReactionController::class, 'removeCommentReactionWorkflow']);
            });
        });
    });

    Route::get('/upflag/detalhes/{dossieID}', [DossieUpflagController::class, 'getDetails']);
    Route::prefix('/analise')->group(function () {
        Route::prefix('/{dossieID}')->group(function () {
            Route::post('/', [DossieAnalysisController::class, 'addAnalysis']);
            Route::get('/', [DossieAnalysisController::class, 'getAnalysis']);
            Route::put('/relevance', [DossieAnalysisController::class, 'updateRelevance']);
        });
        Route::get('/historico/{dossieID}', [DossieAnalysisController::class, 'getHistoric']);
    });


    Route::get('/upscore/detalhes/{dossieID}', [DossieUpscoreController::class, 'getDetails']);

    Route::get('/convertOldDossie/{dossieID}', [ConvertCapturasToNewDossie::class, 'convert']);
    Route::post('/convertOldDossie/summary', [ConvertCapturasToNewDossie::class, 'convertSummary']);
    Route::post('/convertOldDossie/comments/{dossieID}', [ConvertCommentsToNewDossie::class, 'convert']);
    Route::get('/sourceDictionary/{sourceID}', [SourceDictionaryController::class, 'getSourceDictionary']);

    Route::prefix('/file/custom')->group(function () {
        Route::get('/list', [ArquivosPersonalizadosController::class, 'listFiles']);
        Route::get('/download/{id}', [ArquivosPersonalizadosController::class, 'download']);
    });

    Route::prefix('/resumo')->group(function () {
        Route::get('');
    });

    Route::get('/get-process/{dossieId}', [LogProcessDossieController::class, 'show']);
    Route::get('/get-progress', [LogProcessDossieController::class, 'getProgress']);
    Route::post('/reprocess', [DossieController::class, 'reprocessDossies']);

    Route::prefix('/configuration')
        ->middleware([
            'master',
            'permission:contents.master_permissions.access-general-config',
            'permission:contents.master_permissions.edit-dossie-general-config'
        ])
        ->group(function() {
            Route::prefix('/duplicity-alert')->group(function() {
                Route::get('/', [ConfiguracaoDossieController::class, 'getDuplicityAlert']);
                Route::put('/', [ConfiguracaoDossieController::class, 'updateDuplicityAlert']);
            });

            Route::prefix('/siglas-exclusion')->middleware(
                'permission:contents.master_permissions.access-acronym-exclusion-general-config'
            )->group(function() {
                Route::get('/', [ConfiguracaoDossieController::class, 'getSiglasExclusion']);
                Route::put('/', [ConfiguracaoDossieController::class, 'updateSiglasExclusion'])->middleware(
                    'permission:contents.master_permissions.edit-acronym-exclusion-general-config'
                );
            });

            Route::prefix('/media')->middleware(
                'permission:contents.master_permissions.access-searchers-general-config'
            )->group(function() {
                Route::prefix('/google')->group(function() {
                    Route::get('/', [ConfiguracaoGoogleController::class, 'listProfilesPaginated']);
                    Route::get('/all', [ConfiguracaoGoogleController::class, 'listProfilesAll']);
                    Route::middleware(
                        'permission:contents.master_permissions.edit-google-searcher-general-config'
                    )->group(function() {
                        Route::post('/replicate', [ConfiguracaoGoogleController::class, 'replicateConfiguration']);
                        Route::get('/perfil/{idPerfil}', [ConfiguracaoGoogleController::class, 'getPerfil']);
                        Route::post('/perfil/{idPerfil}', [
                            ConfiguracaoGoogleController::class, 'saveGoogleConfiguration'
                        ]);
                        Route::delete(
                            '/perfil/{idPerfil}/delete-country',
                            [ConfiguracaoGoogleController::class, 'deleteGoogleCountry']
                        );
                    });
                    // Rotas de configuração do Google para os perfis de consulta padrões
                    Route::prefix('perfil-padrao')->middleware('admin')->group(function() {
                        Route::get('/', [ConfiguracaoGooglePerfilPadraoController::class, 'listProfilesPaginated']);
                        Route::get('/all', [ConfiguracaoGooglePerfilPadraoController::class, 'listProfilesAll']);
                        Route::post('/replicate', [
                            ConfiguracaoGooglePerfilPadraoController::class, 'replicateConfiguration'
                        ]);
                        Route::get('/{idPerfil}', [ConfiguracaoGooglePerfilPadraoController::class, 'getPerfil']);
                        Route::post('/{idPerfil}', [
                            ConfiguracaoGooglePerfilPadraoController::class, 'saveGoogleConfiguration'
                        ]);
                        Route::delete(
                            '/{idPerfil}/delete-country',
                            [ConfiguracaoGooglePerfilPadraoController::class, 'deleteGoogleCountry']
                        );
                    });
                });
                Route::prefix('/bing')->group(function() {
                    Route::get('/', [ConfiguracaoBingController::class, 'listConfigurationsPaginated']);
                    Route::middleware(
                        'permission:contents.master_permissions.edit-bing-searcher-general-config'
                    )->group(function() {
                            Route::get('/{idLista}', [
                                ConfiguracaoBingController::class, 'getConfigurationDetails'
                            ]);
                            Route::get('/options/get', [
                                ConfiguracaoBingController::class, 'getConfigurationOptions'
                            ]);
                            Route::post('/', [ConfiguracaoBingController::class, 'store']);
                            Route::put('/{idLista}', [ConfiguracaoBingController::class, 'update']);
                            Route::put('lista-exclusao/{idLista}/status', [
                                ConfiguracaoBingController::class, 'updateListaExclusaoStatus'
                            ]);
                            Route::put('lista-palavras/{idLista}/status', [
                                ConfiguracaoBingController::class, 'updateListaPalavrasStatus'
                            ]);
                    });
                });
            });
            Route::prefix('/custom-files')->middleware([
                'permission:contents.master_permissions.edit-custom-file-general-config'
            ])->group(function() {
                Route::get('/', [ConfiguracaoDossieController::class, 'getCustomFiles']);
                Route::put('/', [ConfiguracaoDossieController::class, 'updateCustomFiles']);
            });

            Route::get('/pdf', [ConfiguracaoPdfController::class, 'get']);
            Route::post('/pdf', [ConfiguracaoPdfController::class, 'update']);
            Route::prefix('/api')->group(function() {
                Route::resource('gupy', ConfiguracaoApiGupyController::class);
            });
            Route::prefix('/perfil')->middleware(
                'permission:contents.master_permissions.access-search-profile'
            )->group(function () {
                Route::get('/', [PerfilConsultaController::class, 'index']);
                Route::get('/get/{idPerfil}', [PerfilConsultaController::class, 'show']);
                Route::middleware('permission:contents.master_permissions.edit-search-profile')
                    ->group(function() {
                        Route::post('/', [PerfilConsultaController::class, 'store']);
                        Route::put('/{idPerfil}', [PerfilConsultaController::class, 'update']);
                        Route::delete('/{idPerfil}', [PerfilConsultaController::class, 'destroy']);
                        Route::post('/inactivate', [PerfilConsultaController::class, 'inactivate']);
                        Route::post('/duplicate', [PerfilConsultaController::class, 'duplicate']);
                        Route::get('/foxy-api/{idPerfil}', [
                            PerfilConsultaController::class, 'getPermissionChatGpt'
                        ]);
                        Route::post('/foxy-ai/{idPerfil}', [
                            PerfilConsultaController::class, 'newPermissionChatGpt'
                        ]);
                    });
                Route::get('/details', [PerfilConsultaController::class, 'details']);
                Route::get('/get-group-user-profile', [PerfilConsultaController::class, 'getGroupUserProfile']);
                Route::get('/export/excel/{id}', [PerfilConsultaController::class, 'exportExcel']);
                Route::get('/list', [PerfilConsultaController::class, 'listPerfil']);
                Route::get('/list/batch-monitoring/{idPerfil}', [
                    PerfilConsultaController::class, 'getBatchsMonitoring'
                ]);
                Route::prefix('sources')->group(function () {
                    Route::get('/', [PerfilConsultaController::class, 'getSources']);
                    Route::get('/available', [PerfilConsultaController::class, 'getAllAvailableSourcesForProfile']);
                    Route::get('/homonyms/{idPerfil}', [PerfilConsultaController::class, 'getSourcesHomonyms']);
                    Route::get('/parameters/{type}', [PerfilConsultaController::class, 'getParametersByType']);
                    Route::get('/{idPerfil}', [PerfilConsultaController::class, 'getSourcesByPerfilId']);
                });

                Route::prefix('workflow')->middleware(
                    'permission:contents.master_permissions.access-workflow-search-profile'
                )->group(function () {
                    Route::get('/{idPerfil}', [DossieWorkflowController::class, 'getWorkflowByPerfilId']);
                    Route::post('/{idPerfil}', [DossieWorkflowController::class, 'store'])->middleware(
                        'permission:contents.master_permissions.edit-workflow-search-profile'
                    );
                });
            });
            Route::prefix('perfil-padrao')->middleware('admin')->group(function () {
                Route::get('/', [PerfilConsultaPadraoController::class, 'index']);
                Route::post('/', [PerfilConsultaPadraoController::class, 'store']);
                Route::put('/{idPerfil}', [PerfilConsultaPadraoController::class, 'update']);
                Route::delete('/{idPerfil}', [PerfilConsultaPadraoController::class, 'destroy']);
                Route::get('/details', [PerfilConsultaPadraoController::class, 'details']);
                Route::post('/inactivate', [PerfilConsultaPadraoController::class, 'inactivate']);
                Route::post('/duplicate', [PerfilConsultaPadraoController::class, 'duplicate']);
                Route::get('/get/{idPerfil}', [PerfilConsultaPadraoController::class, 'show']);
                Route::get('/export/excel/{id}', [PerfilConsultaPadraoController::class, 'exportExcel']);
                Route::get('/foxy-ai/{idPerfil}',
                    [PerfilConsultaPadraoController::class, 'getPermissionChatGpt']
                );
                Route::post('/foxy-ai/{idPerfil}',
                    [PerfilConsultaPadraoController::class, 'newPermissionChatGpt']
                );
                Route::prefix('sources')->group(function () {
                    Route::get('/', [PerfilConsultaController::class, 'getSources']);
                    Route::get('/available', [PerfilConsultaController::class, 'getAllAvailableSourcesForProfile']);
                    Route::get('/parameters/{type}', [PerfilConsultaController::class, 'getParametersByType']);
                    Route::get('/{idPerfil}', [PerfilConsultaPadraoController::class, 'getSourcesByPerfilId']);
                });
            });
    });

    Route::get('/check-errors', [DossieController::class, 'checkErrors']);
    Route::post('/fix-errors', [DossieController::class, 'fixErrors']);
});
Route::prefix('lote')->group(function () {
    Route::get('/dossie/{id}', [DossieController::class, 'show']);
    Route::put('/proccess', [DossieController::class, 'addLoteFila']);
    Route::get('/historico', [DossieController::class, 'historicoLote']);
    Route::post('/dossie', [DossieController::class, 'store']);
    Route::post('/lote-duplicado', [DossieController::class, 'loteDuplicado']);
    Route::post('/extra-dossiers-generated', [DossieController::class, 'extraDossiersGenerated']);

    Route::get('/filters', [DossieController::class, 'filters']);

    Route::get('/listagem-perfis', [PerfilConsultaController::class, 'listParameterByPerfil']);
    Route::get('/perfil/list', [PerfilConsultaController::class, 'listPerfil']);

    Route::post('/criterio/autocomplete', [AutocompleteClient::class, 'getSuggestions']);

    Route::prefix('parameters')->group(function () {
        Route::prefix('pre-parameters')->group(function () {
            Route::post('/source', [PreParameterController::class, 'index']);//list
            Route::get('/{pre_parameter}', [PreParameterController::class, 'show']);//item
            Route::post('/', [PreParameterController::class, 'store']);//store
            Route::delete('/{pre_parameter}', [PreParameterController::class, 'destroy']);
        });
        Route::get('/{source:metodo}', [ParameterController::class, 'index']);
    });
    Route::post('/valida/criterio', [PerfilConsultaController::class, 'validaCriterio'])->middleware(
        'restriction:contents.dossie.report-custom-file'
    );
    Route::get('/perfil/details', [PerfilConsultaController::class, 'details']);

    Route::prefix('monitoria')->middleware('monitoring-permission')->group(function () {
        Route::get('/{id}', [MonitoriaController::class, 'show']);
        Route::prefix('/lote/{loteId}')->group(function () {
            Route::get('/', [MonitoriaController::class, 'getByLote']);
            Route::post('/', [MonitoriaController::class, 'createMonitoringFromDossiesTab']);
            Route::put('/', [MonitoriaController::class, 'updateMonitoringByLoteId']);
            Route::post('/stop', [MonitoriaController::class, 'stopMonitoringByLoteId']);
            Route::post('/restart', [MonitoriaController::class, 'restartMonitoringByLoteId']);
        });
    });
});

Route::prefix('source')->group(function () {
    // Rota de teste
    Route::prefix('resultado')->group(function () {
        Route::get('/testar-template/{sourceID}', [SourceController::class, 'getSouceResultTest']);
        Route::get('/{dossieID}/{sourceID}', [SourceController::class, 'getSouceResult']);
    });

    Route::prefix('comentario')->group(function () {
        Route::prefix('{dossieId}')->group(function () {
            Route::get('/{sourceId}', [DossieCommentsController::class, 'getCommentsSource']);
            Route::post('/', [DossieCommentsController::class, 'addCommentSource']);
            Route::put('/', [DossieCommentsController::class, 'updateComment']);
            Route::delete('/{commentId}', [DossieCommentsController::class, 'deleteComment']);
        });
        Route::get(
            '/historico/{dossieId}/{commentId}',
            [DossieCommentsController::class, 'getCommentHistoric']
        );
        Route::prefix('/reacao/{dossieId}/{commentId}/{reactionId}')->group(function () {
            Route::post('/', [CommentReactionController::class, 'addCommentReaction']);
            Route::put('/', [CommentReactionController::class, 'updateCommentReaction']);
            Route::delete('/', [CommentReactionController::class, 'removeCommentReaction']);
        });
    });
    // TBD - Criar a rota dossie/captura/view igual a do upminer api e retornando os dados no mesmo formato

});

Route::prefix('user')->group(function () {
    Route::get('/', [UsuarioController::class, 'getUsuarioSession']);
    Route::prefix('group')->group(function () {
        Route::get('', [UsuarioController::class, 'getUsuarioByGrupo']);
    });
    Route::get('/group-users', [UsuarioController::class, 'getGroupUsers']);
    Route::get('/group/all', [UsuarioController::class, 'getAllGroups']);
    Route::get('/appsDisponiveis', [UsuarioController::class, 'getAppsDisponiveis']);
});


// TBD
// Apis para salvar configurações de notificações de comentarios
// Apis para pegar lista de usuário no mesmo grupo quando o usuário começar a digitar e ja tiver no minimo 2 letras, pesquisar pelo inicio do email
// - deve retornar alias do usuário e id do usuario
// APIs para cadastrar, editar e deletar tags (PRECISA CRIAR TABELA NO BANCO DE DADOS)
// APIs para cadastrar, editar e deletar comentarios nas fontes (PRECISA CRIAR TABELA NO BANCO DE DADOS)
// APIs para atualizar configurações de itens visiveis no dossie (PRECISA CRIAR TABELA NO BANCO DE DADOS)
// APIS para atualizar o status da analise do dossie por e salvar o comentario da analise
//  -- Duvida se um usuário aprovar e outro reprovar o que deve acontecer?

// listar comentarios
// mensao - @ trazer apenas os comentarios que me mencionaram
// configurações:
//     notificações
//         configurar por onde ele quer receber as notificações

//             emails, relevante, via upminer

Route::get('/status', function () {
    return 'OK';
});
