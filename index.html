<!DOCTYPE html>
<html lang="pt-BR">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" href="/logo.ico" />
		<link rel="preconnect" href="https://fonts.googleapis.com" />
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
		<link
			href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
			rel="stylesheet" />
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Dossiê</title>
	</head>
	<body>
		<div id="app"></div>
		<script type="module" src="/src/main.js"></script>

		<script type="text/javascript">
			window.smartlook ||
				(function (d) {
					var o = (smartlook = function () {
							o.api.push(arguments)
						}),
						h = d.getElementsByTagName('head')[0]
					var c = d.createElement('script')
					o.api = new Array()
					c.async = true
					c.type = 'text/javascript'
					c.charset = 'utf-8'
					c.src = 'https://web-sdk.smartlook.com/recorder.js'
					h.appendChild(c)
				})(document)
			smartlook('init', '59505cb3dd5f15b2e3d7ff063e9b757b41c36fc0', {
				region: 'eu',
			})
		</script>

		<script>
			window.STONLY_WID = 'f7268861-8e6e-11ec-9fb8-0ae9fa2a18a2'

			!(function (s, t, o, n, l, y, w, g) {
				s.StonlyWidget ||
					(((w = s.StonlyWidget =
						function () {
							w._api ? w._api.apply(w, arguments) : w.queue.push(arguments)
						}).scriptPath = n),
					(w.queue = []),
					((y = t.createElement(o)).async = !0),
					(g = new XMLHttpRequest()).open(
						'GET',
						n + 'version?v=' + Date.now(),
						!0
					),
					(g.onreadystatechange = function () {
						4 === g.readyState &&
							((y.src =
								n +
								'stonly-widget.js?v=' +
								(200 === g.status ? g.responseText : Date.now())),
							(l = t.getElementsByTagName(o)[0]).parentNode.insertBefore(y, l))
					}),
					g.send())
			})(window, document, 'script', 'https://stonly.com/js/widget/v2/')
		</script>

		<!-- Start of HubSpot Embed Code -->
		<script
			type="text/javascript"
			id="hs-script-loader"
			async
			defer
			src="//js.hs-scripts.com/20084253.js"></script>
		<!-- End of HubSpot Embed Code -->

		<script>
			function initializeHubSpotChat(email, hubspotVisitorKey) {
				window.hsConversationsSettings = {
					identificationEmail: email,
					visitorIdentificationKey: hubspotVisitorKey,
					loadImmediately: false,
				}

				window.hsConversationsOnReady = [
					() => {
						window.HubSpotConversations.widget.load()
					},
				]
			}

			const checkDataInterval = setInterval(() => {
				const userData = JSON.parse(
					localStorage.getItem('user-dossie-data-persist')
				)
				if (userData && userData.email && userData.hubspot_visitor_key) {
					clearInterval(checkDataInterval)
					initializeHubSpotChat(userData.email, userData.hubspot_visitor_key)
				}
			}, 1000)
		</script>

		<script type="module">
			if (import.meta.env.VITE_APP_ENV === 'prod') {
				;(function (d) {
					var o = (smartlook = function () {
							o.api.push(arguments)
						}),
						h = d.getElementsByTagName('head')[0]
					var c = d.createElement('script')
					o.api = new Array()
					c.async = true
					c.type = 'text/javascript'
					c.charset = 'utf-8'
					c.src = 'https://web-sdk.smartlook.com/recorder.js'
					h.appendChild(c)
				})(document)

				smartlook('init', '59505cb3dd5f15b2e3d7ff063e9b757b41c36fc0', {
					region: 'eu',
				})
			}
		</script>
	</body>
</html>
