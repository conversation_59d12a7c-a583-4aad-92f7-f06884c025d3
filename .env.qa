APP_NAME=dossie-api
APP_ENV=qa
APP_KEY=base64:bLGU3BLSCJz+C9P+DY6IFK8/bjyfBOa/KSObaHUaM44=
APP_DEBUG=true
APP_URL=https://dossie-api.qa.uplexis.com.br

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

UPMINER_API=https://upminer-api.qa.uplexis.com.br
UPMINER_URL=https://upminer.qa.uplexis.com.br
DOSSIE_URL=https://dossie.qa.uplexis.com.br

# Upminer Database
UPMINER_CONNECTION=upminer
UPMINER_HOST=db-qa.cu7goubbl3mv.us-east-1.rds.amazonaws.com
UPMINER_PORT=5432
UPMINER_DATABASE=upminerdossie
UPMINER_DEFAULT_SCHEMA=public

##### CAPTURA #####
CAPTURA3_CONNECTION=pgsql_captura3
CAPTURA3_HOST=db-qa.cu7goubbl3mv.us-east-1.rds.amazonaws.com
CAPTURA3_PORT=5432
CAPTURA3_DATABASE=captura3

###### UPSCORE######
UPSCORE_HOST=db-qa.cu7goubbl3mv.us-east-1.rds.amazonaws.com
UPSCORE_PORT=5432
UPSCORE_DATABASE=upscore_app
UPSCORE_DEFAULT_SCHEMA=public

##### UPSEARCH #####
UPSEARCH_HOST=aplicativos-qa-instance-1.cu7goubbl3mv.us-east-1.rds.amazonaws.com
UPSEARCH_PORT=5432
UPSEARCH_DATABASE=upsearch
UPSEARCH_DEFAULT_SCHEMA=public

##### AURORA #####
AURORA_HOST=db1-qa-log.cu7goubbl3mv.us-east-1.rds.amazonaws.com
AURORA_PORT=3306
AURORA_DATABASE=log

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# SESSION_DRIVER
SESSION_DRIVER = redis
CACHE_DRIVER = redis
REDIS_CACHE_HOST = qa-aplicativos-cache.ywgtda.ng.0001.use1.cache.amazonaws.com
REDIS_HOST = qa-aplicativos.ywgtda.ng.0001.use1.cache.amazonaws.com

UPSCORE_API_URL=https://upscore-api.qa.uplexis.com.br

AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

SQS_QUEUE_MONITORING_SERVICE=MonitoriaMotorProcessarMonitoriaSqs-qa
AWS_REGION=us-east-1
SQS_PREFIX_MONITORING_SERVICE=https://sqs.us-east-1.amazonaws.com/857717895630

##S3
S3_STATIC_REGION=us-east-1
S3_STATIC_BUCKET=estatico.uplexis.com.br
S3_STATIC_HOST_URL=s3.amazonaws.com
S3_STATIC_URL=https://${S3_STATIC_HOST_URL}/${S3_STATIC_BUCKET}

UPJURIS_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
DOSSIES_APP=https://upminer.qa.uplexis.com.br/dossies
ENRIQUECIMENTO_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
SUGERIDOR_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
NEW_ENRIQUECIMENTO_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
COMPARADOR_SOCIETARIO_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
BALANCO_PATRIMONIAL_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
VALIDADOR_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
VEICULO_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPMAP_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPLINK_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPMATCH_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
CERTIDOES_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
HISTORICO_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
MONITORIA_DOSSIE_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
DIARIOS_OFICIAIS_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPFLAG_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
NEW_UPJURIS_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
IMOVEIS_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
NEW_UPMATCH_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
OBRAS_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPAPI_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPFOLDER_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
UPSEARCH_APP=https://upminer.qa.uplexis.com.br/aplicativos/app
DOSSIE_APP=https://dossie.qa.uplexis.com.br/dossiers

ENV_SWAGGER_URL=https://dossie-api.qa.uplexis.com.br
ENV_SWAGGER_DESCRIPTION="API de QA"

#ses
AWS_REGION=us-east-1

#E-MAIL
MAIL_MAILER=smtp
MAIL_ENCRYPTION=tls
MAIL_HOST=email-smtp.us-east-1.amazonaws.com
MAIL_PORT=587
MAIL_USERNAME=${AWS_SES_KEY}
MAIL_PASSWORD=${AWS_SES_SECRET}}
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=upMiner

#logo upminer
LOGO=https://d15k2d11r6t6rl.cloudfront.net/public/users/Integrators/BeeProAgency/397501_377031/1_RGB_upLogo_Color.png


#url pdf
EXPORTACAO_PDF_URL=https://res.qa.uplexis.com.br

#spine pf
## usar a base de prod do spine em qa para testes
SPINE_URL=https://98v9sns4k3.execute-api.us-east-1.amazonaws.com/prod
#spine pj
CAPTURA_LAMBDA=https://lambda.qa.uplexis.com.br/captura-qa

#autocomplete
## usar a base de prod do spine em qa para testes
AUTOCOMPLETE_URL=https://98v9sns4k3.execute-api.us-east-1.amazonaws.com/prod/autocomplete

WITH_EXCEPTIONS=true

### ELASTIC SEARCH LOGS ###
ES_HOST_LOGS=https://vpc-dossie-service-6arwmxtjjzwbrpjnilsiyekiji.us-east-1.es.amazonaws.com:443
ES_INDEX_LOGS=dossie-service-logs
ES_INDEX_LOGS_DETAILS=dossie-service-details-logs

### SQS DOSSIE SERVICE ###
SQS_PREFIX_DOSSIE_SERVICE=https://sqs.us-east-1.amazonaws.com/857717895630
SQS_QUEUE_DOSSIE_SERVICE=dossie-service-qa.fifo

### UPFOLDER ###
UPFOLDER_SQS=https://sqs.us-east-1.amazonaws.com/857717895630/upfolder-history-qa
DOSSIE_PRODUCT_ID=65
DOSSIE_PRODUCT_NAME="Dossiês"

### HOMÔNIMOS ###
HOMONIMOS_URL="https://98v9sns4k3.execute-api.us-east-1.amazonaws.com/prod/search?field=individuos"

### GUPY ###
GUPY_INTEGRATION=https://gupy-integration.qa.uplexis.com.br/api

### FOXY.IA ###
BROWNARROW_API_URL=https://uplexis-test.brownarrow.ai
BROWNARROW_API_KEY=ad69cc938be430d82fe851e0d6d2d7c0
GOOGLE_MAPS_GEOCODING_API_KEY=AIzaSyD3cZ2QPOg1WzKF-eenIO37d77JmTnXJ0A

### DOSSIER_PROGRESS_LAMBDA ###
DOSSIE_LAMBDA_PROGRESS_LAMBDA=dossie-process-lambda-qa-get-dossier-process

### ENDEREÇO DO SIGNOZ PARA TRACES E LOGS ###
OTEL_EXPORTER_OTLP_ENDPOINT=http://52.22.248.251:4318
OTEL_EXPORTER_OTLP_ENDPOINT_LOGS=http://52.22.248.251:4318
### NOME DA APLICAÇÃO PARA SEPARAR OS TRACES NO SIGNOZ###
OTEL_SERVICE_NAME=dossie-api-qa

GOOGLE_CHAT_WEBHOOK_URL="https://chat.googleapis.com/v1/spaces/AAAAUnXPLfc/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=YMCGXiC0sj5mAcc3UH94cwzM1v7PkRU4KJz8Ws9tuMs"
