{"name": "package.json", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky install"}, "dependencies": {"@aplicativo/uplexis-ui": "latest", "@googlemaps/js-api-loader": "^1.16.2", "@mdi/font": "^7.2.96", "@tanstack/vue-query": "^5.51.21", "@vueuse/core": "^10.4.1", "apexcharts": "^3.43.0", "axios": "^1.4.0", "axios-retry": "^3.5.1", "balloon-css": "^1.2.0", "floating-vue": "^2.0.0-beta.24", "pinia": "^2.1.6", "radix-vue": "^1.8.5", "roboto-fontface": "^0.10.0", "sass": "^1.62.1", "vue": "^3.3.2", "vue-i18n": "^9.2.2", "vue-inline-svg": "^3.1.2", "vue-mention": "^2.0.0-alpha.3", "vue-router": "^4.2.0", "vue-slider-component": "^4.1.0-beta.7", "vue-tippy": "^6.3.1", "vue-toastification": "^2.0.0-rc.5", "vue3-apexcharts": "^1.4.4", "vuetify": "3.4.0", "vuewordcloud": "^19.0.0", "webfontloader": "^1.6.28", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/vue": "^4.1.2", "@intlify/unplugin-vue-i18n": "^0.12.2", "@rushstack/eslint-patch": "^1.2.0", "@tailwindcss/forms": "^0.5.7", "@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^7.1.0", "autoprefixer": "^10.4.17", "eslint": "^8.39.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-vue": "^9.11.0", "git-commit-msg-linter": "^5.0.4", "husky": "^8.0.3", "lint-staged": "^13.2.2", "postcss": "^8.4.35", "postcss-import": "^16.1.0", "prettier": "^2.8.8", "tailwindcss": "^3.4.1", "tailwindcss-inner-border": "^0.2.0", "vite": "^4.3.5", "vite-plugin-vuetify": "1.0.2"}}