#!/usr/bin/env python3
"""
Script para testar o Model Context Protocol (MCP)
"""

import os
import sys
import json
import argparse
from dotenv import load_dotenv
import requests

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()

class MCPClient:
    """Cliente para o Model Context Protocol"""
    
    def __init__(self, api_url=None):
        """Inicializa o cliente MCP"""
        self.api_url = api_url or os.getenv('MCP_API_URL', f"http://localhost:{os.getenv('MCP_PORT', '5000')}")
        
    def query(self, query_type, params=None):
        """Envia uma consulta para o servidor MCP"""
        url = f"{self.api_url}/api/mcp/query"
        data = {
            'type': query_type,
            'params': params or {}
        }
        
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()
            return response.json()['result']
        except requests.exceptions.RequestException as e:
            print(f"Erro ao enviar requisição: {e}")
            if hasattr(e, 'response') and e.response:
                print(f"Resposta do servidor: {e.response.text}")
            sys.exit(1)
        
    def get_client_info(self, client_id=None):
        """Obtém informações de um cliente"""
        return self.query('client', {
            'query_type': 'info',
            'client_id': client_id
        })
    
    def count_active_clients(self):
        """Conta o número de clientes ativos"""
        return self.query('client', {
            'query_type': 'count_active'
        })
    
    def list_active_clients(self, limit=10):
        """Lista os clientes ativos"""
        return self.query('client', {
            'query_type': 'list_active',
            'limit': limit
        })
    
    def get_dossier_info(self, dossier_id):
        """Obtém informações de um dossier"""
        return self.query('dossier', {
            'query_type': 'info',
            'dossier_id': dossier_id
        })
    
    def list_recent_dossiers(self, limit=10):
        """Lista os dossiers mais recentes"""
        return self.query('dossier', {
            'query_type': 'list_recent',
            'limit': limit
        })

def print_json(data):
    """Imprime dados em formato JSON"""
    print(json.dumps(data, indent=2, default=str))

def main():
    parser = argparse.ArgumentParser(description='Testar o Model Context Protocol (MCP)')
    parser.add_argument('--action', choices=[
        'count_active_clients', 
        'list_active_clients',
        'client_info',
        'dossier_info',
        'list_recent_dossiers'
    ], required=True, help='Ação a ser executada')
    parser.add_argument('--id', type=int, help='ID do cliente ou dossier (para ações que requerem ID)')
    parser.add_argument('--limit', type=int, default=10, help='Limite de resultados (para ações de listagem)')
    args = parser.parse_args()
    
    # Inicializa o cliente MCP
    client = MCPClient()
    
    # Executa a ação especificada
    if args.action == 'count_active_clients':
        result = client.count_active_clients()
        print(f"Número de clientes ativos: {result.get('count', 0)}")
    
    elif args.action == 'list_active_clients':
        result = client.list_active_clients(args.limit)
        print(f"Clientes ativos (max {args.limit}):")
        print_json(result)
    
    elif args.action == 'client_info':
        if not args.id:
            parser.error("A ação 'client_info' requer o parâmetro --id")
        result = client.get_client_info(args.id)
        print(f"Informações do cliente {args.id}:")
        print_json(result)
    
    elif args.action == 'dossier_info':
        if not args.id:
            parser.error("A ação 'dossier_info' requer o parâmetro --id")
        result = client.get_dossier_info(args.id)
        print(f"Informações do dossier {args.id}:")
        print_json(result)
    
    elif args.action == 'list_recent_dossiers':
        result = client.list_recent_dossiers(args.limit)
        print(f"Dossiers recentes (max {args.limit}):")
        print_json(result)

if __name__ == "__main__":
    main()
