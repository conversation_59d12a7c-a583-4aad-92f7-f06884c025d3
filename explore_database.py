#!/usr/bin/env python3
"""
Script para explorar a estrutura do banco de dados PostgreSQL
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from dotenv import load_dotenv
import argparse

# Carrega variáveis de ambiente do arquivo .env
load_dotenv()

def connect_to_db():
    """Conecta ao banco de dados PostgreSQL"""
    try:
        # Obtém parâmetros de conexão das variáveis de ambiente
        host = os.getenv('UPMINER_HOST', 'localhost')
        port = os.getenv('UPMINER_PORT', '5432')
        database = os.getenv('UPMINER_DATABASE', 'upminerdossie')
        user = os.getenv('UPMINER_USERNAME', 'postgres')
        password = os.getenv('UPMINER_PASSWORD', '')
        
        # Conecta ao banco de dados
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password
        )
        
        return conn
    except Exception as e:
        print(f"Erro ao conectar ao banco de dados: {e}")
        sys.exit(1)

def list_schemas(conn):
    """Lista todos os schemas no banco de dados"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute("""
                SELECT schema_name 
                FROM information_schema.schemata
                WHERE schema_name NOT LIKE 'pg_%' 
                AND schema_name != 'information_schema'
                ORDER BY schema_name
            """)
            schemas = cursor.fetchall()
            
            print("\n=== Schemas disponíveis ===")
            if schemas:
                for schema in schemas:
                    print(f"- {schema['schema_name']}")
            else:
                print("Nenhum schema personalizado encontrado")
            
            return [schema['schema_name'] for schema in schemas]
    except Exception as e:
        print(f"Erro ao listar schemas: {e}")
        return []

def list_tables(conn, schema):
    """Lista todas as tabelas em um schema específico"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(f"""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = '{schema}'
                ORDER BY table_name
            """)
            tables = cursor.fetchall()
            
            print(f"\n=== Tabelas no schema '{schema}' ===")
            if tables:
                for table in tables:
                    print(f"- {table['table_name']}")
            else:
                print(f"Nenhuma tabela encontrada no schema '{schema}'")
            
            return [table['table_name'] for table in tables]
    except Exception as e:
        print(f"Erro ao listar tabelas do schema {schema}: {e}")
        return []

def describe_table(conn, schema, table):
    """Descreve a estrutura de uma tabela específica"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(f"""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_schema = '{schema}' AND table_name = '{table}'
                ORDER BY ordinal_position
            """)
            columns = cursor.fetchall()
            
            print(f"\n=== Estrutura da tabela '{schema}.{table}' ===")
            if columns:
                print(f"{'Coluna':<30} {'Tipo':<20} {'Nullable':<10}")
                print("-" * 60)
                for column in columns:
                    print(f"{column['column_name']:<30} {column['data_type']:<20} {column['is_nullable']:<10}")
            else:
                print(f"Nenhuma coluna encontrada para a tabela '{schema}.{table}'")
    except Exception as e:
        print(f"Erro ao descrever tabela {schema}.{table}: {e}")

def sample_data(conn, schema, table, limit=5):
    """Mostra uma amostra dos dados de uma tabela"""
    try:
        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            cursor.execute(f"""
                SELECT * FROM {schema}.{table} LIMIT {limit}
            """)
            rows = cursor.fetchall()
            
            print(f"\n=== Amostra de dados da tabela '{schema}.{table}' (max {limit} linhas) ===")
            if rows:
                # Imprime cabeçalhos
                headers = rows[0].keys()
                for header in headers:
                    print(f"{header:<20}", end="")
                print("\n" + "-" * (20 * len(headers)))
                
                # Imprime dados
                for row in rows:
                    for value in row.values():
                        print(f"{str(value)[:18]:<20}", end="")
                    print()
            else:
                print(f"Nenhum dado encontrado na tabela '{schema}.{table}'")
    except Exception as e:
        print(f"Erro ao buscar dados da tabela {schema}.{table}: {e}")

def main():
    parser = argparse.ArgumentParser(description='Explorar estrutura do banco de dados PostgreSQL')
    parser.add_argument('--schema', help='Nome do schema para explorar')
    parser.add_argument('--table', help='Nome da tabela para explorar')
    parser.add_argument('--sample', action='store_true', help='Mostrar amostra de dados da tabela')
    parser.add_argument('--limit', type=int, default=5, help='Limite de linhas para amostra (padrão: 5)')
    args = parser.parse_args()
    
    # Conecta ao banco de dados
    conn = connect_to_db()
    
    try:
        # Se nenhum schema for especificado, lista todos os schemas
        if not args.schema:
            schemas = list_schemas(conn)
            if schemas and len(schemas) > 0:
                print("\nDica: Para explorar um schema específico, execute:")
                print(f"python {sys.argv[0]} --schema NOME_DO_SCHEMA")
        else:
            # Se um schema for especificado, mas nenhuma tabela, lista todas as tabelas do schema
            if not args.table:
                tables = list_tables(conn, args.schema)
                if tables and len(tables) > 0:
                    print("\nDica: Para explorar uma tabela específica, execute:")
                    print(f"python {sys.argv[0]} --schema {args.schema} --table NOME_DA_TABELA")
            else:
                # Se schema e tabela forem especificados, descreve a tabela
                describe_table(conn, args.schema, args.table)
                
                # Se a flag --sample for especificada, mostra uma amostra dos dados
                if args.sample:
                    sample_data(conn, args.schema, args.table, args.limit)
                else:
                    print("\nDica: Para ver uma amostra dos dados, adicione a flag --sample:")
                    print(f"python {sys.argv[0]} --schema {args.schema} --table {args.table} --sample")
    finally:
        # Fecha a conexão
        conn.close()

if __name__ == "__main__":
    main()
